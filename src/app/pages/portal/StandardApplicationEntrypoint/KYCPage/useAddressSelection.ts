import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import type { AddressAutocompleteResult } from '../../../../api/types';
import { AddressComponentType } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { convertFullWidthToHalfWidth } from '../../../../utilities/convertFullWidthToHalfWidth';
import type { KYCJourneyValues } from './shared';

type UseAddressSelectionOptions = {
    fieldPrefix?: string;
};

const COUNTRIES_ADDRESS_WITHOUT_SPACE = ['JP'];

const formatPostalCodeForCountry = (postalCode: string, countryCode?: string): string => {
    if (countryCode === 'JP') {
        return postalCode.replace(/-/g, '');
    }

    return postalCode;
};

const buildFieldPath = (fieldPrefix: string, fieldName: string): string =>
    fieldPrefix ? `${fieldPrefix}.${fieldName}.value` : `${fieldName}.value`;

const useAddressSelection = (options: UseAddressSelectionOptions = {}) => {
    const { setFieldValue } = useFormikContext<KYCJourneyValues>();
    const company = useCompany();
    const { fieldPrefix = '' } = options;

    const handleAddressSelect = useCallback(
        (address: AddressAutocompleteResult) => {
            const { components } = address;

            const getComponent = (type: AddressComponentType) => components.find(comp => comp.type === type);

            const joinComponents = (componentTypes: AddressComponentType[]) => {
                const joinedValue = componentTypes
                    .map(type => getComponent(type)?.longName)
                    .filter(Boolean)
                    .join(COUNTRIES_ADDRESS_WITHOUT_SPACE.includes(company?.countryCode) ? '' : ' ');

                // Apply full-width to half-width conversion for Japan
                return company?.countryCode === 'JP' ? convertFullWidthToHalfWidth(joinedValue) : joinedValue;
            };

            const regionComponent = getComponent(AddressComponentType.Region);
            const districtComponent = getComponent(AddressComponentType.District);
            const postalCodeComponent = getComponent(AddressComponentType.Postcode);

            const cityValue = joinComponents([AddressComponentType.Place, AddressComponentType.Locality]);

            const addressValue = joinComponents([
                AddressComponentType.Neighborhood,
                AddressComponentType.Street,
                AddressComponentType.Block,
                AddressComponentType.Address,
                AddressComponentType.SecondaryAddress,
            ]);

            // Apply conversion to region and district for Japan
            const regionValue =
                company?.countryCode === 'JP'
                    ? convertFullWidthToHalfWidth(regionComponent?.longName || '')
                    : regionComponent?.longName || '';
            const districtValue =
                company?.countryCode === 'JP'
                    ? convertFullWidthToHalfWidth(districtComponent?.longName || '')
                    : districtComponent?.longName || '';

            setFieldValue(buildFieldPath(fieldPrefix, 'Region'), regionValue);
            setFieldValue(buildFieldPath(fieldPrefix, 'City'), cityValue);
            setFieldValue(buildFieldPath(fieldPrefix, 'District'), districtValue);
            setFieldValue(buildFieldPath(fieldPrefix, 'Address'), addressValue);

            if (postalCodeComponent) {
                const formattedPostalCode = formatPostalCodeForCountry(
                    postalCodeComponent.longName,
                    company?.countryCode
                );
                setFieldValue(buildFieldPath(fieldPrefix, 'PostalCode'), formattedPostalCode);
            } else {
                setFieldValue(buildFieldPath(fieldPrefix, 'PostalCode'), '');
            }
        },
        [fieldPrefix, setFieldValue, company?.countryCode]
    );

    return useMemo(
        () => ({
            handleAddressSelect,
        }),
        [handleAddressSelect]
    );
};

export default useAddressSelection;
