import { useLazyQuery } from '@apollo/client';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { GetAddressAutoCompleteDocument } from '../../../../api/queries/getAddressAutoComplete';
import {
    AddressAutofillType,
    AddressComponentType,
    AddressSearchType,
    type AddressAutocompleteResult,
} from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import useDebounce from '../../../../utilities/useDebounce';
import useRegionList from '../../../../utilities/useRegionList';
import type { KYCJourneyValues } from '../../StandardApplicationEntrypoint/KYCPage/shared';

const postalCodeRegex = /^[0-9]{6}$/;
const japanPostalCodeRegexWithoutDash = /^[0-9]{7}$/;
const southKoreaPostalCodeRegex = /^[0-9]{5}$/;

const COUNTRIES_ADDRESS_WITHOUT_SPACE = ['JP'];
const COUNTRIES_WITH_POSTAL_CODE_LENGTH = ['SG', 'JP', 'KR'];

type UsePostalCodeAddressAutocompleteOptions = {
    fieldPrefix?: string;
};

const isValidPostalCode = (postalCode: string, countryCode?: string): boolean => {
    if (!postalCode) {
        return false;
    }

    switch (countryCode) {
        case 'SG': // Singapore: 6 digits
            return postalCodeRegex.test(postalCode);

        case 'JP': // Japan: 7 digits
            return japanPostalCodeRegexWithoutDash.test(postalCode);

        case 'KR': // South Korea: 5 digits
            return southKoreaPostalCodeRegex.test(postalCode);

        default:
            // Generic validation: at least 2 characters
            return postalCode.length >= 2;
    }
};

const buildFieldPath = (fieldPrefix: string, fieldName: string): string =>
    fieldPrefix ? `${fieldPrefix}.${fieldName}.value` : `${fieldName}.value`;

const usePostalCodeAddressAutocomplete = (options: UsePostalCodeAddressAutocompleteOptions = {}) => {
    const { values, setFieldValue, setFieldTouched, validateField } = useFormikContext<KYCJourneyValues>();

    const { fieldPrefix } = options;

    const prevPostalCodeValueRef = useRef(values.customer?.fields?.PostalCode?.value);

    const company = useCompany(true);

    const [getAddressAutocomplete, { data, loading }] = useLazyQuery(GetAddressAutoCompleteDocument, {
        errorPolicy: 'all',
    });

    const results = useMemo(() => data?.getAddressAutocomplete || [], [data]);

    const { data: regionListData, loading: regionListLoading } = useRegionList();

    const regionsByCountry = useMemo(
        () =>
            !regionListLoading && company?.countryCode
                ? regionListData.filter(({ country }) => country === company.countryCode)
                : [],
        [regionListData, regionListLoading, company?.countryCode]
    );

    const searchAddresses = useCallback(
        (searchQuery: string) => {
            if (isValidPostalCode(searchQuery, company?.countryCode)) {
                getAddressAutocomplete({
                    variables: {
                        input: {
                            query: searchQuery,
                            limit: 1,
                            countryCode: company?.countryCode || undefined,
                            searchType: AddressSearchType.Postalcode,
                        },
                    },
                });
            }
        },
        [company?.countryCode, getAddressAutocomplete]
    );

    const debouncedSearch = useDebounce((searchQuery: string) => {
        if (searchQuery.length >= 2) {
            searchAddresses(searchQuery);
        }
    });

    const getRegionValue = useCallback(
        (regionCode: string) => {
            // Remove leading zeros, but keep '0' if the string is all zeros
            const normalizedRegionCode = regionCode.replace(/^0+/, '') || '0';
            const region = regionsByCountry.find(region => region.region === normalizedRegionCode);

            return region ? region.city : '';
        },
        [regionsByCountry]
    );

    const convertFullWidthToHalfWidth = useCallback((text: string): string => {
        if (!text) {
            return text;
        }

        return (
            text
                // Full-width numbers to half-width numbers
                .replace(/[０-９]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                // Full-width letters to half-width letters
                .replace(/[Ａ-Ｚａ-ｚ]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                // Full-width punctuation to half-width punctuation
                .replace(/\u3000/g, ' ') // Full-width space (U+3000)
                .replace(/（/g, '(')
                .replace(/）/g, ')')
                .replace(/－/g, '-')
                .replace(/．/g, '.')
                .replace(/，/g, ',')
                .replace(/：/g, ':')
                .replace(/；/g, ';')
                .replace(/！/g, '!')
                .replace(/？/g, '?')
                .replace(/／/g, '/')
                .replace(/＼/g, '\\')
        );
    }, []);

    const handleAddressAutocomplete = useCallback(
        async (address: AddressAutocompleteResult) => {
            const { components } = address;

            const getComponent = (type: AddressComponentType) => components.find(comp => comp.type === type);

            const joinComponents = (componentTypes: AddressComponentType[]) =>
                componentTypes
                    .map(type => getComponent(type)?.longName)
                    .filter(Boolean)
                    .join(COUNTRIES_ADDRESS_WITHOUT_SPACE.includes(company?.countryCode) ? '' : ' ');

            const regionValue =
                company?.countryCode === 'JP'
                    ? getRegionValue(getComponent(AddressComponentType.Region)?.shortName || '')
                    : getComponent(AddressComponentType.Region)?.longName || '';

            const cityValue =
                company?.countryCode === 'JP'
                    ? getComponent(AddressComponentType.Place)?.longName || ''
                    : joinComponents([AddressComponentType.Place, AddressComponentType.Locality]);

            const addressValue =
                company?.countryCode === 'JP'
                    ? convertFullWidthToHalfWidth(getComponent(AddressComponentType.Address)?.longName || '')
                    : joinComponents([
                          AddressComponentType.Neighborhood,
                          AddressComponentType.Street,
                          AddressComponentType.Block,
                          AddressComponentType.Address,
                          AddressComponentType.SecondaryAddress,
                      ]);

            const cityPath = buildFieldPath(fieldPrefix, 'City');
            const regionPath = buildFieldPath(fieldPrefix, 'Region');
            const addressPath = buildFieldPath(fieldPrefix, 'Address');

            await Promise.all([
                regionValue && setFieldValue(regionPath, regionValue),
                cityValue && setFieldValue(cityPath, cityValue),
                addressValue && setFieldValue(addressPath, addressValue),
            ]);

            await Promise.all([
                regionValue && setFieldTouched(regionPath, true, true),
                cityValue && setFieldTouched(cityPath, true, true),
                addressValue && setFieldTouched(addressPath, true, true),
            ]);

            await Promise.all([
                regionValue && validateField(regionPath),
                cityValue && validateField(cityPath),
                addressValue && validateField(addressPath),
            ]);
        },
        [
            company?.countryCode,
            convertFullWidthToHalfWidth,
            fieldPrefix,
            getRegionValue,
            setFieldTouched,
            setFieldValue,
            validateField,
        ]
    );

    useEffect(() => {
        if (!loading && results.length > 0) {
            handleAddressAutocomplete(results[0]);
        }
    }, [loading, results, handleAddressAutocomplete]);

    useEffect(() => {
        if (company?.addressAutofill === AddressAutofillType.PostalCode) {
            const currentPostalCode = values.customer?.fields?.PostalCode?.value;
            const prevPostalCode = prevPostalCodeValueRef.current;

            if (currentPostalCode !== prevPostalCode) {
                // Update the ref with the new value
                prevPostalCodeValueRef.current = currentPostalCode;

                if (currentPostalCode) {
                    if (COUNTRIES_WITH_POSTAL_CODE_LENGTH.includes(company?.countryCode)) {
                        searchAddresses(currentPostalCode);
                    } else {
                        debouncedSearch(currentPostalCode);
                    }
                }
            }
        }
    }, [
        company?.addressAutofill,
        company?.countryCode,
        debouncedSearch,
        searchAddresses,
        values.customer?.fields?.PostalCode,
    ]);
};

export default usePostalCodeAddressAutocomplete;
