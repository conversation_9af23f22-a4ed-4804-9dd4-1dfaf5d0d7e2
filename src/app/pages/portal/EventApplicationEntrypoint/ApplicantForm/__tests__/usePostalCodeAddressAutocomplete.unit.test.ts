/**
 * Unit tests for postal code address autocomplete functionality
 * Tests region code normalization and full-width to half-width conversion for Japan postal API
 */

describe('Postal Code Address Autocomplete - Japan Specific Features', () => {
    describe('Region Code Normalization', () => {
        // Test the normalization logic that was implemented in the fix
        const normalizeRegionCode = (code: string): string => code.replace(/^0+/, '') || '0';

        it('should remove leading zeros from region codes', () => {
            expect(normalizeRegionCode('01')).toBe('1');
            expect(normalizeRegionCode('08')).toBe('8');
            expect(normalizeRegionCode('023')).toBe('23');
            expect(normalizeRegionCode('001')).toBe('1');
        });

        it('should preserve non-zero-padded codes', () => {
            expect(normalizeRegionCode('1')).toBe('1');
            expect(normalizeRegionCode('8')).toBe('8');
            expect(normalizeRegionCode('13')).toBe('13');
            expect(normalizeRegionCode('23')).toBe('23');
        });

        it('should handle edge cases correctly', () => {
            expect(normalizeRegionCode('0')).toBe('0');
            expect(normalizeRegionCode('00')).toBe('0');
            expect(normalizeRegionCode('000')).toBe('0');
            expect(normalizeRegionCode('10')).toBe('10');
            expect(normalizeRegionCode('010')).toBe('10');
        });
    });

    describe('Full-Width to Half-Width Conversion', () => {
        // Test the conversion logic for Japanese full-width characters
        const convertFullWidthToHalfWidth = (text: string): string => {
            if (!text) {
                return text;
            }

            return (
                text
                    // Full-width numbers to half-width numbers
                    .replace(/[０-９]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                    // Full-width letters to half-width letters
                    .replace(/[Ａ-Ｚａ-ｚ]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                    // Full-width punctuation to half-width punctuation
                    .replace(/（/g, '(') // Full-width opening parenthesis
                    .replace(/）/g, ')') // Full-width closing parenthesis
                    .replace(/－/g, '-') // Full-width hyphen
                    .replace(/\u3000/g, ' ') // Full-width space (U+3000)
                    .replace(/．/g, '.') // Full-width period
                    .replace(/，/g, ',') // Full-width comma
                    .replace(/：/g, ':') // Full-width colon
                    .replace(/；/g, ';') // Full-width semicolon
                    .replace(/！/g, '!') // Full-width exclamation mark
                    .replace(/？/g, '?') // Full-width question mark
                    .replace(/／/g, '/') // Full-width slash
                    .replace(/＼/g, '\\')
            ); // Full-width backslash
        };

        it('should convert full-width numbers to half-width numbers', () => {
            expect(convertFullWidthToHalfWidth('０１２３４５６７８９')).toBe('0123456789');
            expect(convertFullWidthToHalfWidth('２９')).toBe('29');
            expect(convertFullWidthToHalfWidth('１２３')).toBe('123');
            expect(convertFullWidthToHalfWidth('５０１')).toBe('501');
        });

        it('should convert full-width parentheses to half-width parentheses', () => {
            expect(convertFullWidthToHalfWidth('（２９階）')).toBe('(29階)');
            expect(convertFullWidthToHalfWidth('（１階）')).toBe('(1階)');
            expect(convertFullWidthToHalfWidth('（地下１階）')).toBe('(地下1階)');
        });

        it('should convert full-width hyphens to half-width hyphens', () => {
            expect(convertFullWidthToHalfWidth('１－２－３')).toBe('1-2-3');
            expect(convertFullWidthToHalfWidth('２－１－１')).toBe('2-1-1');
            expect(convertFullWidthToHalfWidth('３－１５－３３')).toBe('3-15-33');
        });

        it('should convert full-width letters to half-width letters', () => {
            expect(convertFullWidthToHalfWidth('ＡＢＣＤＥＦＧ')).toBe('ABCDEFG');
            expect(convertFullWidthToHalfWidth('ａｂｃｄｅｆｇ')).toBe('abcdefg');
            expect(convertFullWidthToHalfWidth('ビルＡ棟')).toBe('ビルA棟');
            expect(convertFullWidthToHalfWidth('Ｆ階')).toBe('F階');
        });

        it('should convert full-width punctuation to half-width punctuation', () => {
            expect(convertFullWidthToHalfWidth('住所．番地')).toBe('住所.番地');
            expect(convertFullWidthToHalfWidth('東京，大阪')).toBe('東京,大阪');
            expect(convertFullWidthToHalfWidth('時間：９時')).toBe('時間:9時');
            expect(convertFullWidthToHalfWidth('注意；重要')).toBe('注意;重要');
            expect(convertFullWidthToHalfWidth('緊急！連絡')).toBe('緊急!連絡');
            expect(convertFullWidthToHalfWidth('質問？回答')).toBe('質問?回答');
            expect(convertFullWidthToHalfWidth('パス／ディレクトリ')).toBe('パス/ディレクトリ');
            expect(convertFullWidthToHalfWidth('バックスラッシュ＼テスト')).toBe('バックスラッシュ\\テスト');
        });

        it('should convert full-width spaces to half-width spaces', () => {
            // Using Unicode escape for full-width space to avoid linting issues
            expect(convertFullWidthToHalfWidth('東京\u3000都')).toBe('東京 都');
            expect(convertFullWidthToHalfWidth('渋谷\u3000区')).toBe('渋谷 区');
        });

        it('should handle mixed content correctly', () => {
            expect(convertFullWidthToHalfWidth('１２３番地')).toBe('123番地');
            expect(convertFullWidthToHalfWidth('アパート５０１号室')).toBe('アパート501号室');
            expect(convertFullWidthToHalfWidth('マンション（２９階）')).toBe('マンション(29階)');
            expect(convertFullWidthToHalfWidth('ビル１０Ｆ')).toBe('ビル10F');
        });

        it('should preserve half-width characters and other content', () => {
            expect(convertFullWidthToHalfWidth('123番地')).toBe('123番地');
            expect(convertFullWidthToHalfWidth('(29階)')).toBe('(29階)');
            expect(convertFullWidthToHalfWidth('アパート501号室')).toBe('アパート501号室');
            expect(convertFullWidthToHalfWidth('普通の文字列')).toBe('普通の文字列');
        });

        it('should handle empty and null inputs', () => {
            expect(convertFullWidthToHalfWidth('')).toBe('');
            expect(convertFullWidthToHalfWidth(null as any)).toBe(null);
            expect(convertFullWidthToHalfWidth(undefined as any)).toBe(undefined);
        });

        it('should handle complex real-world address examples', () => {
            // Real-world examples that might come from Japan postal API
            expect(convertFullWidthToHalfWidth('東京都渋谷区（２９階）')).toBe('東京都渋谷区(29階)');
            expect(convertFullWidthToHalfWidth('大阪市中央区１－２－３')).toBe('大阪市中央区1-2-3');
            expect(convertFullWidthToHalfWidth('横浜市西区みなとみらい２－１－１')).toBe('横浜市西区みなとみらい2-1-1');
            expect(convertFullWidthToHalfWidth('名古屋市中区栄３－１５－３３（１８階）')).toBe(
                '名古屋市中区栄3-15-33(18階)'
            );
            // Additional complex examples with various full-width characters
            expect(convertFullWidthToHalfWidth('ビルＡ棟\u3000２０１号室')).toBe('ビルA棟 201号室');
            expect(convertFullWidthToHalfWidth('住所：東京都．渋谷区１－１－１')).toBe('住所:東京都.渋谷区1-1-1');
            expect(convertFullWidthToHalfWidth('緊急連絡先！０３－１２３４－５６７８')).toBe('緊急連絡先!03-1234-5678');
            expect(convertFullWidthToHalfWidth('パス／ディレクトリ＼ファイル')).toBe('パス/ディレクトリ\\ファイル');
        });
    });

    describe('Integration Scenarios', () => {
        const mockRegionsByCountry = [
            { country: 'JP', region: '1', city: 'Hokkaido' },
            { country: 'JP', region: '8', city: 'Ibaragi' },
            { country: 'JP', region: '13', city: 'Tokyo' },
            { country: 'JP', region: '23', city: 'Aichi' },
        ];

        const normalizeRegionCode = (code: string): string => code.replace(/^0+/, '') || '0';
        const convertFullWidthToHalfWidth = (text: string): string => {
            if (!text) {
                return text;
            }

            return (
                text
                    // Full-width numbers to half-width numbers
                    .replace(/[０-９]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                    // Full-width letters to half-width letters
                    .replace(/[Ａ-Ｚａ-ｚ]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
                    // Full-width punctuation to half-width punctuation
                    .replace(/（/g, '(') // Full-width opening parenthesis
                    .replace(/）/g, ')') // Full-width closing parenthesis
                    .replace(/－/g, '-') // Full-width hyphen
                    .replace(/\u3000/g, ' ') // Full-width space (U+3000)
                    .replace(/．/g, '.') // Full-width period
                    .replace(/，/g, ',') // Full-width comma
                    .replace(/：/g, ':') // Full-width colon
                    .replace(/；/g, ';') // Full-width semicolon
                    .replace(/！/g, '!') // Full-width exclamation mark
                    .replace(/？/g, '?') // Full-width question mark
                    .replace(/／/g, '/') // Full-width slash
                    .replace(/＼/g, '\\')
            ); // Full-width backslash
        };

        it('should handle complete Japan postal API response processing', () => {
            // Simulate a complete API response with both region code and address normalization
            const mockApiResponse = {
                components: [
                    {
                        type: 'REGION',
                        longName: 'Tokyo',
                        shortName: '013', // Zero-padded region code
                    },
                    {
                        type: 'PLACE',
                        longName: '渋谷区（２９階）', // Full-width characters in city
                    },
                    {
                        type: 'ADDRESS',
                        longName: '１－２－３番地（地下１階）', // Full-width characters in address
                    },
                ],
            };

            // Process region code
            const regionCode = mockApiResponse.components[0].shortName;
            const normalizedRegionCode = normalizeRegionCode(regionCode);
            const matchedRegion = mockRegionsByCountry.find(region => region.region === normalizedRegionCode);

            // Process city and address values (both should use conversion for Japan)
            const cityValue = convertFullWidthToHalfWidth(mockApiResponse.components[1].longName);
            const addressValue = convertFullWidthToHalfWidth(mockApiResponse.components[2].longName);

            // Verify results
            expect(normalizedRegionCode).toBe('13');
            expect(matchedRegion?.city).toBe('Tokyo');
            expect(cityValue).toBe('渋谷区(29階)');
            expect(addressValue).toBe('1-2-3番地(地下1階)');
        });

        it('should apply conversion for both PostalCode and Partial autofill types', () => {
            // This test verifies that the conversion works regardless of autofill type
            // since the handleAddressAutocomplete function is used for both scenarios

            const mockAddressComponents = [
                {
                    type: 'PLACE',
                    longName: 'ビルＡ棟\u3000２０１号室', // Full-width characters in city
                },
                {
                    type: 'ADDRESS',
                    longName: '住所：東京都．渋谷区１－１－１', // Full-width characters in address
                },
            ];

            // Process both city and address values
            const cityValue = convertFullWidthToHalfWidth(mockAddressComponents[0].longName);
            const addressValue = convertFullWidthToHalfWidth(mockAddressComponents[1].longName);

            // Verify both are converted properly
            expect(cityValue).toBe('ビルA棟 201号室');
            expect(addressValue).toBe('住所:東京都.渋谷区1-1-1');
        });
    });
});
