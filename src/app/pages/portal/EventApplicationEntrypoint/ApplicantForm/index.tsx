import { Formik, FormikHelpers, useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { usePrefetchAgreementsForEventQuery } from '../../../../api/queries/prefetchAgreementsForEvent';
import { usePrefetchKycFieldsForEventQuery } from '../../../../api/queries/prefetchKYCFieldsForEvent';
import {
    AssetCondition,
    CompanyTheme,
    CustomerKind,
    LocalCustomerManagementModule,
    PorscheIdData,
} from '../../../../api/types';
import getKYCDataFromPorscheId from '../../../../components/PorscheID/getKYCDataFromPorscheId';
import { getPorscheIDConfigByEvent } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { SelectedCapValues, ViewState } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import { useHeaderContext } from '../../../../layouts/HeaderContextManager';
import { useRuntimeConfig } from '../../../../runtimeConfig';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, prepareKYCFieldPayload } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import useCheckCapIntegrationIsOn from '../../../../utilities/useCheckCapIntegrationIsOn';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import useValidator from '../../../../utilities/useValidator';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import {
    getAgreementValues,
    mergeAgreementValues,
} from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import AddressAutocompleteContextManager from '../../../shared/JourneyPage/AddressAutocomplete/ContextManager';
import { SearchCapCustomerContextManager } from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/ContextManager';
import getKYCDataFromCap, {
    getCurrentVehicleFromCap,
} from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/getKYCDataFromCap';
// eslint-disable-next-line max-len
import useCapSearchCustomerAction from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/useCapSearchCustomerAction';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import { JourneyStage } from '../../FinderApplicationPublicAccessEntrypoint/types';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import useAddressSelection from '../../StandardApplicationEntrypoint/KYCPage/useAddressSelection';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import { useEventJourneyKycAndAgreementContext } from '../Entrypoint/EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import type { EventApplicationState } from '../Journey/shared';
import useSubmitDraft from '../useSubmitDraft';
import ThemedInner from './ThemedInner';
import { InnerType } from './ThemedInner/shared/types';
import { ApplicantFormValues } from './shared';
import useEventApplicantValidator from './useEventApplicantValidator';
import usePostalCodeAddressAutocomplete from './usePostalCodeAddressAutocomplete';

declare global {
    interface Window {
        ttq;
    }
}

export type Agreements = ApplicationAgreementDataFragment[];

export type InnerFormProps = {
    state?: State<EventApplicationState>;
    dispatch?: Dispatch<Action<EventApplicationState>>;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
} & Partial<UploadDocumentProp>;

const InnerForm = ({ state, dispatch, uploadDocument, removeDocument, kycExtraSettings }: InnerFormProps) => {
    const navigate = useNavigate();
    const company = useCompany();
    const FORM_NAME = 'applicantForm';

    const [isPorscheIDFetchLoading, setIsPorscheIDFetchLoading] = useState<boolean>(false);

    const runtime = useRuntimeConfig();
    const { t } = useTranslation([
        'eventApplicantForm',

        // Load it first, so that when there is test drive
        // It won't trigger portal loading
        'appointmentPage',
    ]);
    const translatedString = useTranslatedString();

    const { event, eventModule, endpoint, getDraftJourneyStages, showResetKYCButton } = useEventJourneySetupContext();
    const { kycState, setKycState, agreementState, setAgreementState, customerKind, isCorporateCustomer } =
        useEventJourneyKycAndAgreementContext();

    const submitDraft = useSubmitDraft(event.id, endpoint, {
        isPrivateAccess: event.privateAccess,
        userIds: event.userIds,
    });

    const { setHeaderVariant } = useHeaderContext();
    const { BackButton, StandardLayout, theme } = useThemeComponents();

    const {
        initialValues,
        isSubmitting,
        values,
        setFieldTouched,
        resetForm,
        setFieldValue,
        validateForm,
        submitForm,
        setValues,
    } = useFormikContext<ApplicantFormValues>();

    // Only capture the first loading for both kyc and agreement
    // This to prevent flicker on the front side
    const [isKycLoaded, setKycLoaded] = useState(false);
    const [isAgreementLoaded, setAgreementLoaded] = useState(false);
    const [withMyInfo, setWithMyInfo] = useState(false);
    const [capModalHasError, setCapModalHasError] = useState(false);

    useEffect(() => {
        setHeaderVariant(event.hasCustomiseBanner && event.banner ? 'transparent' : 'absolute');

        return () => setHeaderVariant('absolute');
    }, [event.banner, event.hasCustomiseBanner, setHeaderVariant]);

    // Fetch KYC
    usePrefetchKycFieldsForEventQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            urlSlug: event.urlSlug,
            configuration: {
                assetCondition: AssetCondition.New,
                testDrive: values.configuration.testDrive,
                tradeIn: values.configuration.tradeIn,
                visitAppointment: values.configuration.visitAppointment,
            },
            dealerId: values.dealerId,
            eventModuleId: eventModule.id,
        },
        // Skip when kyc step is for guarantor
        skip: customerKind === CustomerKind.Guarantor,
        onCompleted: data => {
            const applicantKyc = getApplicantKyc(data.applicantKYC ?? []);
            const corporateKyc = getApplicantKyc(data.corporateKYC ?? []);
            const guarantorKyc = getApplicantKyc(data.guarantorKYC ?? []);
            const activeKyc = isCorporateCustomer ? corporateKyc : applicantKyc;

            const newCustomerFields = getInitialValues(prepareKYCFieldPayload(values.customer.fields, true), activeKyc);

            // Set outer state, so it align with validator
            setKycState({
                applicant: applicantKyc,
                corporate: corporateKyc,
                active: activeKyc,
                guarantor: guarantorKyc,
            });

            // Set formik value
            if (!isKycLoaded) {
                setFieldValue('customer.fields', newCustomerFields);
            }

            // Make sure it's shown loading portal once
            setKycLoaded(oldLoaded => (!oldLoaded ? true : oldLoaded));
        },
    });

    // Fetch Agreements
    usePrefetchAgreementsForEventQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            urlSlug: event.urlSlug,
            configuration: {
                assetCondition: AssetCondition.New,
                testDrive: values.configuration.testDrive,
                tradeIn: values.configuration.tradeIn,
                visitAppointment: values.configuration.visitAppointment,
            },
            dealerId: values.dealerId,
            eventModuleId: eventModule.id,
        },

        // Skip when kyc step is for guarantor
        skip: customerKind === CustomerKind.Guarantor,
        onCompleted: data => {
            const activeAgreements: ApplicationAgreementDataFragment[] = isCorporateCustomer
                ? getApplicantAgreements(data.corporateAgreements)
                : getApplicantAgreements(data.applicantAgreements);

            // Get remaining agreements that align with active one
            const newAgreementValues = getAgreementValues(mergeAgreementValues(activeAgreements, values.agreements));

            // Set outer state, so it align with validator
            setAgreementState({
                values: newAgreementValues,
                data: activeAgreements,
            });

            // Set formik value
            if (!isAgreementLoaded) {
                setFieldValue('agreements', newAgreementValues);
            }

            // Make sure it's shown loading portal once
            setAgreementLoaded(oldLoaded => (!oldLoaded ? true : oldLoaded));
        },
    });

    // Touch customer fields on fields change
    useEffect(() => {
        setFieldTouched('customer.fields', true);
    }, [kycState.active, setFieldTouched]);

    // Touch agreements fields on fields change
    useEffect(() => {
        setFieldTouched('agreements', true);
    }, [agreementState.data, setFieldTouched]);

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycState.active) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycState.active, resetForm, values.agreements]);

    const backIcon = state ? null : <BackButton type="link"> {t('eventApplicantForm:actions.back')}</BackButton>;

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();

        const hasTitTok = !isNil(runtime.router.tikTok?.id);

        if (hasTitTok) {
            window.ttq.track('SubmitForm');
        }
    }, [submitForm, validateForm]);

    const withTradeIn = useMemo(() => values?.configuration.tradeIn, [values?.configuration.tradeIn]);

    const onCapValuesChanged = useCallback(
        (capValues: SelectedCapValues) => {
            setFieldValue('isCustomerSearchPerformed', true);

            switch (capValues.selectedValue) {
                case ViewState.BusinessPartner: {
                    const { selectedBusinessPartner } = capValues;

                    if (selectedBusinessPartner.currentVehicle && values.tradeInVehicle?.length) {
                        setFieldValue(
                            'tradeInVehicle.0',
                            getCurrentVehicleFromCap(selectedBusinessPartner, values.tradeInVehicle)
                        );
                    }

                    setFieldValue(
                        'customer.fields',
                        getKYCDataFromCap(selectedBusinessPartner, kycState.applicant, values.customer)
                    );
                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        salesPersonId: selectedBusinessPartner?.responsibleSalesPersonId,
                    });

                    break;
                }

                default: {
                    const { selectedBusinessPartner, selectedLead } = capValues;

                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        salesPersonId: selectedBusinessPartner?.responsibleSalesPersonId,
                        leadGuid: selectedLead?.leadGuid,
                        leadId: selectedLead?.leadId,
                    });
                }
            }
        },
        [setFieldValue, kycState.applicant, values.customer, values.tradeInVehicle]
    );

    const onCapSearchErrorConfirmed = useCallback(() => {
        setCapModalHasError(true);
    }, []);

    const onCustomerSearchPerformed = useCallback(
        (isPerformed: boolean) => {
            setFieldValue('isCustomerSearchPerformed', isPerformed);
        },
        [setFieldValue]
    );

    const user = useAccount(true);
    const showCommentsToBank = user && state?.application?.bank?.showCommentsField;

    const capModuleId = event.module.__typename === 'EventApplicationModule' ? event.module.capModuleId : null;
    const isCapEnabled = useCheckCapIntegrationIsOn({ event, dealerId: values.dealerId });

    const { showSearchCapCustomerButton, immediateOpenCapCustomerSearch } = useCapSearchCustomerAction({
        kycHasCompleted: state?.application?.draftFlow?.isApplicantKYCCompleted,
        requireLogin: event.privateAccess,
        isCapEnabled,
        isOptional: event.isSearchCapCustomerOptional,
        hasLeadGuid: !!values.capValues?.leadGuid,
        capModalHasError,
    });

    const { porscheIdIntegrationEnabled, isPorscheIdLoginMandatory } = useMemo(
        () => getPorscheIDConfigByEvent(event),
        [event]
    );

    const currentStage = useMemo(() => {
        if (state?.stage) {
            return state.stage;
        }

        return isPorscheIdLoginMandatory ? JourneyStage.PorscheIdLoginRegister : JourneyStage.ApplicantKYC;
    }, [isPorscheIdLoginMandatory, state?.stage]);

    const onPorscheIDCustomerFetched = useCallback(
        (porscheIdData: PorscheIdData) => {
            getKYCDataFromPorscheId(porscheIdData, setValues, withTradeIn);

            if (dispatch && state?.stage === JourneyStage.PorscheIdLoginRegister) {
                dispatch({
                    type: 'goTo',
                    stage: JourneyStage.ApplicantKYC,
                });
            }
        },

        [dispatch, setValues, state, withTradeIn]
    );

    const submitDraftWithPorscheId = useCallback(async () => {
        const result = await submitDraft(values, CustomerKind.Local, true);

        return result?.application?.id;
    }, [submitDraft, values]);

    useEffect(() => {
        if (
            isPorscheIdLoginMandatory &&
            values.customerCiamId &&
            state?.stage === JourneyStage.PorscheIdLoginRegister
        ) {
            dispatch({
                type: 'goTo',
                stage: JourneyStage.ApplicantKYC,
            });
        }
    }, [dispatch, isPorscheIdLoginMandatory, state?.stage, values.customerCiamId]);

    const dealer = useMemo(
        () => event.dealers.find(dealer => dealer.id === values.dealerId),
        [event.dealers, values.dealerId]
    );

    const { handleAddressSelect } = useAddressSelection({
        fieldPrefix: 'customer.fields',
    });

    usePostalCodeAddressAutocomplete({
        fieldPrefix: 'customer.fields',
    });

    const innerProps: InnerType = {
        capIntegration: {
            showSearchCapCustomerButton,
            immediateOpenCapCustomerSearch,
        },
        currentStage,
        dealer,
        dispatch,
        formName: FORM_NAME,
        kycExtraSettings,
        porscheIdIntegration: {
            isPorscheIDFetchLoading,
            isPorscheIdLoginMandatory,
            porscheIdIntegrationEnabled,
            onPorscheIDCustomerFetched,
            setIsPorscheIDFetchLoading,
            submitDraftWithPorscheId,
        },
        myInfoIntegration: {
            withMyInfo,
            setWithMyInfo,
        },
        removeDocument,
        resetFormHandler,
        showCommentsToBank,
        stages: state?.stages || getDraftJourneyStages(values.dealerId),
        state,
        uploadDocument,
    };

    // Only show loading for the first one
    if (!isKycLoaded || !isAgreementLoaded) {
        return <PortalLoadingElement />;
    }

    return (
        <AddressAutocompleteContextManager onAddressSelected={handleAddressSelect}>
            <SearchCapCustomerContextManager
                applicationModuleId={event.module.id}
                applicationRequireLogin={event.privateAccess}
                capModuleId={capModuleId}
                countryCode={company?.countryCode}
                dealerId={values.dealerId}
                eventId={event.id}
                kycHasCompleted={state?.application?.draftFlow?.isApplicantKYCCompleted}
                lead={state?.application.lead}
                onCapModalErrorConfirmed={onCapSearchErrorConfirmed}
                onCapValuesChanged={onCapValuesChanged}
                onCustomerSearchPerformed={onCustomerSearchPerformed}
            >
                <StandardLayout
                    {...(!event?.hasCustomiseBanner && {
                        backIcon,
                        onBack: event.privateAccess ? () => navigate('..') : null,
                        title: translatedString(event.name),
                    })}
                    hasFooterBar
                >
                    <ThemedInner {...innerProps} />
                    <StyledJourneyToolbar>
                        {showResetKYCButton &&
                            !withMyInfo &&
                            (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                                <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                            )}
                        <NextButton
                            data-cy="leadGenFormButton"
                            disabled={isSubmitting || (isPorscheIdLoginMandatory && !values.customerCiamId)}
                            form={FORM_NAME}
                            onSubmit={onSubmit}
                        />
                    </StyledJourneyToolbar>
                    {isPorscheIDFetchLoading && <Backdrop />}
                </StandardLayout>
            </SearchCapCustomerContextManager>
        </AddressAutocompleteContextManager>
    );
};

export type ApplicantFormProps = {
    initialValues: ApplicantFormValues;
    onSubmit: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    state?: State<EventApplicationState>;
    dispatch?: Dispatch<Action<EventApplicationState>>;
} & Partial<UploadDocumentProp>;

const ApplicantForm = ({ onSubmit, initialValues, ...rest }: ApplicantFormProps) => {
    const { agreementState, kycState, prefill } = useEventJourneyKycAndAgreementContext();
    const { event, isSubModelRequired } = useEventJourneySetupContext();

    const kycExtraSettings = useMemo(() => event.kycExtraSettings, [event.kycExtraSettings]);
    const applicantValidations = useEventApplicantValidator(
        agreementState.data,
        kycState.active,
        kycExtraSettings,
        isSubModelRequired
    );

    const validate = useValidator(applicantValidations, { prefill });

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            <InnerForm kycExtraSettings={kycExtraSettings} {...rest} />
        </Formik>
    );
};

export default ApplicantForm;
