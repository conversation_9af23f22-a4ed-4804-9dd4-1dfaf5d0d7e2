import { useLazyQuery } from '@apollo/client';
import { PTextFieldWrapper } from '@porsche-design-system/components-react';
import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { GetAddressAutoCompleteDocument } from '../../../../api/queries/getAddressAutoComplete';
import type { AddressAutocompleteResult } from '../../../../api/types';
import LoadingElement from '../../../../components/LoadingElement';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { convertFullWidthToHalfWidth } from '../../../../utilities/convertFullWidthToHalfWidth';
import useDebounce from '../../../../utilities/useDebounce';

const ModalContainer = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
`;

const SearchContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
`;

const ResultsContainer = styled.div`
    position: absolute;
    padding: 6px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    top: calc(100% + 4px);
    left: 0;
    right: 0;
    overflow-y: auto;
    max-height: 300px;
    border: 1px solid #d8d8db;
    border-radius: 8px;
    background: white;
    z-index: 1000;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
`;

const AddressResultItem = styled.div<{ isSelected?: boolean }>`
    padding: 8px 12px;
    cursor: pointer;
    background-color: ${props => (props.isSelected ? '#f3f4f6' : 'white')};
    transition: background-color 0.2s ease;
    border-radius: 4px;

    &:hover {
        background-color: rgba(148, 149, 152, 0.18);
    }
`;

type AddressAutocompleteModalProps = {
    open: boolean;
    onClose: () => void;
    onAddressSelect: (address: AddressAutocompleteResult) => void;
};

const AddressAutocompleteModal: React.FC<AddressAutocompleteModalProps> = ({ open, onClose, onAddressSelect }) => {
    const { t } = useTranslation('customerDetails');
    const { Modal, Button } = useThemeComponents();
    const company = useCompany();
    const inputRef = useRef<HTMLInputElement>(null);
    const [query, setQuery] = useState('');
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const [selectedAddress, setSelectedAddress] = useState<AddressAutocompleteResult | null>(null);

    const [getAddressAutocomplete, { data, loading }] = useLazyQuery(GetAddressAutoCompleteDocument, {
        errorPolicy: 'all',
    });

    const results = useMemo(() => data?.getAddressAutocomplete || [], [data]);

    const debouncedSearch = useDebounce((searchQuery: string) => {
        if (searchQuery.length >= 2) {
            getAddressAutocomplete({
                variables: {
                    input: {
                        query: searchQuery,
                        limit: 5,
                        countryCode: company?.countryCode || undefined,
                    },
                },
            });
        }
    });

    const handleInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const { value } = e.target;
            setQuery(value);
            setSelectedIndex(-1);
            setSelectedAddress(null);
            debouncedSearch(value);

            if (!value) {
                getAddressAutocomplete({
                    variables: { input: { query: '', limit: 5, countryCode: company?.countryCode || undefined } },
                });
            }
        },
        [company?.countryCode, debouncedSearch, getAddressAutocomplete]
    );

    const handleResultClick = useCallback((address: AddressAutocompleteResult, index: number) => {
        setSelectedIndex(index);
        setSelectedAddress(address);
        setQuery(address.address);
    }, []);

    const handleSubmit = useCallback(() => {
        if (!selectedAddress) {
            return;
        }

        onAddressSelect(selectedAddress);
        onClose();
    }, [selectedAddress, onAddressSelect, onClose]);

    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : prev));
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedIndex >= 0 && results[selectedIndex]) {
                    const address = results[selectedIndex];
                    setSelectedAddress(address);
                    setQuery(address.address);
                    handleSubmit();
                }
            }
        },
        [results, selectedIndex, handleSubmit]
    );

    useEffect(() => {
        if (open && inputRef.current) {
            const timer = setTimeout(() => {
                inputRef.current?.focus();
            }, 100);

            return () => clearTimeout(timer);
        }

        return undefined;
    }, [open]);

    const renderResults = () => {
        if (loading) {
            return <LoadingElement />;
        }

        return results.map((result: AddressAutocompleteResult, index: number) => {
            // Apply full-width to half-width conversion for Japan
            const displayAddress =
                company?.countryCode === 'JP' ? convertFullWidthToHalfWidth(result.address) : result.address;

            return (
                <AddressResultItem
                    key={result.id}
                    isSelected={index === selectedIndex}
                    onClick={() => handleResultClick(result, index)}
                >
                    {displayAddress}
                </AddressResultItem>
            );
        });
    };

    const footerButtons = useMemo(
        () => [
            <Button key="submit-confirm" disabled={!selectedAddress} onClick={handleSubmit} type="primary" block>
                {t('customerDetails:buttons.confirm')}
            </Button>,
            <Button key="cancel" onClick={onClose} type="default" block>
                {t('customerDetails:buttons.cancel')}
            </Button>,
        ],
        [Button, selectedAddress, handleSubmit, t, onClose]
    );

    return (
        <Modal
            closable={false}
            closeIcon={null}
            footer={footerButtons}
            onCancel={onClose}
            open={open}
            title={t('customerDetails:addressSearch.title')}
            width={600}
            destroyOnClose
        >
            <ModalContainer>
                <SearchContainer>
                    <form onSubmit={e => e.preventDefault()}>
                        <PTextFieldWrapper label={t('customerDetails:addressSearch.label')} state="none" submitButton>
                            <input
                                ref={inputRef}
                                onChange={handleInputChange}
                                onKeyDown={handleKeyDown}
                                placeholder={t('customerDetails:addressSearch.placeholder')}
                                type="search"
                                value={query}
                            />
                        </PTextFieldWrapper>
                    </form>
                    {(results.length > 0 || loading) && !selectedAddress && (
                        <ResultsContainer>{renderResults()}</ResultsContainer>
                    )}
                </SearchContainer>
            </ModalContainer>
        </Modal>
    );
};

export default AddressAutocompleteModal;
