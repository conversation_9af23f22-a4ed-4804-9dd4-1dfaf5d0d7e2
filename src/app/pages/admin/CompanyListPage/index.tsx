import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Space } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../utilities/hasPermissions';
import useGoTo from '../../../utilities/useGoTo';
import CompanyList from './CompanyList';
import { useImportCompanyModal } from './ImportCompanyModal';

const CompanyListPage = () => {
    const { t } = useTranslation('companyList');

    const goToNewCompanyPage = useGoTo('/admin/companies/add');
    const { permissions: accountPermissions, hasRootPermission } = useAccount();
    const importCompanyModal = useImportCompanyModal();

    const handleImportSuccess = useCallback(() => {
        window.location.reload();
    }, []);

    const extra = useMemo(() => {
        const hasCreatePermission = hasPermissions(accountPermissions, [permissionKind.createCompany]);
        const hasImportPermission = hasRootPermission;

        if (!hasCreatePermission && !hasImportPermission) {
            return null;
        }

        return (
            <Space>
                {hasCreatePermission && (
                    <Button icon={<PlusOutlined />} onClick={goToNewCompanyPage} type="primary">
                        {t('companyList:actions.newCompany')}
                    </Button>
                )}
                {hasImportPermission && (
                    <Button icon={<UploadOutlined />} onClick={importCompanyModal.open} type="primary">
                        {t('companyList:actions.importCompany')}
                    </Button>
                )}
            </Space>
        );
    }, [accountPermissions, hasRootPermission, goToNewCompanyPage, importCompanyModal.open, t]);

    return (
        <ConsolePageWithHeader extra={extra} title={t('companyList:title')}>
            <CompanyList />
            {importCompanyModal.render({
                onImportSuccess: handleImportSuccess,
            })}
        </ConsolePageWithHeader>
    );
};

export default CompanyListPage;
