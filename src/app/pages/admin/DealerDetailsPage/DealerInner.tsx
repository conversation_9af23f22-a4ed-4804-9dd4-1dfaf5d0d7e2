import { useApolloClient } from '@apollo/client';
import { Button, Grid, Tabs, message } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import * as permissionKind from '../../../../shared/permissions';
import { DeleteDealerDocument, DeleteDealerMutation, DeleteDealerMutationVariables } from '../../../api';
import { DealerWithPermissionsFragmentFragment } from '../../../api/fragments/DealerWithPermissionsFragment';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import getApolloErrors from '../../../utilities/getApolloErrors';
import hasPermissions from '../../../utilities/hasPermissions';
import { renderDealerType } from '../DealerListPage/helpers';
import DealerModuleManagement from './DealerModuleManagement';
import DealerSettingForm from './DealerSettingForm';
import { useDeleteDealerModal } from './DeleteDealerModal';
import useDealerModules from './useDealerModules';

const StyledTitle = styled.div`
    display: flex;
    gap: 16px;
`;

export type DealerProps = {
    dealer: DealerWithPermissionsFragmentFragment;
};

const DealerInner = ({ dealer }: DealerProps) => {
    const { t } = useTranslation(['dealerForm', 'dealerList']);
    const navigate = useNavigate();
    const apolloClient = useApolloClient();

    const [activeTab, setActiveTab] = useState('settings');
    const modules = useDealerModules(dealer);

    const tabPane = useMemo(
        () => [
            <Tabs.TabPane key="settings" tab={t('dealerForm:mainDetailsTab')} />,
            ...modules.map(module => <Tabs.TabPane key={module.id} tab={module.displayName} />),
        ],
        [modules, t]
    );
    const screens = Grid.useBreakpoint();

    const render = () => {
        switch (activeTab) {
            case 'settings':
                return <DealerSettingForm key="settings" dealer={dealer} />;

            default: {
                const module = modules.find(module => module.id === activeTab);

                return <DealerModuleManagement key={activeTab} dealer={dealer} module={module} moduleId={activeTab} />;
            }
        }
    };

    const dealerId = dealer?.id;
    const deleteDealerModal = useDeleteDealerModal();

    const onDeleteDealer = useCallback(async () => {
        try {
            // loading message
            message.loading({
                content: t('dealerForm:messages.deleteSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // delete with the API
            await apolloClient
                .mutate<DeleteDealerMutation, DeleteDealerMutationVariables>({
                    mutation: DeleteDealerDocument,
                    variables: { id: dealerId },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // show success
            message.success({
                content: t('dealerForm:messages.deleteSuccessful'),
                key: 'primary',
            });

            // then go back to list
            navigate('/admin/dealers');
        } catch (error) {
            const apolloErrors = getApolloErrors(error);
            if (apolloErrors?.$root) {
                message.error(apolloErrors?.$root);
            }
        }
    }, [t, apolloClient, dealerId, navigate]);

    const title = useMemo(() => {
        const allowLimitDealerFeature = dealer?.company?.allowLimitDealerFeature;

        if (allowLimitDealerFeature) {
            return (
                <StyledTitle>
                    {screens.md ? t('dealerForm:title', { name: dealer.displayName }) : dealer.displayName}

                    {renderDealerType({ allowLimitDealerFeature, limitFeature: dealer.limitFeature, t })}
                </StyledTitle>
            );
        }

        return <span>{screens.md ? t('dealerForm:title', { name: dealer.displayName }) : dealer.displayName}</span>;
    }, [screens, t, dealer]);

    return (
        <ConsolePageWithHeader
            extra={
                hasPermissions(dealer?.permissions, [permissionKind.deleteDealer]) && (
                    <Button onClick={deleteDealerModal.open} danger>
                        {t('dealerForm:actions.delete')}
                    </Button>
                )
            }
            footer={[
                hasPermissions(dealer?.permissions, [permissionKind.updateDealer]) && (
                    <Button key="submit" form={`dealerForm-${activeTab}`} htmlType="submit" type="primary">
                        {t('dealerForm:actions.update')}
                    </Button>
                ),
            ]}
            header={{
                footer:
                    tabPane.length > 1 ? <Tabs onChange={activeKey => setActiveTab(activeKey)}>{tabPane}</Tabs> : null,
            }}
            onBack={() => navigate('/admin/dealers')}
            title={title}
        >
            {render()}
            {deleteDealerModal.render(onDeleteDealer)}
        </ConsolePageWithHeader>
    );
};

export default DealerInner;
