import { uniqBy } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { useGetConditionDetailQuery } from '../../../../api/queries/getConditionDetail';
import { useGetMobilityHomeDeliveryQuery } from '../../../../api/queries/getMobilityHomeDelivery';
import { useGetMobilityLocationQuery } from '../../../../api/queries/getMobilityLocation';
import { useListLocationsQuery } from '../../../../api/queries/listLocations';
import { useListMobilityHomeDeliveriesQuery } from '../../../../api/queries/listMobilityHomeDeliveries';
import { ConditionSettings, ConditionType } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { Condition } from './Condition';

export type ConditionContainerProps = {
    conditionSettings: ConditionSettings;
    onClick: () => void;
    onDelete: () => void;
    logicCondition?: boolean;
};

const ApplicationConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { applicationModuleConditionSettings } = conditionSettings;
    const { moduleId } = applicationModuleConditionSettings;

    const { data, loading } = useGetConditionDetailQuery({
        fetchPolicy: 'cache-and-network',
        variables: { id: moduleId, kind: ConditionType.IsApplicationModule },
    });

    if (loading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isApplicationModule', {
                displayName: data.conditionDetail?.displayName,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const SalesOfferConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { salesOfferConditionSettings } = conditionSettings;

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:salesOfferAgreement', {
                displayName: salesOfferConditionSettings?.feature,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const BankConditionContainer = ({ conditionSettings, onDelete, onClick, logicCondition }: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { bankConditionSettings } = conditionSettings;
    const { bankId } = bankConditionSettings;

    const { data, loading } = useGetConditionDetailQuery({
        fetchPolicy: 'cache-and-network',
        variables: { id: bankId, kind: ConditionType.IsBank },
    });

    if (loading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isBank', {
                displayName: data.conditionDetail?.displayName,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const DealerConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { dealerConditionsSettings } = conditionSettings;
    const { dealerId } = dealerConditionsSettings;

    const { data, loading } = useGetConditionDetailQuery({
        fetchPolicy: 'cache-and-network',
        variables: { id: dealerId, kind: ConditionType.IsDealer },
    });

    if (loading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isDealer', {
                displayName: data.conditionDetail?.displayName,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const InsurerConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { insurerConditionSettings } = conditionSettings;
    const { insurerId } = insurerConditionSettings;

    const { data, loading } = useGetConditionDetailQuery({
        fetchPolicy: 'cache-and-network',
        variables: { id: insurerId, kind: ConditionType.IsInsurer },
    });

    if (loading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isInsurer', {
                displayName: data.conditionDetail?.displayName,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const GiftVoucherConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { giftVoucherConditionSettings } = conditionSettings;
    const { moduleId } = giftVoucherConditionSettings;

    const { data, loading } = useGetConditionDetailQuery({
        fetchPolicy: 'cache-and-network',
        variables: { id: moduleId, kind: ConditionType.IsGiftVoucher },
    });

    if (loading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isGiftVoucher', {
                displayName: data.conditionDetail?.displayName,
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const LocationConditionContainer = ({
    conditionSettings,
    onDelete,
    onClick,
    logicCondition,
}: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { locationConditionSettings } = conditionSettings;
    const { locationId, isHomeDelivery } = locationConditionSettings;
    const company = useCompany(true);

    const { data: listLocationData } = useListLocationsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { companyId: company?.id } },
        skip: !locationId || isHomeDelivery,
    });

    const { data: listDeliveryData } = useListMobilityHomeDeliveriesQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyId: company?.id,
            },
        },
    });

    const isMoreThanOneLocationsModule = uniqBy('moduleName', listLocationData?.locations?.items).length > 1;
    const isMoreThanOneHomeDeliveryModule =
        uniqBy(
            'id',
            listDeliveryData?.delivery?.items
                .filter(result => result.homeDelivery.isEnable)
                .flatMap(result => result.homeDelivery)
        ).length > 1;

    const { data, loading } = useGetMobilityLocationQuery({
        fetchPolicy: 'cache-and-network',
        variables: { locationId },
        skip: !locationId || isHomeDelivery,
    });

    const { data: deliveryData, loading: deliveryLoading } = useGetMobilityHomeDeliveryQuery({
        fetchPolicy: 'cache-and-network',
        variables: { deliveryId: locationId },
        skip: !locationId || !isHomeDelivery,
    });

    if (loading || deliveryLoading) {
        return null;
    }

    return (
        <Condition
            label={t('consentsAndDeclarations:conditionLabels:isLocation', {
                displayName: isHomeDelivery
                    ? t('common:homeDeliveryOption', {
                          moduleDisplayName: isMoreThanOneHomeDeliveryModule ? deliveryData?.delivery?.displayName : '',
                      })
                    : (t('common:pickUpLocation', {
                          locationPickUpName: data?.location?.name,
                          moduleDisplayName: isMoreThanOneLocationsModule ? data?.location.moduleName : '',
                      }) ?? ''),
            })}
            logicCondition={logicCondition}
            onClick={logicCondition ? onClick : onDelete}
        />
    );
};

const ConditionContainer = ({ conditionSettings, onClick, onDelete, logicCondition }: ConditionContainerProps) => {
    const { t } = useTranslation('consentsAndDeclarations');

    switch (conditionSettings.type) {
        case ConditionType.IsApplicationModule: {
            return (
                <ApplicationConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );
        }

        case ConditionType.IsBank: {
            return (
                <BankConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );
        }

        case ConditionType.IsDealer: {
            return (
                <DealerConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );
        }

        case ConditionType.IsInsurer: {
            return (
                <InsurerConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );
        }

        case ConditionType.IsApplyingForFinancing:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isApplyingForFinancing')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsApplyingForInsurance:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels.isApplyingForInsurance')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsApplicant:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isApplicant')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsGuarantor:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isGuarantor')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsCorporate:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isCorporate')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsTestDrive:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isTestDrive')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsShowroomVisit:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isShowroomVisit')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsTradeIn:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isTradeIn')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.WithMyinfo:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:withMyinfo')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.WithoutMyinfo:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:withoutMyinfo')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsLocation:
            return (
                <LocationConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );

        case ConditionType.IsTestDriveProcess:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:isTestDriveProcess')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.IsGiftVoucher:
            return (
                <GiftVoucherConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );

        case ConditionType.ForCapQualification:
            return (
                <Condition
                    label={t('consentsAndDeclarations:conditionLabels:forCapQualification')}
                    logicCondition={logicCondition}
                    onClick={logicCondition ? onClick : onDelete}
                />
            );

        case ConditionType.SalesOfferAgreements:
            return (
                <SalesOfferConditionContainer
                    conditionSettings={conditionSettings}
                    logicCondition={logicCondition}
                    onClick={onClick}
                    onDelete={onDelete}
                />
            );

        default:
            return null;
    }
};

export default ConditionContainer;
