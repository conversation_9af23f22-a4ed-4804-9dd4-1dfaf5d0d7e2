import { useField } from 'formik';
import { memo, useCallback, useMemo } from 'react';
import type { CheckboxProps } from '../../../themes/context';
import { StyledConsentCheckBox } from '../ConsentCheckBoxField';
import FormItem, { FormItemProps } from '../FormItem';

export interface CheckboxFieldProps extends CheckboxProps {
    name: string;
    label?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    required?: boolean;
    customComponent?: React.ComponentType<CheckboxProps>;
    showValidationOnCustomComponent?: boolean;
}

const CheckboxField = ({
    name,
    required,
    label,
    children,
    itemProps,
    customComponent: CustomComponent,
    onChange: onChangeProps,
    showValidationOnCustomComponent = false,
    ...props
}: CheckboxFieldProps) => {
    const [{ onChange, ...field }, meta] = useField({ name });

    const enhancedOnChange = useCallback<CheckboxProps['onChange']>(
        ev => {
            onChange(ev);
            onChangeProps?.(ev);
        },
        [onChange, onChangeProps]
    );
    const UsedCheckboxComponent = useMemo(() => CustomComponent ?? StyledConsentCheckBox, [CustomComponent]);

    return (
        <FormItem
            {...itemProps}
            label={label}
            required={required}
            {...(showValidationOnCustomComponent ? {} : { meta })}
        >
            <UsedCheckboxComponent
                checked={field.value}
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
                onChange={enhancedOnChange}
                showValidation={showValidationOnCustomComponent}
            >
                {children}
            </UsedCheckboxComponent>
        </FormItem>
    );
};

export default memo(CheckboxField);
