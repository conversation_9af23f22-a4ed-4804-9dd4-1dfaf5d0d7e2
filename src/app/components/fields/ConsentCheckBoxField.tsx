import { Checkbox } from 'antd';
import { useField } from 'formik';
import { memo, useMemo } from 'react';
import styled from 'styled-components';
import type { CheckboxProps } from '../../themes/context';
import FormItem, { FormItemProps } from './FormItem';

export const StyledConsentCheckBox = styled(Checkbox)`
    & .ant-checkbox {
        & + span {
            padding-left: 1rem;
        }

        & .ant-checkbox-inner {
            width: 1.5rem;
            height: 1.5rem;
            background-color: transparent;
            border-color: #ccc;
            margin-bottom: -2px;
        }

        &:hover::after {
            visibility: hidden;
        }
    }

    &.ant-checkbox-wrapper {
        &:hover .ant-checkbox::after {
            visibility: hidden;
        }
    }

    & .ant-checkbox-checked > .ant-checkbox-inner {
        border-color: var(--ant-primary-color);
        background-color: var(--ant-primary-color);

        &::after {
            border-width: 2px;
            border-color: #fff;
            transform: rotate(45deg) scale(1.1, 1.5) translate(-50%, -50%);
            top: 12px;
            left: 5px;
        }
    }
`;

export interface CheckboxFieldProps extends Omit<CheckboxProps, 'onChange'> {
    name: string;
    label?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    required?: boolean;
    customComponent?: React.ComponentType<CheckboxProps>;
    showValidationOnCustomComponent?: boolean;
}

const ConsentCheckboxField = ({
    name,
    required,
    label,
    itemProps,
    customComponent: CustomComponent,
    showValidationOnCustomComponent = false,
    ...props
}: CheckboxFieldProps) => {
    const [{ onChange, onBlur, ...field }, meta] = useField({ name });

    const UsedCheckboxComponent = useMemo(() => CustomComponent ?? StyledConsentCheckBox, [CustomComponent]);

    return (
        <FormItem
            {...itemProps}
            label={label}
            required={required}
            {...(showValidationOnCustomComponent ? {} : { meta })}
        >
            <UsedCheckboxComponent
                checked={field.value}
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
                showValidation={showValidationOnCustomComponent}
            />
        </FormItem>
    );
};

export default memo(ConsentCheckboxField);
