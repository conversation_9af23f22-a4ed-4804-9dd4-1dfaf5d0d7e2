import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LaunchPadModuleSpecsFragment } from '../../../api/fragments/LaunchPadModuleSpecs';
import { LeadDataFragment } from '../../../api/fragments/LeadData';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';
import TestDriveForm from './TestDriveForm';
import { StateAndDispatch } from './types';
import useReducer from './useReducer';

type AppointmentModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    endpointId: string;
    lead: LeadDataFragment;
    dealerId: string;
    countryCode: string;
    timeZone: string;
};

const AppointmentModal = ({ state, dispatch, onClose }: StateAndDispatch & { onClose: () => void }) => {
    const { visible } = state;
    const [submissionLoading, setSubmissionLoading] = useState(false);

    const { t } = useTranslation(['launchpadLeadDetails']);

    const onCancel = useCallback(() => {
        onClose();
    }, [onClose]);

    const footerButton = useMemo(
        () => [
            <Button
                key="submitTestDrive"
                data-cy="create-test-drive-button"
                disabled={submissionLoading}
                form="testDriveForm"
                htmlType="submit"
                type="primary"
                block
            >
                {t('launchpadLeadDetails:appointmentModal.buttons.create')}
            </Button>,
            <Button
                key="cancel"
                data-cy="cancel-test-drive-creation-button"
                disabled={submissionLoading}
                onClick={onCancel}
                type="tertiary"
                block
            >
                {t('launchpadLeadDetails:appointmentModal.buttons.cancel')}
            </Button>,
        ],
        [onCancel, submissionLoading, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            data-cy="create-test-drive-modal"
            footer={footerButton}
            maskClosable={!submissionLoading}
            minWidth="800px"
            onCancel={onCancel}
            open={visible}
            title={t('launchpadLeadDetails:appointmentModal.title.testDrive')}
            centered
            destroyOnClose
        >
            <TestDriveForm dispatch={dispatch} setSubmissionLoading={setSubmissionLoading} state={state} />
        </Modal>
    );
};

export const useAppointmentModal = ({
    launchpadModule,
    endpointId,
    lead,
    dealerId,
    countryCode,
    timeZone,
}: AppointmentModalProps) => {
    const [state, dispatch] = useReducer();

    const actions = useMemo(
        () => ({
            createTestDrive: () => {
                dispatch({
                    type: 'setStateValues',
                    values: {
                        visible: true,
                        lead,
                        launchpadModule,
                        appointmentBaseSubmissionValue: {
                            applicationModuleId: launchpadModule.id,
                            endpointId,
                            dealerId,
                            countryCode,
                            timeZone,
                        },
                    },
                });
            },
            close: () => {
                dispatch({ type: 'resetStateValues' });
            },
        }),
        [countryCode, dealerId, endpointId, launchpadModule, lead, timeZone]
    );

    return { ...actions, render: () => <AppointmentModal dispatch={dispatch} onClose={actions.close} state={state} /> };
};

export default AppointmentModal;
