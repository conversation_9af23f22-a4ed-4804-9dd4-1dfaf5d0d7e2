import { ApolloError, useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { FormikHelpers, FormikProps } from 'formik';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LaunchPadModuleSpecsFragment } from '../../../api/fragments/LaunchPadModuleSpecs';
import { LeadDataFragment } from '../../../api/fragments/LeadData';
import {
    CreateLeadFollowUpMutation,
    CreateLeadFollowUpMutationVariables,
    CreateLeadFollowUpDocument,
} from '../../../api/mutations/createLeadFollowUp';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';
import useHandleError from '../../../utilities/useHandleError';
import { useLanguage } from '../../contexts/LanguageContextManager';
import FollowUpLeadForm from './FollowUpForm';
import { FollowUpLeadValues } from './types';

type FollowUpModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    lead: LeadDataFragment;
    endpointId: string;
    visible: boolean;
    onCloseAction?: () => void;
};

const FollowUpLeadModal = ({ launchpadModule, lead, endpointId, onCloseAction, visible }: FollowUpModalProps) => {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { t } = useTranslation(['launchpadLeadDetails']);
    const { notification } = useThemeComponents();
    const apolloClient = useApolloClient();
    const formRef = useRef<FormikProps<FollowUpLeadValues>>(null);

    const { currentLanguageId } = useLanguage();

    const handleClose = useCallback(() => {
        formRef.current?.resetForm();
        onCloseAction?.();
    }, [onCloseAction]);

    const handleSubmit = useHandleError(
        async (values: FollowUpLeadValues, { resetForm }: FormikHelpers<FollowUpLeadValues>) => {
            try {
                setIsSubmitting(true);
                notification.loading({
                    content: t('launchpadLeadDetails:followUpModal.creatingMessage'),
                    duration: 0,
                    key: 'primary',
                });

                const scheduledDateTime = dayjs(values.scheduledDate)
                    .hour(values.scheduledTime.hour())
                    .minute(values.scheduledTime.minute())
                    .second(0)
                    .tz(launchpadModule?.company?.timeZone, true)
                    .toDate();

                const { errors } = await apolloClient.mutate<
                    CreateLeadFollowUpMutation,
                    CreateLeadFollowUpMutationVariables
                >({
                    mutation: CreateLeadFollowUpDocument,
                    variables: {
                        leadId: lead?.id,
                        followUpValues: {
                            scheduledDate: scheduledDateTime,
                            remarks: values.remarks,
                            endpointId,
                            languageId: currentLanguageId,
                        },
                    },
                });

                if (!errors) {
                    notification.success({
                        content: t('launchpadLeadDetails:followUpModal.submissionCompleteMessage'),
                        key: 'primary',
                    });
                    resetForm();
                    handleClose();
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error({ content: error.graphQLErrors[0].message });
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
                setIsSubmitting(false);
            }
        },
        [
            apolloClient,
            currentLanguageId,
            endpointId,
            handleClose,
            launchpadModule?.company?.timeZone,
            lead?.id,
            notification,
            t,
        ]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={[
                <Button
                    key="submit"
                    disabled={isSubmitting}
                    form="followUpForm"
                    htmlType="submit"
                    loading={isSubmitting}
                    type="primary"
                    block
                >
                    {t('launchpadLeadDetails:followUpModal.buttons.submit')}
                </Button>,
                <Button key="cancel" disabled={isSubmitting} onClick={handleClose} type="tertiary" block>
                    {t('launchpadLeadDetails:followUpModal.buttons.cancel')}
                </Button>,
            ]}
            maskClosable={!isSubmitting}
            onCancel={handleClose}
            open={visible}
            title={t('launchpadLeadDetails:followUpModal.title')}
            centered
            destroyOnClose
        >
            <FollowUpLeadForm formRef={formRef} onSubmit={handleSubmit} />
        </Modal>
    );
};

type UseFollowUpModalProps = Omit<FollowUpModalProps, 'visible'>;

export const useFollowUpLeadModal = ({ launchpadModule, lead, endpointId, onCloseAction }: UseFollowUpModalProps) => {
    const [isVisible, setIsVisible] = useState(false);

    const onClose = useCallback(() => {
        setIsVisible(false);
        onCloseAction?.();
    }, [onCloseAction]);

    return {
        open: () => setIsVisible(true),
        close: onClose,
        render: () => (
            <FollowUpLeadModal
                endpointId={endpointId}
                launchpadModule={launchpadModule}
                lead={lead}
                onCloseAction={onClose}
                visible={isVisible}
            />
        ),
    };
};

export default FollowUpLeadModal;
