import { ApolloError } from '@apollo/client';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { ApplicationDataFragment } from '../../../api/fragments/ApplicationData';
import { useCancelLeadFollowUpMutation } from '../../../api/mutations/cancelLeadFollowUp';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';

const Container = styled.div`
    margin-bottom: 32px;
`;

type UseCancelFollowUpModalProps = {
    application: Extract<ApplicationDataFragment, { __typename: 'LaunchpadApplication' }>;
    refetchApplication?: () => void;
};

type CancelFollowUpModalProps = UseCancelFollowUpModalProps & {
    visible: boolean;
    onClose?: (isSaved: boolean) => void;
};

const CancelFollowUpModal = ({ application, onClose, visible, refetchApplication }: CancelFollowUpModalProps) => {
    const { t } = useTranslation(['launchpadFollowUpDetails']);
    const { notification, Typography } = useThemeComponents();

    const [cancelFollowUpMutation, { loading: submissionLoading }] = useCancelLeadFollowUpMutation();

    const onCancel = useCallback(() => {
        onClose?.(false);
    }, [onClose]);

    const cancelFollowUpAction = useCallback(async () => {
        try {
            notification.loading({
                content: t('launchpadFollowUpDetails:cancelModal.updating'),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await cancelFollowUpMutation({
                variables: {
                    applicationId: application.id,
                },
            });

            if (!errors) {
                notification.success({
                    content: t('launchpadFollowUpDetails:cancelModal.submissionCompleteMessage'),
                    key: 'primary',
                });

                onClose?.(true);
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error({
                    content: error.graphQLErrors[0]?.message || error.message,
                    key: 'primary',
                });
            } else {
                console.error(error);
                notification.error({
                    content: t('launchpadFollowUpDetails:cancelModal.errorMessage'),
                    key: 'primary',
                });
            }
        } finally {
            notification.destroy('primary');
        }
    }, [cancelFollowUpMutation, application.id, notification, onClose, t]);

    const footerButtons = useMemo(
        () => [
            <Button
                key="submit-cancel"
                loading={submissionLoading}
                onClick={e => {
                    e.preventDefault();
                    cancelFollowUpAction();
                }}
                type="primary"
                block
            >
                {t('launchpadFollowUpDetails:cancelModal.buttons.confirm')}
            </Button>,
            <Button key="keep" disabled={submissionLoading} onClick={onCancel} type="tertiary" block>
                {t('launchpadFollowUpDetails:cancelModal.buttons.cancel')}
            </Button>,
        ],
        [t, onCancel, cancelFollowUpAction, submissionLoading]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButtons}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t('launchpadFollowUpDetails:cancelModal.title')}
            centered
            destroyOnClose
        >
            <Container>
                <Typography.Text level={4}>
                    {t('launchpadFollowUpDetails:cancelModal.confirmationMessage')}
                </Typography.Text>
            </Container>
        </Modal>
    );
};

export default CancelFollowUpModal;

export const useCancelFollowUpModal = ({ refetchApplication, ...props }: UseCancelFollowUpModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    refetchApplication?.();
                }
            },
        }),
        [refetchApplication]
    );

    return {
        ...actions,
        render: () => <CancelFollowUpModal onClose={actions.close} visible={visible} {...props} />,
    };
};
