import { ApolloError } from '@apollo/client';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { ApplicationDataFragment } from '../../../api/fragments/ApplicationData';
import { useConfirmLeadFollowUpMutation } from '../../../api/mutations/confirmLeadFollowUp';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';

const Container = styled.div`
    margin-bottom: 32px;
`;

type UseConfirmFollowUpModalProps = {
    application: Extract<ApplicationDataFragment, { __typename: 'LaunchpadApplication' }>;
    refetchApplication?: () => void;
};

type ConfirmFollowUpModalProps = UseConfirmFollowUpModalProps & {
    visible: boolean;
    onClose?: (isSaved: boolean) => void;
};

const ConfirmFollowUpModal = ({ application, onClose, visible, refetchApplication }: ConfirmFollowUpModalProps) => {
    const { t } = useTranslation(['launchpadFollowUpDetails']);
    const { notification, Typography } = useThemeComponents();

    const [confirmFollowUpMutation, { loading: submissionLoading }] = useConfirmLeadFollowUpMutation();

    const onCancel = useCallback(() => {
        onClose?.(false);
    }, [onClose]);

    const confirmFollowUpAction = useCallback(async () => {
        try {
            notification.loading({
                content: t('launchpadFollowUpDetails:confirmModal.updating'),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await confirmFollowUpMutation({
                variables: {
                    applicationId: application.id,
                },
            });

            if (!errors) {
                notification.success({
                    content: t('launchpadFollowUpDetails:confirmModal.submissionCompleteMessage'),
                    key: 'primary',
                });

                onClose?.(true);
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error({
                    content: error.graphQLErrors[0]?.message || error.message,
                    key: 'primary',
                });
            } else {
                console.error(error);
                notification.error({
                    content: t('launchpadFollowUpDetails:confirmModal.errorMessage'),
                    key: 'primary',
                });
            }
        } finally {
            notification.destroy('primary');
        }
    }, [confirmFollowUpMutation, application.id, notification, onClose, t]);

    const footerButtons = useMemo(
        () => [
            <Button
                key="submit-confirm"
                loading={submissionLoading}
                onClick={e => {
                    e.preventDefault();
                    confirmFollowUpAction();
                }}
                type="primary"
                block
            >
                {t('launchpadFollowUpDetails:confirmModal.buttons.confirm')}
            </Button>,
            <Button key="cancel" disabled={submissionLoading} onClick={onCancel} type="tertiary" block>
                {t('launchpadFollowUpDetails:confirmModal.buttons.cancel')}
            </Button>,
        ],
        [t, onCancel, confirmFollowUpAction, submissionLoading]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButtons}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t('launchpadFollowUpDetails:confirmModal.title')}
            centered
            destroyOnClose
        >
            <Container>
                <Typography.Text level={4}>
                    {t('launchpadFollowUpDetails:confirmModal.confirmationMessage')}
                </Typography.Text>
            </Container>
        </Modal>
    );
};

export default ConfirmFollowUpModal;

export const useConfirmFollowUpModal = ({ refetchApplication, ...props }: UseConfirmFollowUpModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    refetchApplication?.();
                }
            },
        }),
        [refetchApplication]
    );

    return {
        ...actions,
        render: () => <ConfirmFollowUpModal onClose={actions.close} visible={visible} {...props} />,
    };
};
