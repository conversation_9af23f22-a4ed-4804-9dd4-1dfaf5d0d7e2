import { ApolloError, useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { FormikHelpers, FormikProps } from 'formik';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalCustomerFieldSource } from '../../../api';
import {
    DraftLaunchpadApplicationDocument,
    DraftLaunchpadApplicationMutation,
    DraftLaunchpadApplicationMutationVariables,
} from '../../../api/mutations/draftLaunchpadApplication';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';
import useHandleError from '../../../utilities/useHandleError';
import { useLanguage } from '../../contexts/LanguageContextManager';
import TradeInVehicleForm from './TradeInVehicleForm';
import { TradeInVehicleModalFormikValues, TradeInVehicleModalProps } from './typings';

const TradeInVehicleModal = ({
    lead,
    onClose,
    visible,
    endpointId,
    launchpadModuleId,
    refetchLead,
}: TradeInVehicleModalProps) => {
    const { notification } = useThemeComponents();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const formRef = useRef<FormikProps<TradeInVehicleModalFormikValues>>(null);
    const { t } = useTranslation(['launchpadLeadDetails']);

    const handleClose = useCallback(() => {
        formRef.current?.resetForm();
        onClose?.();
    }, [onClose]);

    const apolloClient = useApolloClient();

    const { currentLanguageId } = useLanguage();

    const handleSubmit = useHandleError(
        async (
            values: TradeInVehicleModalFormikValues,
            { resetForm }: FormikHelpers<TradeInVehicleModalFormikValues>
        ) => {
            try {
                setIsSubmitting(true);
                notification.loading({
                    content: t('launchpadLeadDetails:tradeInModal.submittingTradeIn'),
                    duration: 0,
                    key: 'primary',
                });

                const { errors } = await apolloClient.mutate<
                    DraftLaunchpadApplicationMutation,
                    DraftLaunchpadApplicationMutationVariables
                >({
                    mutation: DraftLaunchpadApplicationDocument,
                    variables: {
                        leadId: lead?.id,
                        endpointId,
                        languageId: currentLanguageId,
                        launchpadModuleId,
                        tradeInVehicle: [
                            {
                                ...values,
                                isSelected: false,
                                source: LocalCustomerFieldSource.UserInput,
                                yearOfManufacture: values.yearOfManufacture
                                    ? parseInt(dayjs(values.yearOfManufacture).format('YYYY'), 10)
                                    : null,
                            },
                        ],
                    },
                });

                if (!errors) {
                    notification.success({
                        content: t('launchpadLeadDetails:tradeInModal.tradeInCompletedMessage'),
                        key: 'primary',
                    });
                    resetForm();
                    refetchLead?.();
                    onClose();
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error({ content: error.graphQLErrors[0].message });
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
                setIsSubmitting(false);
            }
        },
        [
            notification,
            t,
            apolloClient,
            lead?.id,
            endpointId,
            currentLanguageId,
            launchpadModuleId,
            refetchLead,
            onClose,
        ]
    );

    const footerButton = useMemo(
        () => [
            <Button
                key="submit"
                disabled={isSubmitting}
                form="tradeInModalForm"
                htmlType="submit"
                loading={isSubmitting}
                type="primary"
                block
            >
                {t('launchpadLeadDetails:tradeInModal.buttons.submit')}
            </Button>,
            <Button key="cancel" disabled={isSubmitting} onClick={handleClose} type="tertiary" block>
                {t('launchpadLeadDetails:tradeInModal.buttons.cancel')}
            </Button>,
        ],
        [handleClose, isSubmitting, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButton}
            onCancel={onClose}
            open={visible}
            title={t('launchpadLeadDetails:tradeInModal.title')}
            centered
            destroyOnClose
        >
            <TradeInVehicleForm formRef={formRef} onSubmit={handleSubmit} />
        </Modal>
    );
};

export default TradeInVehicleModal;
