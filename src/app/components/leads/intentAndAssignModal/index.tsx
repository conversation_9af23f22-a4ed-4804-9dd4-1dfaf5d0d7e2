import { ApolloError, useApolloClient } from '@apollo/client';
import { FormikHelpers, FormikProps } from 'formik';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    SubmitIntentAndAssignDocument,
    SubmitIntentAndAssignMutation,
    SubmitIntentAndAssignMutationVariables,
    LaunchPadModuleSpecsFragment,
    LeadDataFragment,
} from '../../../api';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';
import { isValid as isValidObjectId } from '../../../utilities/oid';
import useCapCampaignIdOptions from '../../../utilities/useCapCampaignIdOptions';
import useHandleError from '../../../utilities/useHandleError';
import { useLanguage } from '../../contexts/LanguageContextManager';
import IntentAndAssignForm from './IntentAndAssignForm';
import { IntentAndAssignFormValues } from './types';

type UseIntentAndAssignModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    lead: LeadDataFragment;
    dealerId: string;
    onClose?: (isSave: boolean) => void;
};

type IntentAndAssignModalProps = UseIntentAndAssignModalProps & {
    visible: boolean;
};

const IntentAndAssignModal = ({ onClose, visible, ...props }: IntentAndAssignModalProps) => {
    const { lead, launchpadModule } = props;
    const { notification } = useThemeComponents();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const formRef = useRef<FormikProps<IntentAndAssignFormValues>>(null);
    const { t } = useTranslation(['launchpadLeadDetails']);
    const campaignOptions = useCapCampaignIdOptions(launchpadModule.companyId);

    const handleClose = useCallback(() => {
        formRef.current?.resetForm();
        onClose?.(false);
    }, [onClose]);

    const apolloClient = useApolloClient();

    const { currentLanguageId } = useLanguage();

    const handleSubmit = useHandleError(
        async (
            { vehicleModel, ...values }: IntentAndAssignFormValues,
            { resetForm }: FormikHelpers<IntentAndAssignFormValues>
        ) => {
            try {
                setIsSubmitting(true);
                notification.loading({
                    content: t('launchpadLeadDetails:intentAndAssignModal.assignContact'),
                    duration: 0,
                    key: 'primary',
                });

                const campaignId = isValidObjectId(values.campaignId)
                    ? campaignOptions.find(option => option.value === values.campaignId)?.campaignId ||
                      values.campaignId
                    : values.campaignId;

                const { errors } = await apolloClient.mutate<
                    SubmitIntentAndAssignMutation,
                    SubmitIntentAndAssignMutationVariables
                >({
                    mutation: SubmitIntentAndAssignDocument,
                    variables: {
                        leadId: lead?.id,
                        submitIntentAndAssignInput: {
                            ...values,
                            languageId: currentLanguageId,
                            campaignId,
                        },
                    },
                });

                if (!errors) {
                    notification.success({
                        content: t('launchpadLeadDetails:intentAndAssignModal.submissionCompleteMessage'),
                        key: 'primary',
                    });
                    resetForm();
                    onClose?.(true);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error({ content: error.graphQLErrors[0].message });
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
                setIsSubmitting(false);
            }
        },
        [notification, t, campaignOptions, apolloClient, lead?.id, currentLanguageId, onClose]
    );

    const footerButton = useMemo(
        () => [
            <Button
                key="submit"
                data-cy="intent-and-assign-submit-button"
                disabled={isSubmitting}
                form="intentAndAssignForm"
                htmlType="submit"
                loading={isSubmitting}
                type="primary"
                block
            >
                {t('launchpadLeadDetails:intentAndAssignModal.buttons.submit')}
            </Button>,
            <Button
                key="cancel"
                data-cy="intent-and-assign-cancel-button"
                disabled={isSubmitting}
                onClick={handleClose}
                type="tertiary"
                block
            >
                {t('launchpadLeadDetails:intentAndAssignModal.buttons.cancel')}
            </Button>,
        ],
        [handleClose, isSubmitting, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            data-cy="intent-and-assign-modal"
            footer={footerButton}
            onCancel={() => onClose(false)}
            open={visible}
            title={t('launchpadLeadDetails:intentAndAssignModal.title')}
            centered
            destroyOnClose
        >
            <IntentAndAssignForm
                campaignOptions={campaignOptions}
                formRef={formRef}
                onSubmit={handleSubmit}
                {...props}
            />
        </Modal>
    );
};

export default IntentAndAssignModal;

export const useIntentAndAssignModal = (props: UseIntentAndAssignModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    props.onClose?.(isSaved);
                }
            },
        }),
        [props]
    );

    const { onClose, ...restProps } = props;

    return useMemo(
        () => ({
            ...actions,
            render: () => <IntentAndAssignModal onClose={actions.close} visible={visible} {...restProps} />,
        }),
        [actions, restProps, visible]
    );
};
