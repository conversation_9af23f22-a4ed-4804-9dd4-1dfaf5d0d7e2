import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { LaunchPadModuleSpecsFragment } from '../../../api/fragments/LaunchPadModuleSpecs';
import type { LeadDataFragment } from '../../../api/fragments/LeadData';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';
import ShowroomVisitForm from './ShowroomVisitForm';
import { StateAndDispatch } from './types';
import useReducer from './useReducer';

type ShowroomVisitModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    endpointId: string;
    lead: LeadDataFragment;
    timeZone: string;
};

const ShowroomVisitModal = ({ state, dispatch, onClose }: StateAndDispatch & { onClose: () => void }) => {
    const { visible } = state;
    const [submissionLoading, setSubmissionLoading] = useState(false);

    const { t } = useTranslation(['launchpadLeadDetails']);

    const onCancel = useCallback(() => {
        onClose();
    }, [onClose]);

    const footerButton = useMemo(
        () => [
            <Button
                key="submitShowroomVisit"
                data-cy="create-showroom-visit-button"
                disabled={submissionLoading}
                form="showroomVisitForm"
                htmlType="submit"
                type="primary"
                block
            >
                {t('launchpadLeadDetails:appointmentModal.buttons.create')}
            </Button>,
            <Button
                key="cancel"
                data-cy="cancel-showroom-visit-creation-button"
                disabled={submissionLoading}
                onClick={onCancel}
                type="tertiary"
                block
            >
                {t('launchpadLeadDetails:appointmentModal.buttons.cancel')}
            </Button>,
        ],
        [onCancel, submissionLoading, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            data-cy="create-showroom-visit-modal"
            footer={footerButton}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t('launchpadLeadDetails:appointmentModal.title.showroomVisit')}
            centered
            destroyOnClose
        >
            <ShowroomVisitForm dispatch={dispatch} setSubmissionLoading={setSubmissionLoading} state={state} />
        </Modal>
    );
};

export const useShowroomVisitModal = ({ launchpadModule, endpointId, lead, timeZone }: ShowroomVisitModalProps) => {
    const [state, dispatch] = useReducer();

    const actions = useMemo(
        () => ({
            createShowroomVisit: () => {
                dispatch({
                    type: 'setStateValues',
                    values: {
                        visible: true,
                        lead,
                        launchpadModule,
                        showroomVisitBaseSubmissionValue: {
                            applicationModuleId: launchpadModule.id,
                            endpointId,
                            timeZone,
                        },
                    },
                });
            },
            close: () => {
                dispatch({ type: 'resetStateValues' });
            },
        }),
        [endpointId, launchpadModule, lead, timeZone]
    );

    return {
        ...actions,
        render: () => <ShowroomVisitModal dispatch={dispatch} onClose={actions.close} state={state} />,
    };
};

export default ShowroomVisitModal;
