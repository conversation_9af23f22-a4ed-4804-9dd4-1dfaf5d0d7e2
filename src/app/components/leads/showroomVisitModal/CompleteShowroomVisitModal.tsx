import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useCompleteApplicationMutation } from '../../../api/mutations/completeApplication';
import { ApplicationStage } from '../../../api/types';
import { GenericFormApplication } from '../../../pages/shared/ApplicationDetailsPage/generic/shared';
import { useThemeComponents } from '../../../themes/hooks';
import Modal from '../../../themes/porscheV3/Modal';

type CompleteShowroomVisitModalInnerProps = {
    isOpen: boolean;
    onClose: () => void;
    submitForm: () => Promise<void>;
    isSubmitting: boolean;
};

const CompleteShowroomVisitInnerModal = ({
    submitForm,
    isSubmitting,
    isOpen,
    onClose,
}: CompleteShowroomVisitModalInnerProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const handleClose = useCallback(() => {
        onClose();
    }, [onClose]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            closable={false}
            maskClosable={false}
            okButtonProps={{
                loading: isSubmitting,
            }}
            okText={t('applicationDetails:buttons.ok')}
            onCancel={handleClose}
            onOk={submitForm}
            open={isOpen}
            title={t(`applicationDetails:confirmModal.title.complete`)}
            width={480}
        />
    );
};

type CompleteShowroomVisitModalProps = {
    application: Pick<GenericFormApplication, 'id' | '__typename'>;
    isOpen: boolean;
    onClose: () => void;
};

const CompleteShowroomVisitModal = ({ application, isOpen, onClose }: CompleteShowroomVisitModalProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [completeMutation] = useCompleteApplicationMutation();

    const { notification } = useThemeComponents();

    const onSubmit = useCallback(async () => {
        notification.loading({
            content: t('applicationDetails:messages.completingApplication'),
            key: 'primary',
            duration: 0,
        });

        setIsSubmitting(true);

        await completeMutation({
            variables: {
                applicationId: application.id,
                stage: ApplicationStage.VisitAppointment,
            },
        });

        notification.success({
            content: t('applicationDetails:messages.completedApplication'),
            key: 'primary',
        });
        setIsSubmitting(false);
        onClose();
    }, [notification, t, completeMutation, application.id, onClose]);

    return (
        <CompleteShowroomVisitInnerModal
            isOpen={isOpen}
            isSubmitting={isSubmitting}
            onClose={onClose}
            submitForm={onSubmit}
        />
    );
};

const useCompleteShowroomVisitModal = (application: CompleteShowroomVisitModalProps['application']) => {
    const [isOpen, setIsOpen] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setIsOpen(true),
            close: () => setIsOpen(false),
        }),
        []
    );

    return useMemo(
        () =>
            [
                () => actions.open(),
                () => <CompleteShowroomVisitModal application={application} isOpen={isOpen} onClose={actions.close} />,
            ] as const,
        [actions, application, isOpen]
    );
};

export default useCompleteShowroomVisitModal;
