import { CloseOutlined } from '@ant-design/icons';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../themes/hooks';
import { isValid as isValidObjectId } from '../../../utilities/oid';
import LoadingElement from '../../LoadingElement';
import BusinessPartnerList from './BusinessPartner/BusinessPartnerList';
import LeadList from './Lead/LeadList';
import SearchCapCustomerForm from './SearchCapCustomerForm';
import { ButtonClickedAction, type SearchCapCustomerKYCProps, SubmissionPurpose, ViewState } from './types';
import { DrawerFooter, SearchCustomerDrawer, SearchTitle } from './ui';
import { type StateAndDispatch, type StateProps } from './useReducer';
import { getModalLocales, useExistingBusinessPartner, useIsLoading } from './utils';

type CapCustomerDrawerType = StateProps & StateAndDispatch & SearchCapCustomerKYCProps;

const SearchCapCustomerDrawer = ({ state, dispatch, onButtonClicked, onClose, closable }: CapCustomerDrawerType) => {
    const { t } = useTranslation('capApplication');
    const { Button } = useThemeComponents();
    const {
        lead,
        applicationModuleId,
        eventId,
        capModuleId,
        visible,
        viewState,
        businessPartners,
        selectedBusinessPartner,
        leads,
        selectedLead,
        loading,
        forCi,
        selectedSalesperson,
        submissionPurpose,
        batchSearch,
    } = state;

    const eventIdForBpSearch = useMemo(() => {
        if (lead?.__typename === 'EventLead' && lead?.eventId) {
            return lead.eventId;
        }

        return eventId;
    }, [eventId, lead]);

    const { existingBusinessPartnerData, loading: searchExistingBusinessPartnerLoading } = useExistingBusinessPartner({
        businessPartnerGuid: lead?.capValues?.businessPartnerGuid,
        applicationModuleId,
        eventId: eventIdForBpSearch,
        capModuleId,
        forCi,
    });

    /*
        If current application already has Business Partner ID stored but for some reason doesn't have lead,
        when qualify application will search existing leads form stored BP so user can choose either to create new leads
        or update the existing one if any

        So, this function below is to skipping the Business Partner selection modal and go to select lead modal instead

        But if the current application doesn't have both BP and lead, then we gonna skip function below
    */
    useEffect(() => {
        dispatch({
            type: 'setLoading',
            loading: {
                loadingBP: searchExistingBusinessPartnerLoading,
                loadingLead: loading.loadingLead,
                submissionLoading: loading.submissionLoading,
            },
        });
        if (existingBusinessPartnerData) {
            dispatch({
                type: 'setStateValues',
                values: {
                    selectedBusinessPartner: existingBusinessPartnerData,
                    viewState: ViewState.Lead,
                },
            });
        }
    }, [
        dispatch,
        existingBusinessPartnerData,
        loading.loadingLead,
        loading.submissionLoading,
        searchExistingBusinessPartnerLoading,
        selectedBusinessPartner,
    ]);

    const selectedResponsibleSalespersonId = useMemo(() => {
        if (selectedSalesperson && isValidObjectId(selectedSalesperson)) {
            return selectedSalesperson;
        }

        // If existing salesperson from C@P also available on PDFS, then we use it to update the assignee
        const assigneeData = lead?.availableAssignees.find(
            assignee => assignee.alias === selectedBusinessPartner?.responsibleSalesPersonId
        );

        return assigneeData?.id;
    }, [lead?.availableAssignees, selectedBusinessPartner, selectedSalesperson]);

    const onConfirmSelection = useCallback(() => {
        switch (viewState) {
            case ViewState.BusinessPartner: {
                onButtonClicked({
                    action: ButtonClickedAction.SelectBP,
                    selectedBusinessPartner,
                    selectedResponsibleSalespersonId,
                });

                if (submissionPurpose === SubmissionPurpose.NewContact) {
                    dispatch({ type: 'setViewState', viewState: ViewState.NewContact });
                } else {
                    dispatch({ type: 'setViewState', viewState: ViewState.Lead });
                }
                break;
            }

            case ViewState.Lead: {
                onButtonClicked({
                    action: ButtonClickedAction.SelectLead,
                    selectedBusinessPartner,
                    selectedLead,
                    selectedResponsibleSalespersonId,
                });

                if (
                    submissionPurpose === SubmissionPurpose.Qualify ||
                    submissionPurpose === SubmissionPurpose.ConfirmBooking
                ) {
                    dispatch({ type: 'setViewState', viewState: ViewState.KYC });
                } else {
                    onClose();
                }
                break;
            }
        }
    }, [
        dispatch,
        selectedBusinessPartner,
        selectedLead,
        selectedResponsibleSalespersonId,
        onClose,
        viewState,
        onButtonClicked,
        submissionPurpose,
    ]);

    const isLoading = useIsLoading(loading);

    const footer = useMemo(() => {
        const isLeadView = viewState === ViewState.Lead;
        const referenceDataLength = isLeadView ? leads.length : businessPartners.length;
        const isSelected = isLeadView ? !!selectedLead : !!selectedBusinessPartner;

        return (
            <DrawerFooter size={14}>
                {!isLoading && (
                    <Button key="createNew" htmlType="button" onClick={() => onClose()} type="tertiary">
                        {t('capApplication:searchDrawer.buttons.createNew')}
                    </Button>
                )}
                {referenceDataLength > 0 && (
                    <Button
                        key="select"
                        disabled={!isSelected}
                        htmlType="button"
                        onClick={onConfirmSelection}
                        type="primary"
                    >
                        {t(`capApplication:${getModalLocales(viewState)}.buttons.select`)}
                    </Button>
                )}
            </DrawerFooter>
        );
    }, [
        Button,
        businessPartners.length,
        isLoading,
        leads.length,
        onClose,
        onConfirmSelection,
        selectedBusinessPartner,
        selectedLead,
        t,
        viewState,
    ]);

    const drawerBody = useMemo(() => {
        switch (viewState) {
            case ViewState.BusinessPartner:
                return <BusinessPartnerList dispatch={dispatch} onLeadDetails={false} state={state} />;

            case ViewState.Lead:
                return <LeadList dispatch={dispatch} onApplicationDetails={false} state={state} />;

            default:
                return null;
        }
    }, [dispatch, state, viewState]);

    return (
        <SearchCustomerDrawer
            $withHeader={closable}
            closable={closable}
            closeIcon={closable ? <CloseOutlined /> : null}
            footer={footer}
            maskClosable={closable}
            onClose={() => onClose()}
            open={visible}
            placement="right"
            size="large"
            destroyOnClose
        >
            <SearchTitle>{t('capApplication:searchDrawer.title')}</SearchTitle>
            <SearchCapCustomerForm dispatch={dispatch} state={state} />
            {isLoading && !batchSearch.searchInProgress && (
                <center>
                    <LoadingElement />
                </center>
            )}
            {drawerBody}
        </SearchCustomerDrawer>
    );
};

export default SearchCapCustomerDrawer;
