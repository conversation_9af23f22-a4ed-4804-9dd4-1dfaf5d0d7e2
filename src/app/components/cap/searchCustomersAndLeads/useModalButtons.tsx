import { TFunction } from 'i18next';
import { useMemo } from 'react';
import { useThemeComponents } from '../../../themes/hooks';
import PorscheV3Button from '../../../themes/porscheV3/Button';
import { useRouter } from '../../contexts/shared';
import { type SearchCapCustomerKYCProps, SubmissionPurpose, ViewState } from './types';
import { type State } from './useReducer';
import { getModalLocales, useIsLoading, validateSearchCriteria } from './utils';

type ModalButtonProps = {
    state: State;
    t: TFunction;
    closable?: boolean;
    forceUsingPorscheV3Components?: boolean;
    onClose: SearchCapCustomerKYCProps['onClose'];
    onConfirmError: () => void;
    onCreateNew: () => void;
    onSubmit: () => void;
};

const useModalButtons = ({
    state,
    t,
    closable = true,
    onClose,
    onConfirmError,
    onCreateNew,
    onSubmit,
    forceUsingPorscheV3Components = false,
}: ModalButtonProps) => {
    const { Button: ThemeButton } = useThemeComponents();
    const router = useRouter();

    const Button = useMemo(
        () =>
            forceUsingPorscheV3Components || router?.layout?.__typename === 'PorscheV3Layout'
                ? PorscheV3Button
                : ThemeButton,
        [ThemeButton, forceUsingPorscheV3Components, router?.layout?.__typename]
    );

    const {
        forCi,
        viewState,
        loading,
        searchedCustomer,
        businessPartners,
        selectedBusinessPartner,
        selectedSalesperson,
        leads,
        selectedLead,
        submissionPurpose,
        searchCapCustomerType,
    } = state;
    const isLoading = useIsLoading(loading);
    const hasErrorWhenFetch = useMemo(
        () => state.searchBPError || state.searchLeadError,
        [state.searchBPError, state.searchLeadError]
    );

    return useMemo(() => {
        if (isLoading) {
            return [];
        }

        const errorWhenFetchButtons = [
            !forCi && (
                <Button key="cancel" block={forCi} onClick={() => onClose()} type="tertiary">
                    {t(`capApplication:${getModalLocales(viewState)}.buttons.cancel`)}
                </Button>
            ),
            !isLoading && (
                <Button key="confirmError" block={forCi} onClick={onConfirmError} type="primary">
                    {t(`capApplication:${getModalLocales(viewState)}.buttons.ok`)}
                </Button>
            ),
        ].filter(Boolean);

        const isLeadQualification = submissionPurpose !== SubmissionPurpose.NewSubmission;

        switch (viewState) {
            case ViewState.SearchForm: {
                const hasValidSearchCriteria = validateSearchCriteria(
                    searchCapCustomerType,
                    searchedCustomer,
                    state.countryCode
                );

                return [
                    <Button
                        key="submit"
                        block={forCi}
                        disabled={!hasValidSearchCriteria}
                        form="searchCapCustomerForm"
                        htmlType="submit"
                        type="primary"
                    >
                        {t(`capApplication:${getModalLocales(viewState)}.buttons.search`)}
                    </Button>,
                    !isLoading && (
                        <Button
                            key="createNew"
                            block={forCi}
                            data-cy="create-new-button"
                            onClick={onCreateNew}
                            type="secondary"
                        >
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.createNew`)}
                        </Button>
                    ),
                ].filter(Boolean);
            }
            case ViewState.BusinessPartner: {
                if (hasErrorWhenFetch) {
                    return errorWhenFetchButtons;
                }

                const buttons = [
                    !isLoading && businessPartners.length && (
                        <Button
                            key="select"
                            block={forCi}
                            data-cy="select-business-partner-button"
                            disabled={!selectedBusinessPartner}
                            onClick={onSubmit}
                            type="primary"
                        >
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.select`)}
                        </Button>
                    ),
                    !isLoading && (
                        <Button
                            key="createNew"
                            block={forCi}
                            data-cy="create-new-button"
                            onClick={onCreateNew}
                            type={!businessPartners.length && !isLeadQualification ? 'primary' : 'tertiary'}
                        >
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.createNew`)}
                        </Button>
                    ),
                    !businessPartners.length && closable && !isLeadQualification && (
                        <Button key="cancel" block={forCi} onClick={() => onClose()} type="tertiary">
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.cancel`)}
                        </Button>
                    ),
                ].filter(Boolean);

                return forCi ? buttons : buttons.reverse();
            }

            case ViewState.SelectSalesperson: {
                const buttons = [
                    <Button
                        key="createNew"
                        block={forCi}
                        disabled={!selectedSalesperson}
                        onClick={onCreateNew}
                        type="primary"
                    >
                        {t(`capApplication:${getModalLocales(viewState)}.buttons.create`)}
                    </Button>,
                    closable && (
                        <Button key="cancel" block={forCi} onClick={() => onClose()} type="tertiary">
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.cancel`)}
                        </Button>
                    ),
                ].filter(Boolean);

                return forCi ? buttons : buttons.reverse();
            }

            case ViewState.KYC: {
                const buttons = [
                    <Button key="submitForm" block={forCi} onClick={onCreateNew} type="primary">
                        {t(`capApplication:${getModalLocales(viewState)}.buttons.submit`)}
                    </Button>,
                    closable && (
                        <Button key="cancel" block={forCi} onClick={() => onClose()} type="tertiary">
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.cancel`)}
                        </Button>
                    ),
                ].filter(Boolean);

                return forCi ? buttons : buttons.reverse();
            }

            case ViewState.Lead: {
                if (hasErrorWhenFetch) {
                    return errorWhenFetchButtons;
                }

                const buttons = [
                    leads.length > 0 && (
                        <Button key="select" block={forCi} disabled={!selectedLead} onClick={onSubmit} type="primary">
                            {!isLeadQualification && !forCi
                                ? t(`capApplication:availableLeadModal.buttons.qualify`)
                                : t(`capApplication:${getModalLocales(viewState)}.buttons.select`)}
                        </Button>
                    ),
                    !isLoading && (
                        <Button
                            key="createNew"
                            block={forCi}
                            data-cy="create-new-button"
                            onClick={onCreateNew}
                            type="tertiary"
                        >
                            {t(`capApplication:${getModalLocales(viewState)}.buttons.createNew`)}
                        </Button>
                    ),
                ].filter(Boolean);

                return forCi ? buttons : buttons.reverse();
            }

            case ViewState.NewContact: {
                return [
                    <Button
                        key="submit"
                        data-cy="submit-kyc-button"
                        form="applicantForm"
                        htmlType="submit"
                        type="primary"
                        block
                    >
                        {t('launchpadLeadList:newContactModal.kyc.buttons.submit')}
                    </Button>,
                    <Button key="cancel" data-cy="cancel-kyc-button" onClick={() => onClose()} type="tertiary" block>
                        {t('launchpadLeadList:newContactModal.kyc.buttons.cancel')}
                    </Button>,
                ];
            }

            default:
                return [];
        }
    }, [
        isLoading,
        forCi,
        Button,
        t,
        viewState,
        onConfirmError,
        submissionPurpose,
        onClose,
        searchCapCustomerType,
        searchedCustomer,
        state.countryCode,
        onCreateNew,
        hasErrorWhenFetch,
        businessPartners.length,
        selectedBusinessPartner,
        onSubmit,
        closable,
        selectedSalesperson,
        leads.length,
        selectedLead,
    ]);
};

export default useModalButtons;
