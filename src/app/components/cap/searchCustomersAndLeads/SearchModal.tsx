import { PButton } from '@porsche-design-system/components-react';
import { Row, Space } from 'antd';
import type { FormikProps } from 'formik';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { usePrefetchKycFieldsForQualifyQuery } from '../../../api/queries/prefetchKYCFieldsForQualify';
import { getApplicantKyc } from '../../../pages/portal/StandardApplicationEntrypoint/KYCPage/getKyc';
import { useThemeComponents } from '../../../themes/hooks';
import PorscheV3Modal from '../../../themes/porscheV3/Modal';
import { isValid as isValidObjectId } from '../../../utilities/oid';
import LoadingElement from '../../LoadingElement';
import { useRouter } from '../../contexts/shared';
import NewContactKYC from '../../leads/newContactKYC';
import BusinessPartnerList from './BusinessPartner/BusinessPartnerList';
import { useContactKycConfirmationModal } from './ContactKycSubmission/useContactKycConfirmationModal';
import LeadList from './Lead/LeadList';
import QualifyModalInner from './QualifyModalInner/QualifyInner';
import type { QualifyContactValues } from './QualifyModalInner/types';
import SearchCapCustomerForm from './SearchCapCustomerForm';
import SelectResponsibleSalesperson from './SelectResponsibleSalesperson';
import { ButtonClickedAction, type SearchCapCustomerKYCProps, SubmissionPurpose, ViewState } from './types';
import useModalButtons from './useModalButtons';
import { SearchCapCustomerType, type StateAndDispatch, type StateProps } from './useReducer';
import useSubmitKyc from './useSubmitKyc';
import { hasSearchedCustomerValues, getModalLocales, useExistingBusinessPartner, useIsLoading } from './utils';

type CapCustomerModalType = StateProps &
    StateAndDispatch &
    SearchCapCustomerKYCProps & {
        onLeadDetails?: boolean;
        forceUsingPorscheV3Components?: boolean;
    };

const SearchCapCustomerModal = ({
    state,
    dispatch,
    onClose,
    closable = true,
    onLeadDetails = false,
    onButtonClicked,
    onCustomerSearchPerformed,
    forceUsingPorscheV3Components,
}: CapCustomerModalType) => {
    const { t } = useTranslation(['capApplication', 'launchpadLeadDetails', 'launchpadLeadList']);
    const { Modal: ThemeModal } = useThemeComponents();
    const router = useRouter();

    const Modal = useMemo(
        () =>
            forceUsingPorscheV3Components || router?.layout?.__typename === 'PorscheV3Layout'
                ? PorscheV3Modal
                : ThemeModal,
        [ThemeModal, forceUsingPorscheV3Components, router?.layout?.__typename]
    );

    const modalClassName = useMemo(
        () =>
            forceUsingPorscheV3Components || router?.layout?.__typename === 'PorscheV3Layout'
                ? 'launchpad-modal'
                : undefined,
        [forceUsingPorscheV3Components, router?.layout?.__typename]
    );

    const contactKycConfirmationModal = useContactKycConfirmationModal();
    const contactKycFormName = 'contactKycForm';

    const formRef = useRef<FormikProps<QualifyContactValues>>(null);

    const {
        submissionPurpose,
        lead,
        applicationModuleId,
        eventId,
        capModuleId,
        visible,
        viewState,
        businessPartners,
        searchedCustomer,
        firstLoad,
        selectedBusinessPartner,
        selectedSalesperson,
        leads,
        selectedLead,
        loading,
        forCi,
        kycFields,
        batchSearch,
    } = state;

    const isLoading = useIsLoading(loading);

    const { submitWithoutKyc } = useSubmitKyc({ state, dispatch, onClose });

    // Fetch KYC fields
    const { data: kycData, loading: kycLoading } = usePrefetchKycFieldsForQualifyQuery({
        fetchPolicy: 'cache-and-network',
        variables: { applicationModuleId },
        skip: !applicationModuleId,
    });

    // Update KYC fields in state when data is loaded
    useEffect(() => {
        if (kycData && !kycLoading) {
            const applicantKycFields = getApplicantKyc(kycData.applicantKyc ?? []);
            dispatch({ type: 'setKycFields', kycFields: applicantKycFields });
        }
    }, [kycData, kycLoading, dispatch]);

    const eventIdForBpSearch = useMemo(() => {
        if (lead?.__typename === 'EventLead' && lead?.eventId) {
            return lead.eventId;
        }

        return eventId;
    }, [eventId, lead]);

    const { existingBusinessPartnerData, loading: searchExistingBusinessPartnerLoading } = useExistingBusinessPartner({
        businessPartnerGuid: lead?.capValues?.businessPartnerGuid,
        applicationModuleId,
        eventId: eventIdForBpSearch,
        capModuleId,
        forCi,
    });

    /*
        If current application already has Business Partner ID stored but for some reason doesn't have lead,
        when qualify application will search existing leads form stored BP so user can choose either to create new leads
        or update the existing one if any

        So, this function below is to skipping the Business Partner selection modal and go to select lead modal instead

        But if the current application doesn't have both BP and lead, then we gonna skip function below
    */

    useEffect(() => {
        if (existingBusinessPartnerData && viewState === ViewState.BusinessPartner) {
            dispatch({
                type: 'setStateValues',
                values: {
                    selectedBusinessPartner: existingBusinessPartnerData,
                    viewState: ViewState.Lead,
                },
            });
        }
    }, [dispatch, viewState, existingBusinessPartnerData]);

    useEffect(() => {
        dispatch({
            type: 'setLoading',
            loading: {
                loadingBP: searchExistingBusinessPartnerLoading,
                loadingLead: loading.loadingLead,
                submissionLoading: loading.submissionLoading,
            },
        });
    }, [dispatch, loading.loadingLead, loading.submissionLoading, searchExistingBusinessPartnerLoading]);

    const selectedResponsibleSalespersonId = useMemo(() => {
        if (selectedSalesperson && isValidObjectId(selectedSalesperson)) {
            return selectedSalesperson;
        }

        // If existing salesperson from C@P also available on PDFS, then we use it to update the assignee
        const assigneeData = lead?.availableAssignees.find(
            assignee => assignee.alias === selectedBusinessPartner?.responsibleSalesPersonId
        );

        return assigneeData?.id;
    }, [lead?.availableAssignees, selectedBusinessPartner, selectedSalesperson]);

    const onSubmit = useCallback(() => {
        switch (viewState) {
            case ViewState.BusinessPartner: {
                onButtonClicked({
                    action: ButtonClickedAction.SelectBP,
                    selectedBusinessPartner,
                    selectedResponsibleSalespersonId,
                });

                if (submissionPurpose === SubmissionPurpose.NewContact) {
                    dispatch({ type: 'setViewState', viewState: ViewState.NewContact });
                } else {
                    dispatch({ type: 'setViewState', viewState: ViewState.Lead });
                }
                break;
            }

            case ViewState.Lead: {
                onButtonClicked({
                    action: ButtonClickedAction.SelectLead,
                    selectedBusinessPartner,
                    selectedLead,
                    selectedResponsibleSalespersonId,
                });

                if (
                    submissionPurpose === SubmissionPurpose.Qualify ||
                    submissionPurpose === SubmissionPurpose.ConfirmBooking
                ) {
                    dispatch({ type: 'setViewState', viewState: ViewState.KYC });
                } else {
                    onClose();
                }
                break;
            }
        }
    }, [
        viewState,
        onButtonClicked,
        selectedBusinessPartner,
        selectedResponsibleSalespersonId,
        dispatch,
        selectedLead,
        submissionPurpose,
        onClose,
    ]);

    const onCancel = useCallback(() => {
        if (closable) {
            dispatch({ type: 'setSearchCapCustomerType', searchCapCustomerType: SearchCapCustomerType.Email });

            onCustomerSearchPerformed?.(false);

            onClose();
        }
    }, [closable, dispatch, onClose, onCustomerSearchPerformed]);

    const onCreateNew = useCallback(() => {
        onCustomerSearchPerformed?.(true);
        dispatch({ type: 'setUserChooseCreateNew', userChooseCreateNew: true });

        if (onLeadDetails) {
            switch (viewState) {
                case ViewState.SearchForm:
                case ViewState.BusinessPartner: {
                    dispatch({ type: 'setViewState', viewState: ViewState.KYC });

                    break;
                }

                case ViewState.SelectSalesperson: {
                    // Check if KYC fields are available
                    if (kycFields.length > 0) {
                        dispatch({ type: 'setViewState', viewState: ViewState.KYC });
                    } else {
                        // No KYC fields, show confirmation modal directly with submitWithoutKyc
                        contactKycConfirmationModal.open();
                    }
                    break;
                }

                case ViewState.Lead: {
                    dispatch({ type: 'setViewState', viewState: ViewState.KYC });

                    break;
                }

                case ViewState.KYC: {
                    formRef.current?.submitForm();

                    break;
                }
            }

            return;
        }

        // If not on lead details, we just reset to search form
        switch (viewState) {
            case ViewState.SearchForm:
            case ViewState.BusinessPartner: {
                if (submissionPurpose === SubmissionPurpose.NewContact) {
                    if (selectedBusinessPartner) {
                        // reset selectedBusinessPartner to null
                        dispatch({ type: 'setSelectedBusinessPartner', selectedBusinessPartner: null });
                    }

                    dispatch({ type: 'setViewState', viewState: ViewState.NewContact });

                    return;
                }

                break;
            }

            default:
                break;
        }

        onClose();
    }, [
        onCustomerSearchPerformed,
        dispatch,
        onLeadDetails,
        viewState,
        onClose,
        kycFields.length,
        contactKycConfirmationModal,
        submissionPurpose,
        selectedBusinessPartner,
    ]);

    const onConfirmError = useCallback(() => {
        onButtonClicked({ action: ButtonClickedAction.ErrorSearchConfirmed });

        onClose();
    }, [onButtonClicked, onClose]);

    const hasErrorWhenFetch = useMemo(
        () => state.searchBPError || state.searchLeadError,
        [state.searchBPError, state.searchLeadError]
    );

    const modalWidth = useMemo(() => {
        const defaultWidth = 636;
        const xlWidth = 1024;

        if (viewState === ViewState.KYC || viewState === ViewState.NewContact) {
            return xlWidth;
        }

        if (!isLoading && !hasErrorWhenFetch) {
            switch (viewState) {
                case ViewState.BusinessPartner:
                    return businessPartners.length ? xlWidth : defaultWidth;

                case ViewState.Lead:
                    return leads.length ? xlWidth : defaultWidth;

                default:
                    return defaultWidth;
            }
        }

        return defaultWidth;
    }, [businessPartners.length, hasErrorWhenFetch, isLoading, leads.length, viewState]);

    const shouldShowPorscheV3Button = useMemo(() => {
        const hasSearchedCustomerValuesPresent = hasSearchedCustomerValues(searchedCustomer);

        return businessPartners.length > 0 && !firstLoad && hasSearchedCustomerValuesPresent;
    }, [businessPartners.length, firstLoad, searchedCustomer]);

    const resetToSearchCapCustomer = useCallback(() => {
        const initialSearchedCustomer = Object.fromEntries(
            Object.keys(state.searchedCustomer).map(key => [key, null])
        ) as typeof state.searchedCustomer;

        dispatch({
            type: 'setStateValues',
            values: {
                businessPartners: [],
                firstLoad: true,
                viewState: ViewState.SearchForm,
                searchCapCustomerType: SearchCapCustomerType.Email,
                searchedCustomer: initialSearchedCustomer,
            },
        });
    }, [dispatch, state.searchedCustomer]);

    const modalTitle = useMemo(() => {
        if (hasErrorWhenFetch) {
            return null;
        }

        if (submissionPurpose === SubmissionPurpose.Qualify) {
            return t('launchpadLeadDetails:qualifyModal.title');
        }

        let title: string;

        if (submissionPurpose === SubmissionPurpose.NewContact) {
            switch (viewState) {
                case ViewState.NewContact:
                    title = t('launchpadLeadList:newContactModal.kyc.title');
                    break;

                default:
                    title = t('launchpadLeadList:newContactModal.searchCustomer.title');
                    break;
            }
        } else {
            const transKey = `capApplication:${getModalLocales(viewState)}.title`;

            if (forCi) {
                title = t(`${transKey}.ci`);
            }

            switch (viewState) {
                case ViewState.BusinessPartner:
                case ViewState.SelectSalesperson:
                case ViewState.KYC:
                case ViewState.Lead: {
                    if (submissionPurpose === SubmissionPurpose.Resubmission) {
                        title = t(`${transKey}.admin.${lead?.isLead ? 'resubmitLead' : 'resubmitContact'}`);
                    } else if (submissionPurpose === SubmissionPurpose.NewSubmission && forCi) {
                        title = t(`${transKey}.ci`);
                    } else {
                        title = t(`${transKey}.admin.${lead?.isLead ? 'lead' : 'contact'}`);
                    }
                    break;
                }

                default:
                    title = t(`${transKey}.admin`);
                    break;
            }
        }

        if (shouldShowPorscheV3Button && viewState === ViewState.BusinessPartner) {
            return (
                <Space size="middle">
                    <span>{title}</span>
                    <PButton icon="search" onClick={resetToSearchCapCustomer} variant="ghost" compact>
                        {t('capApplication:searchAgain')}
                    </PButton>
                </Space>
            );
        }

        return title;
    }, [
        hasErrorWhenFetch,
        submissionPurpose,
        viewState,
        forCi,
        shouldShowPorscheV3Button,
        t,
        lead?.isLead,
        resetToSearchCapCustomer,
    ]);

    const modalCloseable = useMemo(() => {
        if (!hasErrorWhenFetch) {
            return closable;
        }

        if (hasErrorWhenFetch) {
            return false;
        }

        return viewState !== ViewState.SearchForm;
    }, [closable, hasErrorWhenFetch, viewState]);

    const modalInner = useMemo(() => {
        switch (viewState) {
            case ViewState.SearchForm:
                return <SearchCapCustomerForm dispatch={dispatch} state={state} />;

            case ViewState.BusinessPartner:
                return <BusinessPartnerList dispatch={dispatch} onLeadDetails={onLeadDetails} state={state} />;

            case ViewState.SelectSalesperson:
                return (
                    <Row gutter={[0, 24]}>
                        <SelectResponsibleSalesperson dispatch={dispatch} state={state} />
                    </Row>
                );

            case ViewState.KYC:
                return (
                    <QualifyModalInner
                        dispatch={dispatch}
                        formName={contactKycFormName}
                        formRef={formRef}
                        onClose={onClose}
                        state={state}
                    />
                );

            case ViewState.NewContact:
                return <NewContactKYC dispatch={dispatch} onClose={onClose} state={state} />;

            case ViewState.Lead:
                return <LeadList dispatch={dispatch} onApplicationDetails={onLeadDetails} state={state} />;

            default:
                return null;
        }
    }, [dispatch, onClose, onLeadDetails, state, viewState]);

    const bodyStyle = useMemo(() => {
        if (
            viewState === ViewState.SearchForm &&
            (forceUsingPorscheV3Components || router?.layout?.__typename === 'PorscheV3Layout')
        ) {
            return { marginBottom: '32px' };
        }

        if (viewState === ViewState.SearchForm) {
            return { marginBottom: '8px' };
        }

        return {};
    }, [forceUsingPorscheV3Components, router?.layout?.__typename, viewState]);

    const footerButton = useModalButtons({
        state,
        t,
        closable,
        onClose,
        onConfirmError,
        onCreateNew,
        onSubmit,
        forceUsingPorscheV3Components,
    });

    return (
        <Modal
            bodyStyle={bodyStyle}
            closable={modalCloseable}
            data-cy="bp-search-modal"
            footer={footerButton.length ? footerButton : null}
            maskClosable={modalCloseable}
            onCancel={onCancel}
            open={visible}
            title={modalTitle}
            width={modalWidth}
            centered
            destroyOnClose
            {...(modalClassName ? { className: modalClassName } : {})}
        >
            <>
                {isLoading && !batchSearch.searchInProgress && (
                    <center>
                        <LoadingElement />
                    </center>
                )}
                {modalInner}
                {contactKycConfirmationModal.render({
                    forCi,
                    formName: contactKycFormName,
                    onSubmit: kycFields.length === 0 ? submitWithoutKyc : undefined,
                })}
            </>
        </Modal>
    );
};

export default SearchCapCustomerModal;
