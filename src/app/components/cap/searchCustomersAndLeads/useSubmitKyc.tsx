import { ApolloError } from '@apollo/client';
import { omit } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useConfirmBookingApplicationMutation } from '../../../api/mutations/confirmBookingApplication';
import { QualifyLeadDocument, useQualifyLeadMutation } from '../../../api/mutations/qualifyLead';
import { useQualifyLeadWithCapValuesMutation } from '../../../api/mutations/qualifyLeadWithCapValues';
import { useResubmitLeadToCapMutation } from '../../../api/mutations/resubmitLeadToCap';
import { useUploadLeadDocumentMutation } from '../../../api/mutations/uploadLeadDocument';
import refineCustomerValues from '../../../pages/shared/refineCustomerValues';
import { useThemeComponents } from '../../../themes/hooks';
import { prepareKYCFieldPayload } from '../../../utilities/kycPresets';
import useHandleError from '../../../utilities/useHandleError';
import { useContactKycConfirmationModal } from './ContactKycSubmission/useContactKycConfirmationModal';
import type { QualifyContactValues } from './QualifyModalInner/types';
import { SubmissionPurpose } from './types';
import type { StateAndDispatch } from './useReducer';

type UseSubmitKycProps = {
    onClose: (navigateBack?: boolean) => void;
} & StateAndDispatch;

const useSubmitKyc = ({ state, dispatch, onClose }: UseSubmitKycProps) => {
    const {
        lead,
        loading: { loadingBP, loadingLead },
        supportingApplicationValue: { applicationId, stage },
        submissionPurpose,
        selectedBusinessPartner,
        selectedLead,
        selectedSalesperson,
    } = state;

    const { t } = useTranslation(['applicationDetails']);
    const { notification } = useThemeComponents();
    const contactKYCConfirmationModal = useContactKycConfirmationModal();

    const [qualifyLeadMutation] = useQualifyLeadMutation();
    const [qualifyLeadWithCapValuesMutation] = useQualifyLeadWithCapValuesMutation();
    const [confirmBookingMutation] = useConfirmBookingApplicationMutation();
    const [resubmitLeadToCap] = useResubmitLeadToCapMutation();
    const [uploadLeadDocument] = useUploadLeadDocumentMutation();

    const submitKyc = useHandleError<QualifyContactValues>(
        async values => {
            try {
                const { id: leadId } = lead;
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: true },
                });
                notification.loading({
                    content: t('applicationDetails:messages.qualifyApplication'),
                    duration: 0,
                    key: 'primary',
                });

                const { customerAssetArray } = refineCustomerValues(values.customer.fields);
                const documentPayloads = customerAssetArray
                    .map(asset =>
                        asset.files
                            .map(file => {
                                if (file instanceof File) {
                                    return { upload: file, kind: asset.type };
                                }

                                return null;
                            })
                            .filter(Boolean)
                    )
                    .flat();

                const mutation = () => {
                    const commonSubmissionValue = {
                        capValues: {
                            businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                            leadGuid: selectedLead?.leadGuid,
                        },
                        updatedKyc: prepareKYCFieldPayload(values.customer.fields),
                        updatedTradeInVehicle: values.tradeInVehicle,
                    };

                    switch (submissionPurpose) {
                        case SubmissionPurpose.Qualify:
                            return qualifyLeadWithCapValuesMutation({
                                variables: {
                                    leadId,
                                    selectedResponsibleSalesId: selectedSalesperson,
                                    ...commonSubmissionValue,
                                },
                            });

                        case SubmissionPurpose.ConfirmBooking:
                            return confirmBookingMutation({
                                variables: {
                                    applicationId,
                                    stage,
                                    ...commonSubmissionValue,
                                    qualifyValues: {
                                        ...omit(['vehicleModel', 'customer', 'tradeInVehicle'], values),
                                    },
                                },
                            });

                        case SubmissionPurpose.Resubmission:
                            return resubmitLeadToCap({
                                variables: {
                                    leadId,
                                    isTestDrive: false,
                                    ...commonSubmissionValue,
                                    resubmitQualifyValues: {
                                        ...omit(['vehicleModel', 'customer', 'tradeInVehicle'], values),
                                    },
                                },
                            });

                        default:
                            return null;
                    }
                };

                if (mutation) {
                    // First qualify the lead
                    const { errors } = await mutation();

                    if (!errors) {
                        // After successful qualification, upload documents
                        if (documentPayloads.length > 0) {
                            await Promise.all(
                                documentPayloads.map(payload =>
                                    uploadLeadDocument({
                                        variables: {
                                            leadId: lead.id,
                                            upload: payload.upload,
                                            kind: payload.kind,
                                            isTemporary: false,
                                        },
                                    })
                                )
                            );
                        }

                        notification.success({
                            content: t('applicationDetails:messages.qualifiedApplication'),
                            key: 'primary',
                        });

                        onClose(true);
                    }
                }
            } catch (error) {
                notification.destroy('primary');
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            } finally {
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: false },
                });
            }
        },
        [
            dispatch,
            loadingBP,
            loadingLead,
            notification,
            t,
            selectedBusinessPartner?.businessPartnerGuid,
            selectedLead?.leadGuid,
            submissionPurpose,
            qualifyLeadWithCapValuesMutation,
            lead,
            selectedSalesperson,
            confirmBookingMutation,
            applicationId,
            stage,
            resubmitLeadToCap,
            onClose,
            uploadLeadDocument,
        ]
    );

    const submitWithoutKyc = useCallback(async () => {
        try {
            const { id: leadId } = lead;
            dispatch({
                type: 'setLoading',
                loading: { loadingBP, loadingLead, submissionLoading: true },
            });
            notification.loading({
                content: t('applicationDetails:messages.qualifyApplication'),
                duration: 0,
                key: 'primary',
            });

            const commonSubmissionValue = {
                capValues: {
                    businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                    leadGuid: selectedLead?.leadGuid,
                },
                updatedKyc: prepareKYCFieldPayload({}), // Empty KYC values
                updatedTradeInVehicle: [],
            };

            const mutation = () => {
                switch (submissionPurpose) {
                    case SubmissionPurpose.Qualify:
                        return qualifyLeadWithCapValuesMutation({
                            variables: {
                                leadId,
                                selectedResponsibleSalesId: selectedSalesperson,
                                ...commonSubmissionValue,
                            },
                        });

                    case SubmissionPurpose.ConfirmBooking:
                        return confirmBookingMutation({
                            variables: {
                                applicationId,
                                stage,
                                ...commonSubmissionValue,
                            },
                        });

                    case SubmissionPurpose.Resubmission:
                        return resubmitLeadToCap({
                            variables: {
                                leadId,
                                isTestDrive: false,
                                ...commonSubmissionValue,
                            },
                        });

                    default:
                        return null;
                }
            };

            if (mutation) {
                const { errors } = await mutation();

                if (!errors) {
                    notification.success({
                        content: t('applicationDetails:messages.qualifiedApplication'),
                        key: 'primary',
                    });

                    // Close the modal and navigate
                    contactKYCConfirmationModal.close();
                    onClose(true);
                }
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error('Error in submitWithoutKyc:', error);
            }
        } finally {
            notification.destroy('primary');
            dispatch({
                type: 'setLoading',
                loading: { loadingBP, loadingLead, submissionLoading: false },
            });
        }
    }, [
        lead,
        dispatch,
        loadingBP,
        loadingLead,
        notification,
        t,
        selectedBusinessPartner?.businessPartnerGuid,
        selectedLead?.leadGuid,
        submissionPurpose,
        qualifyLeadWithCapValuesMutation,
        selectedSalesperson,
        confirmBookingMutation,
        applicationId,
        stage,
        resubmitLeadToCap,
        contactKYCConfirmationModal,
        onClose,
    ]);

    const qualifyLead = useHandleError(
        async (values: QualifyContactValues) => {
            try {
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: true },
                });

                notification.loading({
                    content: t('launchpadLeadDetails:qualifyModal.qualifyingContact'),
                    duration: 0,
                    key: 'primary',
                });

                const { customerAssetArray } = refineCustomerValues(values.customer.fields);
                const documentPayloads = customerAssetArray
                    .map(asset =>
                        asset.files
                            .map(file => {
                                if (file instanceof File) {
                                    return { upload: file, kind: asset.type };
                                }

                                return null;
                            })
                            .filter(Boolean)
                    )
                    .flat();

                // First qualify the lead
                const { errors } = await qualifyLeadMutation({
                    mutation: QualifyLeadDocument,
                    variables: {
                        leadId: lead.id,
                        qualifyValues: {
                            ...omit(['vehicleModel', 'customer', 'tradeInVehicle'], values),
                        },
                        updates: prepareKYCFieldPayload(values.customer.fields),
                        capValues: {
                            businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                            leadGuid: selectedLead?.leadGuid,
                        },
                    },
                });

                if (!errors) {
                    // After successful qualification, upload documents
                    if (documentPayloads.length > 0) {
                        await Promise.all(
                            documentPayloads.map(payload =>
                                uploadLeadDocument({
                                    variables: {
                                        leadId: lead.id,
                                        upload: payload.upload,
                                        kind: payload.kind,
                                        isTemporary: false,
                                    },
                                })
                            )
                        );
                    }

                    notification.success({
                        content: t('launchpadLeadDetails:qualifyModal.submissionCompleteMessage'),
                        key: 'primary',
                    });

                    onClose(true);
                }
            } catch (error) {
                notification.destroy('primary');

                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            } finally {
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: false },
                });
            }
        },
        [
            dispatch,
            loadingBP,
            loadingLead,
            notification,
            t,
            qualifyLeadMutation,
            lead,
            selectedBusinessPartner?.businessPartnerGuid,
            selectedLead?.leadGuid,
            onClose,
            uploadLeadDocument,
        ]
    );

    return { submitKyc, submitWithoutKyc, qualifyLead };
};

export default useSubmitKyc;
