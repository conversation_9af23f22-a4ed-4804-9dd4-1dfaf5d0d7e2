import { PRadioButtonWrapper } from '@porsche-design-system/components-react';
import { Col, Row, TablePaginationConfig, TableProps } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { CapBusinessPartnerFragment } from '../../../../api/fragments/CapBusinessPartner';
import { CapLeadFragment } from '../../../../api/fragments/CapLead';
import { ColumnsType } from '../../../../types/antd';
import { SearchPagination } from '../types';
import { CenteredCellWrapper, OptionsTableContainer, RecordCount, SelectedRecordCard, StyledOptionsTable } from '../ui';

type LeadRecordTableProps = {
    leads: CapLeadFragment[];
    businessPartnerInformation?: CapBusinessPartnerFragment;
    selectedLead?: CapLeadFragment;
    onSelectionChanged: (lead: CapLeadFragment) => void;
    forCi: boolean;
    pagination: SearchPagination;
    setPagination: Dispatch<SetStateAction<SearchPagination>>;
    totalLeadRecords: number;
};

const BusinessPartnerInformation = ({
    forCi,
    businessPartner,
}: {
    businessPartner: CapBusinessPartnerFragment;
    forCi: boolean;
}) => (
    <SelectedRecordCard forCi={forCi}>
        <p>{businessPartner.fullName}</p>
        <p>{businessPartner.email}</p>
        <p>
            {businessPartner.phone.prefix
                ? `(+${businessPartner.phone.prefix}) ${businessPartner.phone.value}`
                : businessPartner.phone.value}
        </p>
    </SelectedRecordCard>
);

const LeadRecordTable = ({
    leads,
    businessPartnerInformation,
    selectedLead,
    onSelectionChanged,
    forCi = true,
    pagination,
    setPagination,
    totalLeadRecords,
}: LeadRecordTableProps) => {
    const { t } = useTranslation('capApplication');

    const rowSelection: TableProps<CapLeadFragment>['rowSelection'] = {
        getCheckboxProps: (record: CapLeadFragment) => ({
            name: record.leadId,
        }),
        selectedRowKeys: selectedLead ? [selectedLead.leadId] : [],
        columnTitle: 'Select',
        type: 'radio',
        renderCell: (_, record, index) => (
            <CenteredCellWrapper>
                <PRadioButtonWrapper hideLabel={false}>
                    <input
                        checked={selectedLead?.leadId === record.leadId}
                        name="lead-selection"
                        onChange={() => onSelectionChanged(record)}
                        type="radio"
                    />
                </PRadioButtonWrapper>
            </CenteredCellWrapper>
        ),
    };

    const onPageChange = useCallback(
        (tableConfig: TablePaginationConfig) => {
            setPagination({
                page: tableConfig.current,
                limit: tableConfig.pageSize,
                offset: tableConfig.pageSize * (tableConfig.current - 1),
            });
        },
        [setPagination]
    );

    const columns: ColumnsType<CapLeadFragment> = [
        {
            title: t('capApplication:columns.lead.createdAt'),
            dataIndex: 'date',
            render: (createdAt: string) => (createdAt ? dayjs(createdAt).format('DD/MM/YYYY, HH:mm A') : '-'),
        },
        {
            title: t('capApplication:columns.lead.campaignId'),
            dataIndex: 'campaignId',
        },
        {
            title: t('capApplication:columns.lead.leadId'),
            dataIndex: 'leadId',
        },
        {
            title: t('capApplication:columns.lead.vehicleOfInterest'),
            dataIndex: 'interestVehicle',
        },
    ];

    if (leads.length === 0) {
        return (
            <OptionsTableContainer forCi={forCi}>
                <RecordCount style={{ padding: 0 }}>{t('capApplication:leadRecordFound', { count: 0 })}</RecordCount>
            </OptionsTableContainer>
        );
    }

    return (
        <Row gutter={[0, 24]} style={{ width: '100%' }}>
            <Col span={24}>
                <RecordCount>
                    {t(`capApplication:${leads.length === 1 ? 'leadRecordFound' : 'multipleLeadRecordsFound'}`, {
                        count: leads.length,
                    })}
                </RecordCount>
            </Col>
            <Col span={24}>
                <OptionsTableContainer forCi={forCi}>
                    {businessPartnerInformation && (
                        <BusinessPartnerInformation businessPartner={businessPartnerInformation} forCi={forCi} />
                    )}

                    <StyledOptionsTable
                        bordered={false}
                        columns={columns}
                        dataSource={leads}
                        forCi={forCi}
                        onChange={onPageChange}
                        pagination={
                            totalLeadRecords > 10
                                ? {
                                      current: pagination.page,
                                      pageSize: pagination.limit,
                                      total: totalLeadRecords,
                                      pageSizeOptions: [
                                          10,
                                          20,
                                          totalLeadRecords > 20 ? 50 : null,
                                          totalLeadRecords > 50 ? 100 : null,
                                      ].filter(Boolean),
                                  }
                                : false
                        }
                        rowKey="leadId"
                        rowSelection={rowSelection}
                        scroll={{ x: 'max-content' }}
                    />
                </OptionsTableContainer>
            </Col>
        </Row>
    );
};

export default LeadRecordTable;
