import { Col, Grid, Row } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { Dispatch, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { CompanyTheme } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import validators, { emailRegex } from '../../../utilities/validators';
import { useCompany } from '../../contexts/CompanyContextManager';
import { useRouter } from '../../contexts/shared';
import Form from '../../fields/Form';
import { ViewState } from './types';
import { EmptyCustomerWrapper, RecordCount } from './ui';
import { type State, type Action, SearchCapCustomerType } from './useReducer';
import { hasSearchedCustomerValues, validateSearchCriteria, validateSearchedMobileNo } from './utils';

type SearchTypeOption = {
    label: string;
    value: SearchCapCustomerType;
};

export type SearchCapCustomerFormValues = {
    customerName?: string;
    nric?: string;
    email?: string;
    mobileNo?: string;
    searchType?: SearchCapCustomerType;
    firstName?: string;
    lastName?: string;
    firstNameJapan?: string;
    lastNameJapan?: string;
    vin?: string;
    porscheId?: string;
};

const useSearchTypeOptions = () => {
    const { t } = useTranslation('capApplication');
    const company = useCompany(true);
    const isJapan = company?.countryCode === 'JP';

    return useMemo(
        (): SearchTypeOption[] => [
            { label: t('fields.searchType.options.email'), value: SearchCapCustomerType.Email },
            { label: t('fields.searchType.options.mobileNo'), value: SearchCapCustomerType.MobileNo },
            { label: t('fields.searchType.options.name'), value: SearchCapCustomerType.Name },
            ...(isJapan
                ? [
                      {
                          label: t('fields.searchType.options.phoneticName'),
                          value: SearchCapCustomerType.PhoneticName,
                      },
                  ]
                : []),
            { label: t('fields.searchType.options.vin'), value: SearchCapCustomerType.Vin },
            { label: t('fields.searchType.options.porscheId'), value: SearchCapCustomerType.PorscheId },
        ],
        [t, isJapan]
    );
};

const useFormResetOnTypeChange = (dispatch: Dispatch<Action>) => {
    const { resetForm } = useFormikContext<SearchCapCustomerFormValues>();

    return useCallback(
        (searchType: SearchCapCustomerType) => {
            resetForm({ values: { searchType } });

            dispatch({
                type: 'setSearchCapCustomerType',
                searchCapCustomerType: searchType,
            });
        },
        [resetForm, dispatch]
    );
};

// Sync formik values reset with reducer state reset when modal is closed
const useFormResetOnVisibilityChange = (visible: boolean) => {
    const { resetForm } = useFormikContext<SearchCapCustomerFormValues>();
    const prevVisibleRef = useRef(visible);

    useEffect(() => {
        const wasVisible = prevVisibleRef.current;
        const isVisible = visible;

        if (wasVisible && !isVisible) {
            resetForm({ values: { searchType: SearchCapCustomerType.Email } });
        }

        prevVisibleRef.current = isVisible;
    }, [visible, resetForm]);
};

const useFieldRenderer = () => {
    const { t } = useTranslation('capApplication');
    const {
        FormFields: { InputField: FieldComponent },
    } = useThemeComponents();

    const fieldProps = { removeWhiteSpace: true };

    return useCallback(
        (searchType: SearchCapCustomerType) => {
            switch (searchType) {
                case SearchCapCustomerType.Email:
                    return <FieldComponent name="email" {...fieldProps} />;

                case SearchCapCustomerType.MobileNo:
                    return <FieldComponent name="mobileNo" {...fieldProps} />;

                case SearchCapCustomerType.Name:
                    return (
                        <Row gutter={[16, 0]}>
                            <Col sm={12} xs={24}>
                                <FieldComponent {...t('fields.FirstName', { returnObjects: true })} name="firstName" />
                            </Col>
                            <Col sm={12} xs={24}>
                                <FieldComponent {...t('fields.LastName', { returnObjects: true })} name="lastName" />
                            </Col>
                        </Row>
                    );

                case SearchCapCustomerType.PhoneticName:
                    return (
                        <Row gutter={[16, 0]}>
                            <Col sm={12} xs={24}>
                                <FieldComponent
                                    {...t('fields.FirstNameJapan', { returnObjects: true })}
                                    name="firstNameJapan"
                                />
                            </Col>
                            <Col sm={12} xs={24}>
                                <FieldComponent
                                    {...t('fields.LastNameJapan', { returnObjects: true })}
                                    name="lastNameJapan"
                                />
                            </Col>
                        </Row>
                    );

                case SearchCapCustomerType.Vin:
                    return <FieldComponent name="vin" {...fieldProps} />;

                case SearchCapCustomerType.PorscheId:
                    return <FieldComponent name="porscheId" {...fieldProps} />;

                default:
                    return null;
            }
        },
        [FieldComponent, t]
    );
};

const SearchTypeSelect = ({ onChange }: { onChange: (value: SearchCapCustomerType) => void }) => {
    const { t } = useTranslation('capApplication');
    const {
        FormFields: { SelectField },
    } = useThemeComponents();
    const searchTypeOptions = useSearchTypeOptions();

    return (
        <SelectField
            {...t('fields.searchType', { returnObjects: true })}
            displayOptionsAsPopover={false}
            name="searchType"
            onChange={onChange}
            options={searchTypeOptions}
            placeholder={t('fields.searchType.placeholder')}
        />
    );
};

const InnerDrawer = ({ dispatch, state, visible }: { dispatch: Dispatch<Action>; state: State; visible: boolean }) => {
    const { Button } = useThemeComponents();
    const { t } = useTranslation('capApplication');
    const { values } = useFormikContext<SearchCapCustomerFormValues>();
    const { countryCode } = state;

    const isValidForSubmit = useMemo(
        () =>
            validateSearchCriteria(
                values.searchType,
                {
                    email: values.email,
                    phone: values.mobileNo,
                    firstName: values.firstName,
                    lastName: values.lastName,
                    firstNameJapan: values.firstNameJapan,
                    lastNameJapan: values.lastNameJapan,
                    vin: values.vin,
                    porscheId: values.porscheId,
                },
                countryCode
            ),
        [
            values.searchType,
            values.email,
            values.mobileNo,
            values.firstName,
            values.lastName,
            values.firstNameJapan,
            values.lastNameJapan,
            values.vin,
            values.porscheId,
            countryCode,
        ]
    );

    useFormResetOnVisibilityChange(visible);
    const handleSearchTypeChange = useFormResetOnTypeChange(dispatch);
    const renderField = useFieldRenderer();

    return (
        <>
            <Row align="bottom" gutter={[16, 10]}>
                <Col sm={8} xs={24}>
                    <SearchTypeSelect onChange={handleSearchTypeChange} />
                </Col>
                <Col sm={16} xs={24}>
                    {renderField(values.searchType)}
                </Col>
            </Row>
            <Button
                key="submit"
                disabled={!isValidForSubmit}
                form="searchCapCustomerForm"
                htmlType="submit"
                type="secondary"
            >
                {t('searchDrawer.buttons.search')}
            </Button>
        </>
    );
};

const InnerModal = ({ dispatch, visible }: { dispatch: Dispatch<Action>; visible: boolean }) => {
    const { useBreakpoint } = Grid;
    const screens = useBreakpoint();
    const { values } = useFormikContext<SearchCapCustomerFormValues>();
    useFormResetOnVisibilityChange(visible);
    const handleSearchTypeChange = useFormResetOnTypeChange(dispatch);
    const renderField = useFieldRenderer();

    useEffect(() => {
        dispatch({
            type: 'setSearchedCustomer',
            searchedCustomer: {
                email: values.email,
                phone: values.mobileNo,
                firstName: values.firstName,
                lastName: values.lastName,
                firstNameJapan: values.firstNameJapan,
                lastNameJapan: values.lastNameJapan,
                vin: values.vin,
                porscheId: values.porscheId,
            },
        });
    }, [dispatch, values]);

    return (
        <Row align="top" gutter={[16, 10]}>
            <Col sm={8} xs={24}>
                <SearchTypeSelect onChange={handleSearchTypeChange} />
            </Col>
            <Col sm={16} xs={24}>
                <div style={{ marginTop: screens.sm ? '28px' : '0px' }}>{renderField(values.searchType)}</div>
            </Col>
        </Row>
    );
};

const EmptyCustomerWrapperWithMargin = styled(EmptyCustomerWrapper)`
    margin-bottom: 24px;
`;

const NoResultsMessage = ({ state }: { state: State }) => {
    const { t } = useTranslation('capApplication');

    if (state.businessPartners.length === 0 && !state.firstLoad) {
        return (
            <EmptyCustomerWrapperWithMargin>
                <RecordCount>{t('customerRecordFound', { count: 0 })}</RecordCount>
                <RecordCount>{t('searchUsingOtherOptions')}</RecordCount>
            </EmptyCustomerWrapperWithMargin>
        );
    }

    return null;
};

const SearchCapCustomerForm = ({ dispatch, state }: { dispatch: Dispatch<Action>; state: State }) => {
    const { theme } = useThemeComponents();
    const { t } = useTranslation(['common']);
    const { visible, searchedCustomer, searchCapCustomerType, businessPartners, firstLoad, countryCode } = state;

    const validateSearchInput = useValidator(
        validators.compose(
            validators.only(
                () => searchCapCustomerType === SearchCapCustomerType.PorscheId,
                validators.custom('porscheId', (value: string) =>
                    value && emailRegex.test(value) ? null : t('formErrors.invalidPorscheId')
                )
            ),
            validators.only(
                () => searchCapCustomerType === SearchCapCustomerType.Email,
                validators.validEmail('email')
            ),
            validators.only(
                () => searchCapCustomerType === SearchCapCustomerType.MobileNo,
                validators.custom('mobileNo', (value: string) =>
                    value && validateSearchedMobileNo(value, countryCode) ? null : t('formErrors.invalidPhone')
                )
            )
        )
    );
    const onSubmit = useHandleError<SearchCapCustomerFormValues>(
        async values => {
            const searchedCustomer = {
                email: values.email,
                phone: values.mobileNo,
                firstName: values.firstName,
                lastName: values.lastName,
                firstNameJapan: values.firstNameJapan,
                lastNameJapan: values.lastNameJapan,
                vin: values.vin,
                porscheId: values.porscheId,
            };

            if (!validateSearchCriteria(values.searchType, searchedCustomer, countryCode)) {
                return;
            }

            dispatch({
                type: 'setStateValues',
                values: {
                    viewState: ViewState.BusinessPartner,
                    searchedCustomer,
                    searchCapCustomerType: values.searchType,
                },
            });
        },
        [dispatch]
    );

    const initialValues = useMemo((): SearchCapCustomerFormValues => {
        const shouldRestoreSearchValues =
            businessPartners.length === 0 && !firstLoad && hasSearchedCustomerValues(searchedCustomer);

        if (shouldRestoreSearchValues) {
            const { email, phone, firstName, lastName, firstNameJapan, lastNameJapan, vin, porscheId } =
                searchedCustomer;

            // Retain search state when no results found
            return {
                searchType: searchCapCustomerType,
                email,
                mobileNo: phone,
                firstName,
                lastName,
                firstNameJapan,
                lastNameJapan,
                vin,
                porscheId,
            };
        }

        return { searchType: searchCapCustomerType };
    }, [businessPartners.length, firstLoad, searchedCustomer, searchCapCustomerType]);

    const router = useRouter();

    const formInner = useMemo(
        () =>
            theme === CompanyTheme.PorscheV3 || router?.layout?.__typename === 'PorscheV3Layout' ? (
                <InnerModal dispatch={dispatch} visible={visible} />
            ) : (
                <InnerDrawer dispatch={dispatch} state={state} visible={visible} />
            ),
        [theme, router?.layout?.__typename, dispatch, visible, state]
    );

    return (
        <Formik<SearchCapCustomerFormValues>
            key="searchCapCustomerForm"
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={validateSearchInput}
        >
            {({ handleSubmit }) => (
                <Form
                    colon={false}
                    id="searchCapCustomerForm"
                    name="searchCapCustomerForm"
                    onSubmitCapture={handleSubmit}
                >
                    <NoResultsMessage state={state} />
                    {formInner}
                </Form>
            )}
        </Formik>
    );
};

export default SearchCapCustomerForm;
