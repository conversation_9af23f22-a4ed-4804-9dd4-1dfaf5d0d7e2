import type { CapBusinessPartnerFragment } from '../../../api/fragments/CapBusinessPartner';
import type { CapLeadFragment } from '../../../api/fragments/CapLead';
import { KycExtraSettingsSpecsFragment } from '../../../api/fragments/KYCExtraSettingsSpecs';
import { type TradeInVehiclePayload } from '../../../api/types';
import { type KYCPresetFormFields } from '../../../utilities/kycPresets';

export enum ButtonClickedAction {
    CreateNewBP = 'createNewBP',
    CreateNewLead = 'createNewLead',
    SelectBP = 'selectBP',
    SelectLead = 'selectLead',
    ErrorSearchConfirmed = 'errorSearchConfirmed',
}

export enum ViewState {
    SearchForm = 'searchForm',
    BusinessPartner = 'businessPartner',
    SelectSalesperson = 'selectSalesperson',
    KYC = 'kyc',
    Lead = 'lead',
    NewContact = 'newContact',
}

export enum SubmissionPurpose {
    NewSubmission = 'newSubmission',
    Qualify = 'qualify',
    ConfirmBooking = 'confirmBooking',
    Resubmission = 'resubmission',
    NewContact = 'newContact',
}

type CreateNewBPButtonClicked = {
    action: ButtonClickedAction.CreateNewBP;
    selectedResponsibleSalespersonId: string;
};

type CreateNewLeadButtonClicked = {
    action: ButtonClickedAction.CreateNewLead;
    selectedBusinessPartner: CapBusinessPartnerFragment;
    selectedResponsibleSalespersonId: string;
};

type SelectBPButtonClicked = {
    action: ButtonClickedAction.SelectBP;
    selectedBusinessPartner: CapBusinessPartnerFragment;
    selectedResponsibleSalespersonId: string;
};

type SelectLeadButtonClicked = {
    action: ButtonClickedAction.SelectLead;
    selectedBusinessPartner: CapBusinessPartnerFragment;
    selectedLead: CapLeadFragment;
    selectedResponsibleSalespersonId: string;
};

type ErrorSearchConfirmed = {
    action: ButtonClickedAction.ErrorSearchConfirmed;
};

export type ButtonClickedData =
    | CreateNewBPButtonClicked
    | CreateNewLeadButtonClicked
    | SelectBPButtonClicked
    | SelectLeadButtonClicked
    | ErrorSearchConfirmed;

type BusinessPartnerSelected = {
    selectedValue: ViewState.BusinessPartner;
    selectedBusinessPartner: CapBusinessPartnerFragment;
};

type LeadSelected = {
    selectedValue: ViewState.Lead;
    selectedBusinessPartner: CapBusinessPartnerFragment;
    selectedLead: CapLeadFragment;
};

export type SelectedCapValues = BusinessPartnerSelected | LeadSelected;

export type SearchCapCustomerKYCProps = {
    closable?: boolean;
    onClose: (navigateBack?: boolean) => void;
    onButtonClicked: (data: ButtonClickedData) => void;
    onCustomerSearchPerformed?: (isPerformed: boolean) => void;
};

export type SearchPagination = {
    page: number;
    limit: number;
    offset: number;
};

export type ContactKycSupportingProps = {
    kycExtraSettings: KycExtraSettingsSpecsFragment;
    countryCode: string;
};

export type ContactKycFormProps = {
    customer: { fields: KYCPresetFormFields };
    tradeInVehicle: TradeInVehiclePayload[];
};
