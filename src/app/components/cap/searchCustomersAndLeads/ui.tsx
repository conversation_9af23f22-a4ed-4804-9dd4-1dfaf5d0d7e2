import { CloseOutlined } from '@ant-design/icons';
import { Col, Space, Table } from 'antd';
import styled from 'styled-components';
import ThemeDrawer from '../../ThemeDrawer';
import FormItem from '../../fields/FormItem';
import InputField from '../../fields/InputField';

export const SearchCustomerDrawer = styled(ThemeDrawer)<{ $withHeader?: boolean }>`
    & > .ant-drawer-content-wrapper {
        width: clamp(300px, 100vw, 736px) !important;
    }

    & .ant-drawer-body {
        padding: ${({ $withHeader }) => ($withHeader ? '0 60px' : '60px')};
    }

    & .ant-drawer-wrapper-body {
        background-color: #ffffff;
    }

    & .ant-drawer-header {
        border-bottom: none;

        & .ant-drawer-close {
            margin-right: -9px;
        }

        & .ant-drawer-close .anticon-close {
            color: #000;
        }
    }

    & .ant-drawer-header-title {
        flex-direction: row-reverse;
    }

    & .ant-drawer-footer {
        background-color: #ffffff;
        border-top: none;
        box-shadow: 0px 0px 60px #0000001a;
        min-height: 85px;
        display: flex;
        justify-items: center;
        align-items: center;
    }
`;

SearchCustomerDrawer.defaultProps = {
    $withHeader: true,
};

export const DrawerFooter = styled(Space)`
    width: 100%;
    justify-content: end;
`;

export const StyledFormItem = styled(FormItem)`
    & .ant-form-item-label {
        & > label {
            font-size: 14px;
            line-height: 22px;
        }
    }
`;

export const SearchTitle = styled.h4`
    font-size: 20px;
    line-height: 32px;
    font-weight: bold;
    color: var(--ant-secondary-color);
    margin-bottom: 16px;
`;

export const StyledCol = styled(Col)`
    & .ant-form-item {
        background-color: rgba(0, 0, 0, 0.07);
        padding: 10px 16px;
        margin-bottom: 0;
    }
`;

export const StyledCloseIcon = styled(CloseOutlined)`
    svg {
        color: #000;
        width: 14px;
        height: 14px;
    }
`;

export const StyledSearchInput = styled(InputField)`
    font-size: 16px;
    color: var(--ant-primary-color);

    &.ant-input-affix-wrapper,
    & .ant-input {
        padding: 0;
        color: var(--ant-primary-color);
    }

    & .ant-input-clear-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &.ant-input-disabled {
        color: #000;
        cursor: text;
    }
`;

StyledSearchInput.defaultProps = {
    allowClear: {
        clearIcon: <StyledCloseIcon />,
    },
    bordered: false,
};

export const ErrorMessage = styled.span`
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;

    gap: 8px;
    display: flex;
    color: #cc1922;

    & div.message {
        display: flex;
        align-items: center;
    }
`;

export const RecordCount = styled.span`
    font-size: 16px;
`;

export const EmptyCustomerWrapper = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
`;

export const FieldContainer = styled.div`
    & .ant-form-item {
        margin-bottom: 0px;
    }
`;

export const OptionsTableContainer = styled.div<{ forCi?: boolean }>`
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    text-align: start;
    width: 100%;

    padding-bottom: ${({ forCi }) => (forCi ? 'calc(32px - clamp(8px, 6px + 0.5vw, 16px))' : '0px')};

    & .ant-table-wrapper {
        margin-top: 8px;
        width: 100%;
    }
`;

export const StyledOptionsTable = styled(Table)<{ forCi?: boolean }>`
    & .ant-table-thead {
        > tr > th {
            font-size: 16px;
            color: #000;
            border: none;
            border-bottom: 1px solid #c4c4c4;
            font-weight: bold;

            ${({ forCi }) =>
                forCi &&
                `
                ::before {
                    display: none;
                }

                background-color: #fff;
            `}
        }
    }

    & .ant-table-tbody {
        > tr > td,
        > tr.ant-table-row-selected > td {
            font-size: 16px;
            border-bottom: 1px solid #c4c4c4;
            background-color: #fff;
        }
    }

    & .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus + .ant-radio-inner {
        box-shadow: none;
        border-color: #000;
    }

    & .ant-radio-wrapper-checked {
        & .ant-radio-inner {
            border-color: #000;

            ::after {
                background-color: #000;
            }
        }

        & .ant-radio-checked::after {
            border: #000;
        }
    }

    & .ant-table-cell.ant-table-selection-column {
        padding: 16px;
    }

    .disabled-row,
    .disabled-row:hover {
        .ant-table-cell {
            background-color: rgb(245, 245, 245);
        }
    }

    & .ant-form-item {
        margin: 0px;
        & .ant-select-selector {
            height: auto;
            font-size: 16px;
            border-radius: 4px;
            border: 2px solid #010205;

            & .ant-select-selection-placeholder,
            .ant-select-selection-item {
                padding: 12px 0px;
                color: #010205;
            }

            & .ant-select-selection-search {
                input {
                    height: auto;
                    padding: 12px 0px;
                }
            }
        }
    }

    .disabled-row,
    .disabled-row:hover {
        .ant-table-cell {
            background-color: rgb(245, 245, 245);
        }
    }
`;

export const SelectedRecordCard = styled.div<{ forCi?: boolean }>`
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border: 1px solid #d8d8db;
    border-radius: 4px;
    padding: 16px;
    margin-top: 0px;
    margin-bottom: ${({ forCi }) => (forCi ? '8px' : '24px')};

    & p {
        margin: 0;
        font-size: 16px;
        line-height: 22px;
        color: #000;
        opacity: 0.85;
    }

    & p:first-of-type {
        margin-bottom: 8px;
        font-weight: bold;
        opacity: 1;
    }
`;

export const CenteredCellWrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
`;
