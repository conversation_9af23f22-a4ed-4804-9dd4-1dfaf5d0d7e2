import { Row, Col } from 'antd';
import { Dispatch, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetCapBusinessPartnersQuery } from '../../../../api/queries/getCapBusinessPartners';
import { BusinessPartnerSearchField } from '../../../../api/types';
import { useCompany } from '../../../contexts/CompanyContextManager';
import CapErrorMessage from '../CapErrorMessage';
import { SearchPagination, ViewState } from '../types';
import useBatchBusinessPartnerSearch from '../useBatchBusinessPartnerSearch';
import { SearchCapCustomerType, type Action, type State } from '../useReducer';
import { useIsLoading, validateSearchCriteria } from '../utils';
import BatchSearchProgress from './BatchSearchProgress';
import CustomerRecordTable from './CustomerRecordTable';

type BPListProps = {
    state: State;
    dispatch: Dispatch<Action>;
    onLeadDetails: boolean;
};

const BusinessPartnerList = ({ state, dispatch, onLeadDetails }: BPListProps) => {
    const { t } = useTranslation('capApplication');
    const [pagination, setPagination] = useState<SearchPagination>({
        page: 1,
        limit: 10,
        offset: 0,
    });

    // for search customer with no lead data
    const company = useCompany(true);
    const isJapan = company?.countryCode === 'JP';

    const {
        lead,
        applicationModuleId,
        capModuleId,
        eventId,
        dealerId,
        searchedCustomer: { email, phone, firstName, lastName, firstNameJapan, lastNameJapan, vin, porscheId },
        loading: { loadingLead, submissionLoading },
        searchCapCustomerType,
        forCi,
        loading,
        businessPartners,
        searchBPError,
        batchSearch,
        countryCode,
    } = state;

    const searchFromLead = useMemo(() => {
        if (onLeadDetails) {
            return !!lead?.id;
        }

        return !!(lead?.id && lead?.capValues);
    }, [lead?.capValues, lead?.id, onLeadDetails]);

    const hasSearchCriteria = useMemo(
        () =>
            validateSearchCriteria(
                searchCapCustomerType,
                {
                    email,
                    phone,
                    firstName,
                    lastName,
                    firstNameJapan,
                    lastNameJapan,
                    vin,
                    porscheId,
                },
                countryCode
            ),
        [
            searchCapCustomerType,
            email,
            phone,
            firstName,
            lastName,
            firstNameJapan,
            lastNameJapan,
            vin,
            porscheId,
            countryCode,
        ]
    );

    const [skipForGetBPFromLead, skipForGetBPFromQuery] = useMemo(
        () => [
            !searchFromLead || !capModuleId || !!lead?.capValues?.businessPartnerGuid,
            !forCi || !hasSearchCriteria || !capModuleId || !!lead?.capValues?.businessPartnerGuid,
        ],
        [capModuleId, hasSearchCriteria, forCi, lead?.capValues?.businessPartnerGuid, searchFromLead]
    );

    const eventIdForBpSearch = useMemo(() => {
        if (lead?.__typename === 'EventLead' && lead?.eventId) {
            return lead.eventId;
        }

        return eventId;
    }, [eventId, lead]);

    // Build search fields array based on available data
    const searchFields = useMemo(() => {
        const fields = [];

        if (email) {
            fields.push(BusinessPartnerSearchField.Email);
        }
        if (phone) {
            fields.push(BusinessPartnerSearchField.Phone);
        }
        if (firstName && lastName) {
            fields.push(BusinessPartnerSearchField.FirstAndLastName);
        }
        if (firstNameJapan && lastNameJapan && isJapan) {
            fields.push(BusinessPartnerSearchField.FirstAndLastNameJapan);
        }
        if (vin) {
            fields.push(BusinessPartnerSearchField.Vin);
        }
        if (porscheId) {
            fields.push(BusinessPartnerSearchField.PorscheId);
        }

        return fields;
    }, [email, phone, firstName, lastName, firstNameJapan, lastNameJapan, isJapan, vin, porscheId]);

    const { startBatchSearch, shouldUseBatchSearch, SEARCH_BATCHES } = useBatchBusinessPartnerSearch({
        state,
        dispatch,
        onLeadDetails,
        pagination: {
            limit: pagination.limit,
            offset: pagination.offset,
        },
    });

    const getQueryParameters = useMemo(() => {
        const baseQuery = {
            phone,
            firstName,
            lastName,
            firstNameJapan,
            lastNameJapan,
            vin,
        };

        if (searchCapCustomerType === SearchCapCustomerType.PorscheId && porscheId) {
            return { ...baseQuery, email: porscheId };
        }

        return { ...baseQuery, email };
    }, [searchCapCustomerType, email, porscheId, phone, firstName, lastName, firstNameJapan, lastNameJapan, vin]);

    const {
        data: BPFromInputSearchData,
        loading: loadingBPFromInputSearch,
        error: errorBPFromInputSearch,
    } = useGetCapBusinessPartnersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            applicationModuleId,
            capModuleId,
            eventId: eventIdForBpSearch,
            dealerId,
            query: getQueryParameters,
            searchFields,
            pagination: {
                limit: pagination.limit,
                offset: pagination.offset,
            },
        },
        skip: skipForGetBPFromQuery,
    });

    useEffect(() => {
        if (
            shouldUseBatchSearch &&
            !skipForGetBPFromLead &&
            !batchSearch.searchInProgress &&
            !batchSearch.hasResults &&
            batchSearch.batchesCompleted.length === 0
        ) {
            startBatchSearch();
        }
    }, [
        shouldUseBatchSearch,
        skipForGetBPFromLead,
        batchSearch.searchInProgress,
        batchSearch.hasResults,
        batchSearch.batchesCompleted.length,
        startBatchSearch,
    ]);

    useEffect(() => {
        dispatch({
            type: 'setLoading',
            loading: {
                loadingBP: batchSearch.searchInProgress || loadingBPFromInputSearch,
                loadingLead,
                submissionLoading,
            },
        });
    }, [
        dispatch,
        shouldUseBatchSearch,
        batchSearch.searchInProgress,
        loadingBPFromInputSearch,
        loadingLead,
        submissionLoading,
    ]);

    useEffect(() => {
        if (errorBPFromInputSearch) {
            dispatch({
                type: 'setSearchBPError',
                searchBPError: true,
            });
        } else {
            dispatch({
                type: 'setSearchBPError',
                searchBPError: false,
            });
        }
    }, [dispatch, errorBPFromInputSearch]);

    useEffect(() => {
        if (shouldUseBatchSearch) {
            return;
        }

        if (!loadingBPFromInputSearch && BPFromInputSearchData?.businessPartners?.items?.length) {
            dispatch({
                type: 'setStateValues',
                values: {
                    businessPartners: BPFromInputSearchData.businessPartners.items,
                    firstLoad: false,
                },
            });

            return;
        }

        if (!batchSearch.searchInProgress && !loadingBPFromInputSearch) {
            if (hasSearchCriteria) {
                // if search triggered from SearchCapCustomerForm, indicates no results found on SearchCapCustomerForm
                dispatch({
                    type: 'setStateValues',
                    values: {
                        businessPartners: [],
                        firstLoad: false,
                        viewState: ViewState.SearchForm,
                    },
                });
            } else {
                dispatch({
                    type: 'setStateValues',
                    values: {
                        businessPartners: [],
                        firstLoad: false,
                    },
                });
            }
        }
    }, [
        shouldUseBatchSearch,
        BPFromInputSearchData,
        dispatch,
        lead?.id,
        loadingBPFromInputSearch,
        batchSearch.searchInProgress,
        hasSearchCriteria,
    ]);

    const isLoading = useIsLoading(loading);

    const showBatchProgress = shouldUseBatchSearch && batchSearch.wasStarted;

    if (isLoading && !showBatchProgress) {
        return null;
    }

    if (isLoading && showBatchProgress) {
        return (
            <BatchSearchProgress resultCount={businessPartners?.length} searchBatches={SEARCH_BATCHES} state={state} />
        );
    }

    if (searchBPError) {
        return (
            <CapErrorMessage
                message={t(`capApplication:${onLeadDetails ? 'customerSearchError' : 'customerSearchErrorOnKYC'}`)}
            />
        );
    }

    return (
        <Row gutter={[14, 14]}>
            {showBatchProgress && (
                <Col span={24}>
                    <BatchSearchProgress
                        resultCount={businessPartners?.length}
                        searchBatches={SEARCH_BATCHES}
                        state={state}
                    />
                </Col>
            )}
            <Col span={24}>
                <CustomerRecordTable
                    dispatch={dispatch}
                    forCi={forCi}
                    pagination={pagination}
                    setPagination={setPagination}
                    showRecordCount={!showBatchProgress}
                    state={state}
                    totalBpRecords={businessPartners.length}
                />
            </Col>
        </Row>
    );
};

export default BusinessPartnerList;
