import { PRadioButtonWrapper } from '@porsche-design-system/components-react';
import { Row, Col, TableProps } from 'antd';
import { isEmpty } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { CapBusinessPartnerFragment } from '../../../../api/fragments/CapBusinessPartner';
import { ColumnsType } from '../../../../types/antd';
import SelectResponsibleSalesperson from '../SelectResponsibleSalesperson';
import { SearchPagination, SubmissionPurpose } from '../types';
import {
    CenteredCellWrapper,
    EmptyCustomerWrapper,
    OptionsTableContainer,
    RecordCount,
    StyledOptionsTable,
} from '../ui';
import { StateProps } from '../useReducer';

type CustomerRecordTableProps = StateProps & {
    forCi?: boolean;
    pagination: SearchPagination;
    setPagination: Dispatch<SetStateAction<SearchPagination>>;
    totalBpRecords: number;
    showRecordCount?: boolean;
};

const StyledRow = styled(Row)`
    position: relative;
`;

const CustomerRecordTable = ({
    state,
    dispatch,
    forCi = true,
    pagination,
    setPagination,
    totalBpRecords,
    showRecordCount = true,
}: CustomerRecordTableProps) => {
    const { t } = useTranslation(['common', 'capApplication']);

    const { businessPartners: customers, selectedBusinessPartner: selectedCustomer, lead, submissionPurpose } = state;

    const [modifiedResponsibleSalesperson, setModifiedResponsibleSalesperson] = useState<
        { businessPartnerGuid: string; internalAssigneeId: string }[]
    >([]);

    const onBusinessPartnerSelected = useCallback(
        (record: CapBusinessPartnerFragment) => {
            dispatch({ type: 'setSelectedBusinessPartner', selectedBusinessPartner: record });

            const assigneeDetailsFromCap = !isEmpty(record.responsibleSalesPersonId)
                ? lead?.availableAssignees?.find(assignee => assignee.alias === record.responsibleSalesPersonId)
                : null;

            if (assigneeDetailsFromCap) {
                dispatch({ type: 'setSelectedSalesperson', selectedSalesperson: assigneeDetailsFromCap.id });

                return;
            }

            const assigneeDetailsFromModifiedRow = modifiedResponsibleSalesperson.length
                ? modifiedResponsibleSalesperson.find(
                      responsibleSalesperson =>
                          responsibleSalesperson.businessPartnerGuid === record.businessPartnerGuid
                  )
                : null;

            dispatch({
                type: 'setSelectedSalesperson',
                selectedSalesperson: assigneeDetailsFromModifiedRow?.internalAssigneeId,
            });
        },
        [lead?.availableAssignees, dispatch, modifiedResponsibleSalesperson]
    );

    const rowSelection: TableProps<CapBusinessPartnerFragment>['rowSelection'] = {
        getCheckboxProps: (record: CapBusinessPartnerFragment) => ({
            name: record.businessPartnerId,
        }),
        selectedRowKeys: selectedCustomer ? [selectedCustomer.businessPartnerId] : [],
        columnTitle: 'Select',
        type: 'radio',
        renderCell: (_, record, index) =>
            record.isActive && (
                <CenteredCellWrapper>
                    <PRadioButtonWrapper hideLabel={false}>
                        <input
                            checked={selectedCustomer?.businessPartnerId === record.businessPartnerId}
                            name="customer-selection"
                            onChange={() => onBusinessPartnerSelected(record)}
                            type="radio"
                        />
                    </PRadioButtonWrapper>
                </CenteredCellWrapper>
            ),
    };

    // DO NOT REMOVE: Temporary this function to disable pagination when search BP
    // const onPageChange = useCallback(
    //     (tableConfig: TablePaginationConfig) => {
    //         setPagination({
    //             page: tableConfig.current,
    //             limit: tableConfig.pageSize,
    //             offset: tableConfig.pageSize * (tableConfig.current - 1),
    //         });
    //     },
    //     [setPagination]
    // );

    const columns: ColumnsType<CapBusinessPartnerFragment> = useMemo(
        () => [
            {
                title: t('capApplication:columns.customer.BpId'),
                dataIndex: 'businessPartnerId',
            },
            {
                title: t('capApplication:columns.customer.Name'),
                dataIndex: 'fullName',
            },
            {
                title: t('capApplication:columns.customer.Email'),
                dataIndex: 'email',
            },
            {
                title: t('capApplication:columns.customer.Mobile'),
                dataIndex: 'phone',
                render: value => (value.prefix ? `(+${value.prefix}) ${value.value}` : value.value),
            },
            {
                title: t('capApplication:columns.customer.ResponsibleSalesPerson'),
                dataIndex: 'responsibleSalesPerson',
                render: (value, record) => value || record.responsibleSalesPersonId,
            },
        ],
        [t]
    );

    if (totalBpRecords === 0) {
        if (!showRecordCount) {
            return null;
        }

        const emptyRecordFoundCopywriting = t('capApplication:customerRecordFound', { count: 0 });

        if (forCi) {
            return (
                <EmptyCustomerWrapper>
                    <RecordCount>{emptyRecordFoundCopywriting}</RecordCount>
                </EmptyCustomerWrapper>
            );
        }

        return (
            <Row gutter={[0, 24]}>
                <Col span={24}>
                    <RecordCount>{emptyRecordFoundCopywriting}</RecordCount>
                </Col>
                <SelectResponsibleSalesperson dispatch={dispatch} state={state} />
            </Row>
        );
    }

    return (
        <StyledRow className="customer-record-table" gutter={[0, 24]}>
            {showRecordCount && (
                <Col span={24}>
                    <RecordCount>
                        {t(
                            `capApplication:${totalBpRecords === 1 ? 'customerRecordFound' : 'multipleCustomerRecordsFound'}`,
                            { count: totalBpRecords }
                        )}
                    </RecordCount>
                </Col>
            )}
            <Col span={24}>
                <OptionsTableContainer forCi={forCi}>
                    <StyledOptionsTable
                        bordered={false}
                        columns={columns}
                        dataSource={customers}
                        forCi={forCi}
                        // DO NOT REMOVE: Temporary commenting this to disable pagination when search BP
                        // onChange={onPageChange}
                        // pagination={
                        //     totalBpRecords > 10
                        //         ? {
                        //               current: pagination.page,
                        //               pageSize: pagination.limit,
                        //               total: totalBpRecords,
                        //               pageSizeOptions: [
                        //                   10,
                        //                   20,
                        //                   totalBpRecords > 20 ? 50 : null,
                        //                   totalBpRecords > 50 ? 100 : null,
                        //               ].filter(Boolean),
                        //           }
                        //         : false
                        // }
                        rowClassName={(record: CapBusinessPartnerFragment) =>
                            !record.isActive ? 'disabled-row' : null
                        }
                        rowKey="businessPartnerId"
                        rowSelection={rowSelection}
                        scroll={{ x: 'max-content' }}
                    />
                </OptionsTableContainer>
            </Col>
            {selectedCustomer && submissionPurpose !== SubmissionPurpose.NewSubmission && (
                <Col span={24}>
                    <SelectResponsibleSalesperson dispatch={dispatch} state={state} />
                </Col>
            )}
        </StyledRow>
    );
};

export default CustomerRecordTable;
