import { type CountryCode, isValidNumber } from 'libphonenumber-js';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { LeadDataFragment } from '../../../api/fragments/LeadData';
import { useGetCapBusinessPartnerDetailsQuery } from '../../../api/queries/getCapBusinessPartnerDetails';
import { ApplicationStage, LeadType, ModuleType } from '../../../api/types';
import { emailRegex } from '../../../utilities/validators';
import { ViewState } from './types';
import { LoadingState, SearchCapCustomerType, type SearchedCustomer } from './useReducer';

export const validateSearchedMobileNo = (mobileNo: string, countryCode: string): boolean =>
    mobileNo && isValidNumber(mobileNo, countryCode as CountryCode);

export const hasSearchedCustomerValues = (searchedCustomer: SearchedCustomer): boolean =>
    Object.values(searchedCustomer).some(value => value !== null && value !== undefined && value !== '');

export const validateSearchCriteria = (
    searchCapCustomerType: SearchCapCustomerType,
    searchedCustomer: SearchedCustomer,
    countryCode: string
): boolean => {
    switch (searchCapCustomerType) {
        case SearchCapCustomerType.Email:
            return !!searchedCustomer.email && emailRegex.test(searchedCustomer.email);

        case SearchCapCustomerType.PorscheId:
            return !!searchedCustomer.porscheId && emailRegex.test(searchedCustomer.porscheId);

        case SearchCapCustomerType.MobileNo: {
            return validateSearchedMobileNo(searchedCustomer.phone, countryCode);
        }

        case SearchCapCustomerType.Name:
            return !!searchedCustomer.firstName && !!searchedCustomer.lastName;

        case SearchCapCustomerType.PhoneticName:
            return !!searchedCustomer.firstNameJapan && !!searchedCustomer.lastNameJapan;

        case SearchCapCustomerType.Vin:
            return !!searchedCustomer.vin;

        default:
            return false;
    }
};

export const getModalLocales = (viewState: ViewState) => {
    switch (viewState) {
        case ViewState.SearchForm:
            return 'searchCustomer';

        case ViewState.BusinessPartner:
        case ViewState.SelectSalesperson:
        case ViewState.KYC:
            return 'availableBPModal';

        case ViewState.Lead:
            return 'availableLeadModal';

        default:
            return '';
    }
};

export const useExistingBusinessPartner = ({
    businessPartnerGuid,
    applicationModuleId,
    eventId,
    capModuleId,
    forCi,
}: {
    businessPartnerGuid: string;
    applicationModuleId: string;
    eventId?: string;
    capModuleId: string;
    forCi: boolean;
}) => {
    const { data, loading } = useGetCapBusinessPartnerDetailsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { businessPartnerGuid, applicationModuleId, eventId, capModuleId },
        skip: forCi || !businessPartnerGuid || !capModuleId,
    });

    return useMemo(
        () => ({
            loading,
            existingBusinessPartnerData: data?.businessPartner,
        }),
        [data?.businessPartner, loading]
    );
};

export const useIsLoading = (loading: LoadingState): boolean =>
    useMemo(
        () =>
            Object.keys(loading)
                .map(key => loading[key])
                .some(loadingState => loadingState === true),
        [loading]
    );

export const useApplicationStageWithCap = (stage: ApplicationStage) =>
    useMemo(
        () =>
            [
                ApplicationStage.Appointment,
                ApplicationStage.Financing,
                ApplicationStage.Insurance,
                ApplicationStage.Lead,
                ApplicationStage.Reservation,
                ApplicationStage.VisitAppointment,
            ].includes(stage),
        [stage]
    );

export const getKycExtraSettings = (lead: LeadDataFragment) => {
    if (isNil(lead)) {
        return null;
    }

    // Not including launchpad lead type in here because for launchpad is placed on different component
    // It's on pdfs/src/app/components/leads
    switch (lead.__typename) {
        case LeadType.ConfiguratorLead:
        case LeadType.FinderLead:
        case LeadType.StandardLead: {
            switch (lead.module.__typename) {
                case ModuleType.ConfiguratorModule:
                case ModuleType.FinderApplicationPrivateModule:
                case ModuleType.FinderApplicationPublicModule:
                case ModuleType.StandardApplicationModule: {
                    return lead.module.customerModule.__typename === 'LocalCustomerManagementModule'
                        ? lead.module.customerModule.extraSettings
                        : null;
                }

                default:
                    return null;
            }
        }

        case LeadType.EventLead: {
            return lead.event.kycExtraSettings;
        }

        default:
            return null;
    }
};
