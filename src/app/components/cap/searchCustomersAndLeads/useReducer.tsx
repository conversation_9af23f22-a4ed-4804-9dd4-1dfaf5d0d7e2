import { Dispatch, useReducer } from 'react';
import type { ApplicationDataFragment } from '../../../api/fragments/ApplicationData';
import type { CapBusinessPartnerFragment } from '../../../api/fragments/CapBusinessPartner';
import type { CapLeadFragment } from '../../../api/fragments/CapLead';
import type { DebugJourneyDataFragment } from '../../../api/fragments/DebugJourneyData';
import { KycExtraSettingsSpecsFragment } from '../../../api/fragments/KYCExtraSettingsSpecs';
import type { KycFieldSpecsFragment } from '../../../api/fragments/KYCFieldSpecs';
import type { LeadDataFragment } from '../../../api/fragments/LeadData';
import { ApplicationStage, BusinessPartnerSearchField } from '../../../api/types';
import type { CapSubmissionValue } from './CapSubmissionValue';
import { SubmissionPurpose, ViewState } from './types';

export type ApplicableApplicationDataForCap = Extract<
    ApplicationDataFragment,
    { __typename: 'StandardApplication' | 'ConfiguratorApplication' | 'EventApplication' | 'FinderApplication' }
>;

export type ApplicableApplicationJourneyDataForCap = Extract<
    DebugJourneyDataFragment['application'],
    { __typename: 'StandardApplication' | 'ConfiguratorApplication' | 'EventApplication' | 'FinderApplication' }
>;

export type ApplicableApplicationForCap = ApplicableApplicationDataForCap | ApplicableApplicationJourneyDataForCap;

export type FunctionProps = {
    createNewBpFn: (capValues: CapSubmissionValue, responsibleSalesPersonId: string) => void;
    createNewLeadFn: (capValues: CapSubmissionValue, responsibleSalesPersonId: string) => void;
    selectExistingLeadFn: (capValues: CapSubmissionValue, responsibleSalesPersonId: string) => void;
};

export type SearchedCustomer = {
    email?: string;
    phone?: string;
    firstName?: string;
    lastName?: string;
    firstNameJapan?: string;
    lastNameJapan?: string;
    vin?: string;
    porscheId?: string;
};

export enum SearchCapCustomerType {
    Email = 'email',
    MobileNo = 'mobileNo',
    Name = 'name',
    PhoneticName = 'phoneticName',
    Vin = 'vin',
    PorscheId = 'porscheId',
}

export type LoadingState = {
    loadingBP: boolean;
    loadingLead: boolean;
    submissionLoading: boolean;
};

type SupportingApplicationValue = {
    applicationId?: string;
    stage?: ApplicationStage;
};

export type BatchSearchState = {
    currentBatch: number;
    batchesCompleted: BusinessPartnerSearchField[][];
    searchInProgress: boolean;
    hasResults: boolean;
    batchResults: CapBusinessPartnerFragment[];
    wasStarted: boolean;
};

export type State = {
    visible: boolean;
    submissionPurpose: SubmissionPurpose;
    viewState: ViewState;
    lead: LeadDataFragment;
    applicationModuleId: string;
    supportingApplicationValue: SupportingApplicationValue;
    capModuleId: string;
    dealerId: string;
    eventId: string;
    functions: FunctionProps;
    loading: LoadingState;
    businessPartners: CapBusinessPartnerFragment[];
    leads: CapLeadFragment[];
    selectedBusinessPartner: CapBusinessPartnerFragment;
    selectedLead: CapLeadFragment;
    selectedSalesperson: string;
    searchedCustomer: SearchedCustomer;
    searchCapCustomerType: SearchCapCustomerType;
    userChooseCreateNew: boolean;
    searchBPError: boolean;
    searchLeadError: boolean;
    forCi: boolean;
    firstLoad: boolean;
    isKycValid: boolean;
    kycFields: KycFieldSpecsFragment[];
    batchSearch: BatchSearchState;
    countryCode: string;
    kycExtraSettings: KycExtraSettingsSpecsFragment;
    endpointId: string;
};

type SetVisible = { type: 'setVisible'; visible: State['visible'] };
type SetViewStateAction = { type: 'setViewState'; viewState: State['viewState'] };
export type SetLoadingAction = { type: 'setLoading'; loading: State['loading'] };
type SetSearchBPError = { type: 'setSearchBPError'; searchBPError: State['searchBPError'] };
type SetSearchLeadError = { type: 'setSearchLeadError'; searchLeadError: State['searchLeadError'] };

type SetFirstLoadAction = {
    type: 'setFirstLoad';
    firstLoad: State['firstLoad'];
};

type SetUserChooseCreateNewAction = {
    type: 'setUserChooseCreateNew';
    userChooseCreateNew: State['userChooseCreateNew'];
};

type SetSearchedCustomer = {
    type: 'setSearchedCustomer';
    searchedCustomer: State['searchedCustomer'];
};

type SetSearchCapCustomerType = {
    type: 'setSearchCapCustomerType';
    searchCapCustomerType: State['searchCapCustomerType'];
};

export type SetBusinessPartnersAction = {
    type: 'setBusinessPartners';
    businessPartners: State['businessPartners'];
};
export type SetSelectedBusinessPartnerAction = {
    type: 'setSelectedBusinessPartner';
    selectedBusinessPartner: State['selectedBusinessPartner'];
};

export type SetSelectedSalespersonAction = {
    type: 'setSelectedSalesperson';
    selectedSalesperson: State['selectedSalesperson'];
};

export type SetLeadsAction = { type: 'setLeads'; leads: State['leads'] };
export type SetSelectedLeadAction = {
    type: 'setSelectedLead';
    selectedLead: State['selectedLead'];
};

export type SetIsKycValidAction = { type: 'setIsKycValid'; isKycValid: State['isKycValid'] };
export type SetKycFieldsAction = { type: 'setKycFields'; kycFields: State['kycFields'] };

type SetStateValues = {
    type: 'setStateValues';
    values: Partial<State>;
};

type ResetStateValues = {
    type: 'resetStateValues';
};

type SetBatchSearchAction = {
    type: 'setBatchSearch';
    batchSearch: Partial<BatchSearchState>;
};

type ResetBatchSearchAction = {
    type: 'resetBatchSearch';
};

export type Action =
    | SetStateValues
    | ResetStateValues
    | SetVisible
    | SetViewStateAction
    | SetSearchedCustomer
    | SetSearchCapCustomerType
    | SetLoadingAction
    | SetUserChooseCreateNewAction
    | SetBusinessPartnersAction
    | SetLeadsAction
    | SetSelectedBusinessPartnerAction
    | SetSelectedSalespersonAction
    | SetSelectedLeadAction
    | SetSearchBPError
    | SetSearchLeadError
    | SetFirstLoadAction
    | SetIsKycValidAction
    | SetKycFieldsAction
    | SetBatchSearchAction
    | ResetBatchSearchAction;

export type StateProps = { state: State; dispatch: Dispatch<Action> };

const initialFunction = {
    createNewBpFn: null,
    createNewLeadFn: null,
    selectExistingLeadFn: null,
};

const initialState = {
    visible: false,
    submissionPurpose: SubmissionPurpose.NewSubmission,
    viewState: ViewState.SearchForm,
    lead: null,
    supportingApplicationValue: {
        applicationId: null,
        stage: null,
    },
    applicationModuleId: null,
    eventId: null,
    capModuleId: null,
    dealerId: null,
    functions: initialFunction,
    loading: {
        loadingBP: false,
        loadingLead: false,
        submissionLoading: false,
    },
    businessPartners: [],
    leads: [],
    selectedBusinessPartner: null,
    selectedSalesperson: null,
    selectedLead: null,
    searchedCustomer: {
        email: null,
        phone: null,
        firstName: null,
        lastName: null,
        firstNameJapan: null,
        lastNameJapan: null,
        vin: null,
        porscheId: null,
    },
    searchCapCustomerType: SearchCapCustomerType.Email,
    userChooseCreateNew: false,
    searchBPError: false,
    searchLeadError: false,
    forCi: true,
    firstLoad: true,
    isKycValid: false,
    kycFields: [],
    batchSearch: {
        currentBatch: 0,
        batchesCompleted: [],
        searchInProgress: false,
        hasResults: false,
        batchResults: [],
        wasStarted: false,
    },
    countryCode: null,
    kycExtraSettings: null,
    endpointId: null,
};

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setStateValues':
            return { ...state, ...action.values };

        case 'resetStateValues':
            return { ...state, ...initialState };

        case 'setVisible':
            return { ...state, visible: action.visible };

        case 'setViewState':
            return { ...state, viewState: action.viewState };

        case 'setLoading':
            return { ...state, loading: action.loading };

        case 'setUserChooseCreateNew':
            return { ...state, userChooseCreateNew: action.userChooseCreateNew };

        case 'setSearchedCustomer':
            return { ...state, searchedCustomer: action.searchedCustomer };

        case 'setSearchCapCustomerType':
            return { ...state, searchCapCustomerType: action.searchCapCustomerType };

        case 'setBusinessPartners':
            return { ...state, businessPartners: action.businessPartners };

        case 'setLeads':
            return { ...state, leads: action.leads };

        case 'setSelectedBusinessPartner':
            return { ...state, selectedBusinessPartner: action.selectedBusinessPartner };

        case 'setSelectedSalesperson':
            return { ...state, selectedSalesperson: action.selectedSalesperson };

        case 'setSelectedLead':
            return { ...state, selectedLead: action.selectedLead };

        case 'setSearchBPError':
            return { ...state, searchBPError: action.searchBPError };

        case 'setSearchLeadError':
            return { ...state, searchLeadError: action.searchLeadError };

        case 'setFirstLoad':
            return { ...state, firstLoad: action.firstLoad };

        case 'setIsKycValid':
            return { ...state, isKycValid: action.isKycValid };

        case 'setKycFields':
            return { ...state, kycFields: action.kycFields };

        case 'setBatchSearch':
            return { ...state, batchSearch: { ...state.batchSearch, ...action.batchSearch } };

        case 'resetBatchSearch':
            return {
                ...state,
                batchSearch: {
                    currentBatch: 0,
                    batchesCompleted: [],
                    searchInProgress: false,
                    hasResults: false,
                    batchResults: [],
                    wasStarted: false,
                },
            };

        default:
            return state;
    }
};

export type StateAndDispatch = {
    state: State;
    dispatch: Dispatch<Action>;
};

const useSearchCapCustomerReducer = () => useReducer(reducer, initialState);

export default useSearchCapCustomerReducer;
