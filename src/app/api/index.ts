/* eslint-disable */

export type {
  Maybe,
  InputMaybe,
  Exact,
  MakeOptional,
  MakeMaybe,
  MakeEmpty,
  Incremental,
  Scalars,
  AdditionalDetails,
  AdditionalDetailsInput,
  AdditionalIntegrationParameter,
  AdditionalIntegrationParameterInput,
  AddressAutocompleteInput,
  AddressAutocompleteResult,
  AddressComponent,
  AddressCoordinates,
  AdvancedVersioning,
  AdyenPaymentModule,
  AdyenPaymentModuleSettings,
  AdyenPaymentSetting,
  AdyenPaymentSettingsInput,
  AdyenRedirectionLink,
  AmendMobilityApplicationAccess,
  ApplicantAgreementSettings,
  Application,
  ApplicationAdyenDeposit,
  ApplicationAgreement,
  ApplicationCampaignValues,
  ApplicationCampaignValuesInput,
  ApplicationComboOption,
  ApplicationComboSettings,
  ApplicationConfiguration,
  ApplicationConfiguratorBlock,
  ApplicationConfiguratorColorSetting,
  ApplicationConfiguratorOptionSetting,
  ApplicationConfiguratorPackageSetting,
  ApplicationConfiguratorTrimSetting,
  ApplicationCustomerDraft,
  ApplicationDeposit,
  ApplicationDocument,
  ApplicationDocumentPayload,
  ApplicationFilter,
  ApplicationFilteringRule,
  ApplicationFinanceSettings,
  ApplicationFinancing,
  ApplicationFiservDeposit,
  ApplicationInsuranceSettings,
  ApplicationInsurancing,
  ApplicationJourney,
  ApplicationListEndpoint,
  ApplicationListEndpointSettings,
  ApplicationMarketType,
  ApplicationMarketTypeInput,
  ApplicationMessage,
  ApplicationModuleCondition,
  ApplicationModuleConditionSettings,
  ApplicationModuleMarketTypeInput,
  ApplicationMonthlyInstalment,
  ApplicationMonthlyInstalmentPayload,
  ApplicationNamirialSigning,
  ApplicationNewLocalCustomerDraft,
  ApplicationOtpSigning,
  ApplicationOptionSettings,
  ApplicationPayGateDeposit,
  ApplicationPorscheDeposit,
  ApplicationQuotation,
  ApplicationQuotationOption,
  ApplicationQuotationOptionSetting,
  ApplicationQuotationSettings,
  ApplicationSigning,
  ApplicationSortingRule,
  ApplicationStageDetails,
  ApplicationSubmitFinancing,
  ApplicationSubmitInsurance,
  ApplicationSubmitMobilityBooking,
  ApplicationTtbDeposit,
  ApplicationUpdate,
  ApplicationUpdateAppointmentDateTimeData,
  ApplicationUpdateAppointmentDetails,
  ApplicationUpdateAppointmentMileage,
  ApplicationUpdateConfiguration,
  ApplicationUpdateTestDriveDetails,
  ApplicationUpdateVisitAppointmentDetails,
  ApplicationValueSetting,
  ApplicationVehicleDraft,
  ApplyNewRedirectionLink,
  AppointmentBlockedTimeSlot,
  AppointmentBookingTimeSlot,
  AppointmentChangedVehicle,
  AppointmentChangedVehicleInput,
  AppointmentChangedVehicleLocal,
  AppointmentChangedVehicleLocalInput,
  AppointmentChangedVehicleText,
  AppointmentChangedVehicleTextInput,
  AppointmentMileage,
  AppointmentModule,
  AppointmentModuleEmailContent,
  AppointmentModuleEmailContentCustomer,
  AppointmentModuleEmailContentCustomerInput,
  AppointmentModuleEmailContentFinderReservation,
  AppointmentModuleEmailContentFinderReservationInput,
  AppointmentModuleEmailContentInput,
  AppointmentModuleEmailContentSalesPerson,
  AppointmentModuleEmailContentSalesPersonInput,
  AppointmentModuleEmailContents,
  AppointmentModuleEmailContentsInput,
  AppointmentModuleInitialSettings,
  AppointmentStage,
  AppointmentTimeSlot,
  AppointmentTimeSlotInput,
  AuditTrail,
  AuthenticationRequiresNewPassword,
  AuthenticationRequiresSmsOtp,
  AuthenticationRequiresTotp,
  AuthenticationResponse,
  AuthenticationSuccessful,
  AuthenticationWithWebPublicKeyCredential,
  AuthenticatorSetup,
  Author,
  Authorization,
  AutoplayModule,
  AutoplayModulePayload,
  AutoplaySetting,
  AutoplaySettingPayload,
  AvailableModules,
  AverageMileageSettings,
  AverageMileageSettingsPayload,
  BalloonGfvSettings,
  BalloonGfvSettingsPayload,
  BalloonRangeSettings,
  BalloonSettings,
  BalloonSettingsPayload,
  BalloonTableCell,
  BalloonTableCellPayload,
  BalloonTableSettings,
  BalloonValueTableCellForVehicleParameterFilter,
  BalloonValueTableCellForVehicleParameterFilterPayload,
  Bank,
  BankAvailableFinanceProductTypesUpdateSetting,
  BankCondition,
  BankConditionSettings,
  BankDealershipMarket,
  BankDealershipMarketInput,
  BankDealershipMarketOverrides,
  BankDealershipMarketOverridesInput,
  BankFilteringRule,
  BankIntegration,
  BankIntegrationSettings,
  BankIntegrationUpdateSettings,
  BankModule,
  BankModuleSettings,
  BankOption,
  BankOptionFilteringRule,
  BankSettings,
  BankSortingRule,
  Banner,
  BannerFilteringRule,
  BannerInput,
  BannerSortingRule,
  BasePerformance,
  BasicDealerIntegrationDetails,
  BasicLayout,
  BasicProLayout,
  BasicProLayoutSettings,
  BasicRouterSettings,
  BasicSigningModule,
  BasicSigningModuleSettings,
  BidType,
  Block,
  BlockInput,
  CtsFinderRedirectionLink,
  CalculatorDeferredPrincipalResult,
  CalculatorHirePurchaseResult,
  CalculatorHirePurchaseWithBalloonGfvResult,
  CalculatorHirePurchaseWithBalloonResult,
  CalculatorLeasePurchaseResult,
  CalculatorLeaseResult,
  CalculatorPayload,
  CalculatorResult,
  CalculatorUcclLeasingResult,
  CalendarEvent,
  CalendarEventAttendee,
  CalendarEventEmailHistory,
  CalendarEventOrganizer,
  Campaign,
  CampaignFilteringRule,
  CampaignSettings,
  CampaignSortingRule,
  CancelMobilityApplicationAccess,
  CapBusinessPartnerData,
  CapCurrentVehicle,
  CapLeadData,
  CapMetadata,
  CapModule,
  CapModuleSettings,
  CapSetting,
  CapSettingInput,
  CapValuesInput,
  CapValuesOnApplication,
  CapVehicleMakeMetadata,
  CapVehicleModelMetadata,
  CheckboxApplicationAgreement,
  CheckboxConsentsAndDeclarations,
  CheckboxConsentsAndDeclarationsSettings,
  ColorAndTrimSettings,
  ColorAndTrimSettingsInput,
  ColorBlock,
  ColorBlockInput,
  ColumnBlock,
  ColumnBlockInput,
  ColumnWebPageBlock,
  ColumnWebPageBlockInput,
  ComboOption,
  ComboOptionInput,
  ComboOptionSettings,
  ComboOptionSettingsInput,
  CommissionSettingPayload,
  CommissionSettings,
  Company,
  CompanyExportOptions,
  CompanyExportResult,
  CompanyExportStatistics,
  CompanyFilterListRule,
  CompanyFilteringRule,
  CompanyImportConflict,
  CompanyImportOptions,
  CompanyImportResult,
  CompanyImportStatistics,
  CompanyOptions,
  CompanySettings,
  CompanySortingRule,
  CompoundValue,
  CompoundValuePayload,
  Condition,
  ConditionDetail,
  ConditionSettings,
  Configurator,
  ConfiguratorApplication,
  ConfiguratorApplicationConfiguration,
  ConfiguratorApplicationConfigurationPayload,
  ConfiguratorApplicationEntrypoint,
  ConfiguratorApplicationEntrypointSettings,
  ConfiguratorApplicationLink,
  ConfiguratorBlockPayload,
  ConfiguratorComboPayload,
  ConfiguratorInventory,
  ConfiguratorLead,
  ConfiguratorModule,
  ConfiguratorModuleEmailContents,
  ConfiguratorModuleEmailContentsSetting,
  ConfiguratorModuleInitialSettings,
  ConfiguratorModuleUpdateSettings,
  ConfiguratorSaveOrderContent,
  ConfiguratorSaveOrderInput,
  ConfiguratorStockInventory,
  ConfiguratorSubmitOrderContent,
  ConfiguratorSubmitOrderInput,
  ConsentFeaturePurpose,
  ConsentFeaturePurposeInput,
  ConsentsAndDeclarations,
  ConsentsAndDeclarationsFilteringRule,
  ConsentsAndDeclarationsModule,
  ConsentsAndDeclarationsModuleSettings,
  ConsentsAndDeclarationsSortingRule,
  ConsumptionEmission,
  ContentRefinementSourceType,
  ContextualCondition,
  ContextualConditionSettings,
  CorporateCustomer,
  CounterPayload,
  CounterSettings,
  CreateLeadFollowUpInput,
  CreateNewUserLink,
  CreateWebsiteModulePayload,
  CtsModule,
  CtsModuleInitialSettings,
  CtsModuleUpdateSettings,
  CtsSetting,
  CtsSettingInitialInput,
  CtsSettingUpdateInput,
  Currency,
  CurrencyLocalizeArgs,
  CustomTestDriveBookingSlots,
  CustomTestDriveBookingSlotsInput,
  CustomWebPageBlock,
  CustomWebPageBlockInput,
  Customer,
  CustomerFilter,
  CustomerFilteringRule,
  CustomerListEndpoint,
  CustomerListEndpointSettings,
  CustomerSortingRule,
  CustomizedField,
  CustomizedFieldInput,
  DateUnit,
  DateUnitInput,
  DbsBankIntegration,
  DbsBankIntegrationMapping,
  DbsBankIntegrationMappingSettings,
  DbsBankIntegrationSettings,
  Dealer,
  DealerApplicationMarketTypeInput,
  DealerAssignmentObjectId,
  DealerAssignmentObjectIdOverrides,
  DealerAutoplay,
  DealerAutoplayInput,
  DealerBookingCodeSetting,
  DealerBookingCodeSettingInput,
  DealerBookingCodeSettingOverride,
  DealerBookingCodeSettingOverrideInput,
  DealerBooleanSetting,
  DealerBooleanSettingInput,
  DealerBooleanSettingOverride,
  DealerBooleanSettingOverridesInput,
  DealerCondition,
  DealerConditionsSettings,
  DealerContact,
  DealerContactInput,
  DealerDisclaimers,
  DealerDisclaimersConfigurator,
  DealerDisclaimersConfiguratorInput,
  DealerDisclaimersInput,
  DealerDisclaimersOverridesConfigurator,
  DealerDisclaimersOverridesConfiguratorInput,
  DealerFilteringRule,
  DealerFinanceProducts,
  DealerFinanceProductsPayload,
  DealerFinancingDisclaimer,
  DealerFinancingDisclaimerInput,
  DealerFloat,
  DealerFloatOverrides,
  DealerInput,
  DealerInsuranceDisclaimer,
  DealerInsuranceDisclaimerInput,
  DealerInsuranceProducts,
  DealerInsuranceProductsPayload,
  DealerInt,
  DealerIntOverrides,
  DealerIntegrationDetails,
  DealerIntegrationDetailsInput,
  DealerOption,
  DealerOptionPayload,
  DealerPermissions,
  DealerPriceDisclaimer,
  DealerPriceDisclaimerInput,
  DealerPriceDisclaimerOverride,
  DealerPriceDisclaimerOverridesInput,
  DealerSocialMedia,
  DealerSocialMediaInput,
  DealerSortingRule,
  DealerTranslatedStringSetting,
  DealerTranslatedStringSettingInput,
  DealerTranslatedStringSettingOverride,
  DealerTranslatedStringSettingOverridesInput,
  DealerTranslationText,
  DealerTranslationTextInput,
  DealerTranslationTextOverride,
  DealerTranslationTextOverridesInput,
  DealerUpload,
  DealerUploadOverrides,
  DealerUploadedFileWithPreview,
  DealerUploadedFileWithPreviewOverride,
  DealerVehicles,
  DealerVehiclesPayload,
  DealershipDepositAmountInput,
  DealershipDepositAmountOverridesInput,
  DealershipMarket,
  DealershipMarketInput,
  DealershipMarketOverride,
  DealershipMarketOverridesInput,
  DealershipMyInfoSetting,
  DealershipMyInfoSettingOverride,
  DealershipPaymentSetting,
  DealershipPaymentSettingInput,
  DealershipPaymentSettingOverride,
  DealershipPaymentSettingOverridesInput,
  DealershipPublicSalesPerson,
  DealershipPublicSalesPersonOverride,
  DealershipSetting,
  DealershipSettingInput,
  DealershipSettingOverridesInput,
  DefaultApplicationFinanceSettings,
  DefaultApplicationFinancing,
  DefaultApplicationInsuranceSettings,
  DefaultApplicationInsurancing,
  DefaultApplicationMarket,
  DeferredPrincipalTermSettings,
  DepositAmount,
  DepositAmountInput,
  DepositAmountOverride,
  DepositAmountOverridesInput,
  DepositRangeSettings,
  DepositSalesOffer,
  DepositSalesOfferInput,
  DepositSettings,
  DepositSettingsPayload,
  DepositTableCell,
  DepositTableCellPayload,
  DepositTableSettings,
  Discount,
  DiscountCode,
  DiscountInput,
  DiscountPromoType,
  DiscountPromoTypeInput,
  Displacement,
  DisplacementSettingPayload,
  DisplacementSettings,
  DocusignModule,
  DocusignModuleInput,
  DocusignSetting,
  DocusignSettingInput,
  DownPayment,
  DownPaymentLocalizeArgs,
  DownPaymentRangeSettings,
  DownPaymentSettings,
  DownPaymentSettingsPayload,
  DownPaymentTableCell,
  DownPaymentTableCellPayload,
  DownPaymentTableSettings,
  Download,
  DownloadEncryption,
  DraftGiftVoucherSettings,
  DrivingLicense,
  DrivingLicensePayload,
  DropdownOptionSettings,
  DropdownOptionSettingsInput,
  DummyPrivatePageEndpoint,
  DummyPrivatePageSettings,
  DummyWelcomePageEndpoint,
  DummyWelcomePageSettings,
  Eazy,
  EazyInsurerIntegration,
  EazyInsurerIntegrationSettings,
  EdmEmailFooter,
  EdmEmailFooterPayload,
  EdmEmailSocialMedia,
  EdmEmailSocialMediaPayload,
  EfficiencyClassFranceType,
  EfficiencyClassGermanyLabel,
  EfficiencyClassGermanyType,
  EfficiencyClassSwitzerlandType,
  EmailBankIntegration,
  EmailBankIntegrationSettings,
  EmailInsurerIntegration,
  EmailInsurerIntegrationSettings,
  EmailSettings,
  EmailSettingsPayload,
  EnbdApplicationQuotation,
  EnbdApplicationQuotationSettings,
  EnbdBankIntegration,
  EnbdBankIntegrationSettings,
  EnbdFinanceManager,
  EnbdFinanceManagerSettings,
  Endpoint,
  EnergyConsumption,
  EnergyConsumptionInput,
  EnergyEfficiency,
  Equipment,
  EquipmentCategory,
  ErgoLookupTable,
  ErgoLookupTableCell,
  ErgoLookupTableCellPayload,
  ErgoLookupTableSetting,
  ErgoLookupTableSettingsPayload,
  Event,
  EventApplication,
  EventApplicationConfiguration,
  EventApplicationConfigurationPayload,
  EventApplicationEntrypoint,
  EventApplicationEntrypointSettings,
  EventApplicationLink,
  EventApplicationModule,
  EventApplicationModuleEmailContents,
  EventApplicationModuleEmailContentsSetting,
  EventApplicationModuleEmailSubmitOrderInput,
  EventApplicationModuleEmailWithScenarioOverridesSubmitOrderInput,
  EventApplicationModuleInitialSettings,
  EventApplicationModuleSettings,
  EventApplicationModuleSubmitOrderContent,
  EventApplicationModuleSubmitOrderScenarioOverrideInput,
  EventApplicationModuleUpdateSettings,
  EventApplicationSubmitOrderOverrideContent,
  EventApplicationSubmitOrderWithOverrideContent,
  EventCustomizedField,
  EventCustomizedFieldInput,
  EventEmailContents,
  EventEmailContentsSetting,
  EventFilteringRule,
  EventFilters,
  EventInput,
  EventKycPresetInput,
  EventLead,
  EventSortingRule,
  EventSubmitOrder,
  EventUtmParameters,
  EventUtmParametersDefaultValue,
  EventUtmParametersDefaultValueInput,
  EventUtmParametersInput,
  EventUtmParametersOverride,
  EventUtmParametersOverrideInput,
  ExteriorColor,
  ExternalBank,
  ExternalLink,
  ExternalReference,
  ExternalSystemReference,
  FiCommission,
  FeatureValue,
  FilterDropdownOption,
  FilterVehicleFilteringRule,
  FinanceProduct,
  FinanceProductFilteringRule,
  FinanceProductSettings,
  FinanceProductSortingRule,
  FinanceProductVehicleFilteringRule,
  FinanceSalesOffer,
  FinanceSalesOfferInput,
  Financing,
  FinancingProductsArgs,
  FinancingProduct,
  FinancingStage,
  FinderApplication,
  FinderApplicationConfiguration,
  FinderApplicationConfigurationPayload,
  FinderApplicationEmailWithScenarioOverridesReminderContent,
  FinderApplicationEmailWithScenarioOverridesReminderInput,
  FinderApplicationEmailWithScenarioOverridesSubmitOrderContent,
  FinderApplicationEmailWithScenarioOverridesSubmitOrderInput,
  FinderApplicationEntrypoint,
  FinderApplicationEntrypointSettings,
  FinderApplicationLink,
  FinderApplicationModuleEmailContents,
  FinderApplicationModuleEmailContentsSetting,
  FinderApplicationModuleInitialSettings,
  FinderApplicationModuleReminderEmailContent,
  FinderApplicationModuleReminderEmailInput,
  FinderApplicationModuleReminderEmailScenarioOverrideContent,
  FinderApplicationModuleReminderEmailScenarioOverrideInput,
  FinderApplicationModuleSubmitOrderContent,
  FinderApplicationModuleSubmitOrderInput,
  FinderApplicationModuleSubmitOrderScenarioOverrideContent,
  FinderApplicationModuleSubmitOrderScenarioOverrideInput,
  FinderApplicationModuleUpdateSettings,
  FinderApplicationPrivateModule,
  FinderApplicationPrivateModuleInitialSettings,
  FinderApplicationPrivateModuleUpdateSettings,
  FinderApplicationPublicAccessEntrypoint,
  FinderApplicationPublicAccessEntrypointSettings,
  FinderApplicationPublicModule,
  FinderLead,
  FinderRestrictedValuePayload,
  FinderVehicle,
  FinderVehicleListingArgs,
  FinderVehicleMonthlyInstalmentArgs,
  FinderVehicleAutoplay,
  FinderVehicleFilters,
  FinderVehicleLtaIntegrationPayload,
  FinderVehicleManagementModule,
  FinderVehicleManagementModuleSetting,
  FinderVehicleManagementModuleSettingSecret,
  FinderVehicleManagementSecret,
  FinderVehicleManagementSetting,
  FinderVehiclePayload,
  FinderVehicleReferenceParameters,
  FinderVehicleReferenceParametersPayload,
  FinderVehicleSetting,
  FinderVehicleSettings,
  FinderVehicleSortingRule,
  FiservPaymentModule,
  FiservPaymentModuleSettings,
  FiservPaymentRedirectionLink,
  FiservPaymentSetting,
  FiservPaymentSettingsInput,
  FiservPaymentUpdateSettingsInput,
  FlexibleDiscount,
  FlexibleDiscountInput,
  FollowUpStage,
  FormattedDate,
  FormattedDateLocalizeArgs,
  FpTable,
  FpTableExcelHeaderPayload,
  FpTables,
  GeneratedOtpResult,
  GenericPrincipalTermSettings,
  GetCapBusinessPartnerQueryInput,
  GetFinanceProductsFilteringRule,
  GetInsuranceProductsFilteringRule,
  GiftPromoType,
  GiftPromoTypeInput,
  GiftVoucher,
  GiftVoucherAdyenRedirectionLink,
  GiftVoucherCondition,
  GiftVoucherConditionSettings,
  GiftVoucherDraftFlow,
  GiftVoucherFilter,
  GiftVoucherFilteringRule,
  GiftVoucherFiservPaymentRedirectionLink,
  GiftVoucherJourneyContext,
  GiftVoucherModule,
  GiftVoucherModuleCustomerEmail,
  GiftVoucherModuleEmailContent,
  GiftVoucherModuleEmailContentCustomerInput,
  GiftVoucherModuleEmailContentInput,
  GiftVoucherModuleEmailContentSalesPersonInput,
  GiftVoucherModuleEmailContentsInput,
  GiftVoucherModuleEmailContext,
  GiftVoucherModuleInitialSettings,
  GiftVoucherModuleSalespersonEmail,
  GiftVoucherModuleUpdateSettings,
  GiftVoucherPayGatePaymentRedirectionLink,
  GiftVoucherPorschePaymentRedirectionLink,
  GiftVoucherSortingRule,
  GiftVoucherTtbPaymentRedirectionLink,
  GroupApplicationAgreement,
  GroupConsentsAndDeclarations,
  GroupConsentsAndDeclarationsSettings,
  Guarantor,
  Hitcount,
  Hitcounts,
  HlfBankIntegration,
  HlfBankIntegrationSettings,
  HlfBankV2Integration,
  HlfBankV2IntegrationSettings,
  HomeDeliveryFilteringRule,
  HomeDeliverySortingRule,
  ICurrencyType,
  ICurrencyTypeLocalizeArgs,
  ILocalizedValue,
  ILocalizedValueLocalizeArgs,
  ImageDescription,
  ImageDescriptionWebPageBlockInput,
  ImageWebPageBlock,
  ImageWebPageBlockInput,
  ImportDealerFail,
  ImportDealerResponse,
  ImportDealerSuccess,
  ImportExcel,
  ImportImages,
  ImportInventoryFail,
  ImportInventoryResponse,
  ImportInventorySuccess,
  ImportSalesControlBoardResponses,
  ImportedBalloonTableCell,
  ImportedDepositTableCell,
  ImportedDisplacementTableCell,
  ImportedDownPaymentTableCell,
  ImportedErgoLookupData,
  ImportedErgoLookupTableCell,
  ImportedExcel,
  ImportedFpTableFromExcelHeader,
  ImportedInsuranceFeeTableCell,
  ImportedInsuranceFeeValue,
  ImportedInterestRateTableCell,
  ImportedLeaseTableCell,
  ImportedLocationTable,
  ImportedLocationTableValue,
  ImportedResidualValueTableCell,
  InstantApprovalStatisticFilterPayload,
  InsuranceFeeSettingPayload,
  InsuranceFeeSettings,
  InsuranceFeeTableValues,
  InsuranceFeeTableValuesForVehicleParameterFilter,
  InsuranceFeeTableValuesForVehicleParameterPayload,
  InsuranceFeeTableValuesPayload,
  InsuranceModule,
  InsuranceModuleSettings,
  InsurancePremiumPayload,
  InsuranceProduct,
  InsuranceProductFilteringRule,
  InsuranceProductSettings,
  InsuranceProductSortingRule,
  InsuranceSalesOffer,
  InsuranceSalesOfferInput,
  InsuranceStage,
  Insurer,
  InsurerCondition,
  InsurerConditionsSettings,
  InsurerFilteringRule,
  InsurerIntegration,
  InsurerIntegrationSettings,
  InsurerSettings,
  InsurerSortingRule,
  InterestRateFixedSettings,
  InterestRateRangeSettings,
  InterestRateSettings,
  InterestRateSettingsPayload,
  InterestRateTableCell,
  InterestRateTableCellPayload,
  InterestRateTableSettings,
  Interior,
  InteriorColorAttributes,
  Inventory,
  InventoryFilteringRule,
  InventoryModelOption,
  InventoryModuleOption,
  InventoryOptionItem,
  InventoryOptions,
  InventoryOptionsFilteringRule,
  InventorySettings,
  InventorySortingRule,
  InventorySubModelOption,
  InventoryUpdationSettings,
  InventoryVariantOption,
  KycField,
  KycFieldSettings,
  KycPreset,
  KycPresetFilteringRule,
  KycPresetSettings,
  LtaIntegration,
  LtaSettings,
  LabeledValue,
  Labels,
  LabelsFilteringField,
  LabelsInput,
  LabelsModule,
  LabelsModuleSettings,
  LabelsSortingRule,
  LanguagePack,
  LanguagePackFilteringRule,
  LanguagePackFilters,
  LanguagePackSettings,
  LanguagePackSortingRule,
  LaunchPadApplicationConfiguration,
  LaunchPadApplicationEntrypoint,
  LaunchPadApplicationEntrypointSettings,
  LaunchPadModule,
  LaunchPadModuleInitialSettings,
  LaunchPadModuleUpdateSettings,
  LaunchpadApplication,
  LaunchpadApplicationConfiguration,
  LaunchpadLead,
  Layout,
  Lead,
  LeadFilter,
  LeadFilterOptionRule,
  LeadFilteringRule,
  LeadListEndpoint,
  LeadListEndpointSettings,
  LeadRatio,
  LeadSortingRule,
  LeaseSettings,
  LeaseSettingsPayload,
  LeaseTableCell,
  LeaseTableCellPayload,
  LeaseValueTableCellForVehicleParameterFilter,
  LeaseValueTableCellForVehicleParameterFilterPayload,
  LicenseAndFuelTaxSettingPayload,
  LicenseAndFuelTaxSettings,
  LicenseAndFuelTaxTableValues,
  LicenseAndFuelTaxTableValuesPayload,
  LicensePlateFeeSettingPayload,
  LicensePlateFeeSettings,
  ListDealerOption,
  ListFinderVehiclesInfo,
  Listing,
  ListingTypeEdge,
  ListingsConnection,
  LiveChatSetting,
  LoanSettings,
  LoanSettingsPayload,
  LocalCustomer,
  LocalCustomerArrayStringField,
  LocalCustomerDateField,
  LocalCustomerDrivingLicenseField,
  LocalCustomerField,
  LocalCustomerFieldSettings,
  LocalCustomerManagementKycFieldsExtraConfig,
  LocalCustomerManagementKycFieldsExtraConfigSettings,
  LocalCustomerManagementModule,
  LocalCustomerManagementModuleKycField,
  LocalCustomerManagementModuleKycFieldSettings,
  LocalCustomerManagementSettings,
  LocalCustomerNumberField,
  LocalCustomerPhoneField,
  LocalCustomerReferenceDetailSetField,
  LocalCustomerSalaryTransferredBankSetField,
  LocalCustomerStringDescriptionField,
  LocalCustomerStringField,
  LocalCustomerUaeIdentitySetField,
  LocalCustomerUploadsField,
  LocalCustomerVerifiedPhoneField,
  LocalDeferredPrincipal,
  LocalFittedOptions,
  LocalFittedOptionsInput,
  LocalHirePurchase,
  LocalHirePurchaseWithBalloon,
  LocalHirePurchaseWithBalloonGfv,
  LocalLease,
  LocalLeasePurchase,
  LocalMake,
  LocalMakeFilteringRule,
  LocalMakeSettings,
  LocalMakeSortingRule,
  LocalModel,
  LocalModelFilteringRule,
  LocalModelSettings,
  LocalModelSortingRule,
  LocalSubmodelSettings,
  LocalUcclLeasing,
  LocalVariant,
  LocalVariantMonthlyInstalmentArgs,
  LocalVariantFilteringRule,
  LocalVariantSettings,
  LocalVariantSortingRule,
  LocalizedColorGroup,
  LocalizedColorGroupLocalizeArgs,
  LocalizedInteriorColorGroup,
  LocalizedInteriorColorGroupLocalizeArgs,
  LocalizedLabeledValue,
  LocalizedLabeledValueLocalizeArgs,
  LocalizedModelCategory,
  LocalizedModelCategoryLocalizeArgs,
  LocalizedModelGeneration,
  LocalizedModelGenerationLocalizeArgs,
  LocalizedSalesFilter,
  LocalizedSalesFilterLocalizeArgs,
  LocalizedSortOption,
  LocalizedSortOptionLocalizeArgs,
  LocalizedStandardEquipment,
  LocalizedStandardEquipmentLocalizeArgs,
  LocalizedString,
  LocalizedStringLocalizeArgs,
  LocalizedTechnicalData,
  LocalizedTechnicalDataLocalizeArgs,
  LocalizedValue,
  LocalizedValueLocalizeArgs,
  LocalizedVehicleType,
  LocalizedVehicleTypeLocalizeArgs,
  Location,
  LocationCondition,
  LocationConditionSettings,
  LocationFilteringRule,
  LocationInput,
  LocationSortingRule,
  LogicCondition,
  LogicConditionSettings,
  LowestMonthlyInstalmentPayload,
  MfaSettings,
  MfaSettingsPayload,
  MainDetailsSalesOffer,
  MainDetailsSalesOfferInput,
  MaintenanceDetailsSettings,
  MaintenanceModule,
  MaintenanceModuleSettings,
  MaintenanceUpdate,
  MandatoryCompoundValuePayload,
  MarketingApplicationAgreement,
  MarketingConsentsAndDeclarations,
  MarketingConsentsAndDeclarationsSettings,
  MarketingDashboard,
  MarketingDashboardFilterDropdown,
  MarketingDashboardFilterPayload,
  MarketingModule,
  MarketingModuleInput,
  MarketingPlatform,
  MarketingPlatformInput,
  MarketingPlatformsAgreed,
  MarketingPlatformsAgreedInput,
  MaskSettings,
  MaskSettingsPayload,
  Matrix,
  MaybankIntegration,
  MaybankIntegrationSettings,
  MaybankIntegrationUpdateSettings,
  MenuCustomPathItem,
  MenuCustomPathItemSettings,
  MenuEndpointItem,
  MenuEndpointItemSettings,
  MenuItem,
  MenuItemSettings,
  MenuLogoutActionItem,
  MenuLogoutActionItemSettings,
  MessageNotice,
  MinMaxValues,
  MobileVerificationResponse,
  Mobility,
  MobilityAdditionalInfo,
  MobilityAdditionalInfoSetting,
  MobilityAdditionalInfoSnapshot,
  MobilityAddon,
  MobilityAddonSetting,
  MobilityAddonSnapshot,
  MobilityApplication,
  MobilityApplicationAmendmentLink,
  MobilityApplicationCancellationLink,
  MobilityApplicationEntrypoint,
  MobilityApplicationEntrypointSettings,
  MobilityBookingDetails,
  MobilityBookingDetailsPayload,
  MobilityBookingLocation,
  MobilityBookingLocationHome,
  MobilityBookingLocationHomeDeliveryPayload,
  MobilityBookingLocationPayload,
  MobilityBookingLocationPickup,
  MobilityBookingLocationPickupPayload,
  MobilityCustomerEmailContent,
  MobilityCustomerEmailContentInput,
  MobilityDetail,
  MobilityDetailPayload,
  MobilityDetailsAddonPayload,
  MobilityDetailsPayload,
  MobilityEmailContent,
  MobilityEmailContentInput,
  MobilityEmailScenarioContent,
  MobilityEmailScenarioContentInput,
  MobilityFilteringRule,
  MobilityHomeDelivery,
  MobilityHomeDeliveryInput,
  MobilityInventory,
  MobilityLead,
  MobilityLocation,
  MobilityLocationInput,
  MobilityModule,
  MobilityModuleInitialSettings,
  MobilityModuleUpdateSettings,
  MobilityOperatorEmailContent,
  MobilityOperatorEmailContentInput,
  MobilityOption,
  MobilityOptionPayload,
  MobilitySigningSetting,
  MobilitySigningSettingInput,
  MobilitySnapshot,
  MobilitySortingRule,
  MobilityStage,
  MobilityStockInventory,
  MobilityStockReservation,
  MobilityVehicleFilters,
  ModelAverageTarget,
  ModelConfigurator,
  ModelConfiguratorFilteringRule,
  ModelConfiguratorFilters,
  ModelConfiguratorSettings,
  ModelConfiguratorSortingRule,
  Module,
  ModuleDisclaimers,
  ModuleDisclaimersInput,
  ModuleFilteringRule,
  ModuleSortingRule,
  ModuleVariant,
  ModuleVariantInput,
  MonthOfImportOption,
  MonthlyPaymentFixedInterestRateSettingPayload,
  MonthlyPaymentFixedInterestRateSettings,
  MultiSelectOptionSettings,
  MultiSelectOptionSettingsInput,
  Mutation,
  MutationAddAuditTrailCommentArgs,
  MutationAddCustomerAuditTrailCommentArgs,
  MutationAddInventoryAuditTrailCommentArgs,
  MutationAddStockInventoryArgs,
  MutationAddTradeInAuditTrailCommentArgs,
  MutationAddVehicleLtaArgs,
  MutationAmendMobilityApplicationArgs,
  MutationApplyBasicLayoutOnRouterArgs,
  MutationApplyBasicProLayoutOnRouterArgs,
  MutationApplyEmptyLayoutOnRouterArgs,
  MutationApplyForPasswordChangeArgs,
  MutationApplyGiftVoucherArgs,
  MutationApplyNewArgs,
  MutationApplyNewConfiguratorApplicationArgs,
  MutationApplyNewFinderApplicationArgs,
  MutationApplyNewStandardApplicationArgs,
  MutationApplyPorscheV3LayoutOnRouterArgs,
  MutationApproveApplicationArgs,
  MutationAttachDealersToUserGroupArgs,
  MutationAttachPermissionOnRoleArgs,
  MutationAttachSuperiorGroupsToUserGroupArgs,
  MutationAttachUserOnRoleArgs,
  MutationAttachUsersToUserGroupArgs,
  MutationAuthenticateArgs,
  MutationAuthenticateWithSmsOtpArgs,
  MutationAuthenticateWithTotpArgs,
  MutationAuthenticateWithWebPublicKeyCredentialArgs,
  MutationAuthorizeWithOidcArgs,
  MutationCalculateArgs,
  MutationCancelApplicationArgs,
  MutationCancelLeadFollowUpArgs,
  MutationChangePasswordArgs,
  MutationChangePasswordFromAuthenticationArgs,
  MutationChangePasswordFromTokenArgs,
  MutationCheckInApplicationArgs,
  MutationCompleteApplicationArgs,
  MutationCompleteLeadArgs,
  MutationCompleteWebPublicKeyCredentialRegistrationArgs,
  MutationConcludeAgreementApplicationArgs,
  MutationConfirmBookingApplicationArgs,
  MutationConfirmLeadFollowUpArgs,
  MutationContactApplicationArgs,
  MutationContinueApplicationArgs,
  MutationCopyVariantConfiguratorArgs,
  MutationCreateAdditionalDetailArgs,
  MutationCreateAdyenPaymentModuleArgs,
  MutationCreateAdyenPaymentSettingsArgs,
  MutationCreateApplicationListEndpointArgs,
  MutationCreateAppointmentFromLeadArgs,
  MutationCreateAppointmentModuleArgs,
  MutationCreateAutoplayModuleArgs,
  MutationCreateAutoplayModuleSettingArgs,
  MutationCreateBankArgs,
  MutationCreateBankModuleArgs,
  MutationCreateBannerArgs,
  MutationCreateBasicSigningModuleArgs,
  MutationCreateCampaignArgs,
  MutationCreateCapModuleArgs,
  MutationCreateCheckboxConsentsAndDeclarationsArgs,
  MutationCreateColorBlockArgs,
  MutationCreateCompanyArgs,
  MutationCreateConfiguratorApplicationEntrypointArgs,
  MutationCreateConfiguratorModuleArgs,
  MutationCreateConsentsAndDeclarationsModuleArgs,
  MutationCreateCtsModuleArgs,
  MutationCreateCtsModuleSettingArgs,
  MutationCreateCustomerListEndpointArgs,
  MutationCreateDealerArgs,
  MutationCreateDealerSocialMediaArgs,
  MutationCreateDocusignModuleArgs,
  MutationCreateDocusignSettingArgs,
  MutationCreateDummyPrivatePageEndpointArgs,
  MutationCreateDummyWelcomePageEndpointArgs,
  MutationCreateEdmEmailSocialMediaArgs,
  MutationCreateEventArgs,
  MutationCreateEventApplicationEntrypointArgs,
  MutationCreateEventApplicationModuleArgs,
  MutationCreateFinanceProductArgs,
  MutationCreateFinderApplicationEntrypointArgs,
  MutationCreateFinderApplicationPrivateModuleArgs,
  MutationCreateFinderApplicationPublicAccessEntrypointArgs,
  MutationCreateFinderApplicationPublicModuleArgs,
  MutationCreateFinderVehicleManagementModuleArgs,
  MutationCreateFiservPaymentModuleArgs,
  MutationCreateFiservPaymentSettingsArgs,
  MutationCreateGiftVoucherModuleArgs,
  MutationCreateGroupConsentsAndDeclarationsArgs,
  MutationCreateInsuranceModuleArgs,
  MutationCreateInsuranceProductArgs,
  MutationCreateInsurerArgs,
  MutationCreateInventoryArgs,
  MutationCreateKycPresetArgs,
  MutationCreateLabelsArgs,
  MutationCreateLabelsModuleArgs,
  MutationCreateLanguagePackArgs,
  MutationCreateLaunchPadApplicationEntrypointArgs,
  MutationCreateLaunchPadModuleArgs,
  MutationCreateLeadFollowUpArgs,
  MutationCreateLeadListEndpointArgs,
  MutationCreateLocalCustomerManagementArgs,
  MutationCreateMaintenanceModuleArgs,
  MutationCreateMakeArgs,
  MutationCreateMarketingConsentsAndDeclarationsArgs,
  MutationCreateMarketingModuleArgs,
  MutationCreateMobilityAdditionalInfoArgs,
  MutationCreateMobilityAddonArgs,
  MutationCreateMobilityApplicationEntrypointArgs,
  MutationCreateMobilityModuleArgs,
  MutationCreateModelArgs,
  MutationCreateModelConfiguratorArgs,
  MutationCreateMyInfoModuleArgs,
  MutationCreateMyInfoSettingArgs,
  MutationCreateNamirialSigningModuleArgs,
  MutationCreateNewContactFromBpArgs,
  MutationCreateOidcModuleArgs,
  MutationCreateOptionsBlockArgs,
  MutationCreatePackageBlockArgs,
  MutationCreatePackageSettingArgs,
  MutationCreatePayGatePaymentModuleArgs,
  MutationCreatePayGatePaymentSettingsArgs,
  MutationCreatePorscheIdModuleArgs,
  MutationCreatePorscheMasterDataModuleArgs,
  MutationCreatePorschePaymentModuleArgs,
  MutationCreatePorschePaymentSettingsArgs,
  MutationCreatePorscheRetainModuleArgs,
  MutationCreatePromoCodeArgs,
  MutationCreatePromoCodeModuleArgs,
  MutationCreateRoleArgs,
  MutationCreateRouterArgs,
  MutationCreateRouterMenuArgs,
  MutationCreateSalesControlBoardModuleArgs,
  MutationCreateSalesOfferArgs,
  MutationCreateSalesOfferModuleArgs,
  MutationCreateShowroomVisitAppointmentFromLeadArgs,
  MutationCreateSimpleVehicleManagementModuleArgs,
  MutationCreateStandardApplicationEntrypointArgs,
  MutationCreateStandardApplicationModuleArgs,
  MutationCreateStandardApplicationPublicAccessEntrypointArgs,
  MutationCreateSubmodelArgs,
  MutationCreateTextConsentsAndDeclarationsArgs,
  MutationCreateTradeInModuleArgs,
  MutationCreateTrimBlockArgs,
  MutationCreateTtbPaymentModuleArgs,
  MutationCreateTtbPaymentSettingsArgs,
  MutationCreateUserArgs,
  MutationCreateUserGroupArgs,
  MutationCreateUserlikeChatbotModuleArgs,
  MutationCreateUserlikeChatbotSettingsArgs,
  MutationCreateVariantArgs,
  MutationCreateVariantConfiguratorArgs,
  MutationCreateVehicleDataWithPorscheCodeIntegrationModuleArgs,
  MutationCreateVisitAppointmentModuleArgs,
  MutationCreateWebCalcSettingArgs,
  MutationCreateWebPageArgs,
  MutationCreateWebPageEndpointArgs,
  MutationCreateWebsiteModuleArgs,
  MutationCreateWebsiteSocialMediaArgs,
  MutationCreateWhatsappLiveChatModuleArgs,
  MutationCreateWhatsappLiveChatSettingsArgs,
  MutationDeclineApplicationArgs,
  MutationDeleteAdditionalDetailArgs,
  MutationDeleteAdyenPaymentSettingsArgs,
  MutationDeleteApplicationDocumentArgs,
  MutationDeleteAppointmentModuleAssetArgs,
  MutationDeleteAutoplayModuleSettingArgs,
  MutationDeleteBankArgs,
  MutationDeleteBankAssetArgs,
  MutationDeleteBannerArgs,
  MutationDeleteBannerImageArgs,
  MutationDeleteCampaignArgs,
  MutationDeleteCompanyArgs,
  MutationDeleteCompanyAssetArgs,
  MutationDeleteConfiguratorDescriptionImageArgs,
  MutationDeleteConfiguratorModuleAssetArgs,
  MutationDeleteConsentsAndDeclarationsArgs,
  MutationDeleteCtsModuleSettingArgs,
  MutationDeleteDealerArgs,
  MutationDeleteDealerSocialMediaArgs,
  MutationDeleteDealerSocialMediaAssetArgs,
  MutationDeleteDocusignSettingArgs,
  MutationDeleteEdmEmailSocialMediaArgs,
  MutationDeleteEdmSocialMediaAssetArgs,
  MutationDeleteEndpointArgs,
  MutationDeleteEventArgs,
  MutationDeleteEventApplicationModuleAssetArgs,
  MutationDeleteEventLevelAssetArgs,
  MutationDeleteFinanceProductArgs,
  MutationDeleteFinderApplicationModuleAssetArgs,
  MutationDeleteFiservPaymentSettingsArgs,
  MutationDeleteGiftVoucherDocumentArgs,
  MutationDeleteGiftVoucherModuleAssetArgs,
  MutationDeleteInsuranceProductArgs,
  MutationDeleteInsurerArgs,
  MutationDeleteInventoriesArgs,
  MutationDeleteKycPresetArgs,
  MutationDeleteLabelsArgs,
  MutationDeleteLanguagePackArgs,
  MutationDeleteLeadDocumentArgs,
  MutationDeleteMaintenanceModuleAssetArgs,
  MutationDeleteMobilityArgs,
  MutationDeleteMobilityModuleEmailAssetArgs,
  MutationDeleteModelConfiguratorArgs,
  MutationDeleteModelConfiguratorAssetArgs,
  MutationDeleteModuleArgs,
  MutationDeleteMyInfoSettingArgs,
  MutationDeleteOptionsBlockArgs,
  MutationDeletePackageSettingArgs,
  MutationDeletePayGatePaymentSettingsArgs,
  MutationDeletePorschePaymentSettingsArgs,
  MutationDeletePromoCodeArgs,
  MutationDeleteRoleArgs,
  MutationDeleteRouterArgs,
  MutationDeleteSalesOfferDocumentArgs,
  MutationDeleteStandardApplicationModuleAssetArgs,
  MutationDeleteStockAssetArgs,
  MutationDeleteStockInventoryArgs,
  MutationDeleteTtbPaymentSettingsArgs,
  MutationDeleteUserArgs,
  MutationDeleteUserAssetArgs,
  MutationDeleteUserGroupArgs,
  MutationDeleteUserlikeChatbotSettingsArgs,
  MutationDeleteVariantAssetArgs,
  MutationDeleteVariantConfiguratorArgs,
  MutationDeleteVariantConfiguratorColorSettingAssetArgs,
  MutationDeleteVariantConfiguratorMatrixAssetArgs,
  MutationDeleteVariantConfiguratorOptionAssetArgs,
  MutationDeleteVariantConfiguratorOptionSettingAssetArgs,
  MutationDeleteVariantConfiguratorPackageAdditionalDetailsAssetArgs,
  MutationDeleteVariantConfiguratorPackageSectionImageAssetArgs,
  MutationDeleteVariantConfiguratorTrimSettingAssetArgs,
  MutationDeleteVehicleArgs,
  MutationDeleteVisitAppointmentModuleAssetArgs,
  MutationDeleteWebPageArgs,
  MutationDeleteWebPageBlockArgs,
  MutationDeleteWebPageImageArgs,
  MutationDeleteWebsiteSocialMediaArgs,
  MutationDeleteWebsiteSocialMediaAssetArgs,
  MutationDeleteWhatsappLiveChatSettingsArgs,
  MutationDownloadSalesOfferDocumentArgs,
  MutationDownloadSpecificationDocumentArgs,
  MutationDraftConfiguratorApplicationArgs,
  MutationDraftEventApplicationArgs,
  MutationDraftFinderApplicationArgs,
  MutationDraftFinderApplicationFromLeadArgs,
  MutationDraftGiftVoucherArgs,
  MutationDraftLaunchpadApplicationArgs,
  MutationDraftMobilityApplicationArgs,
  MutationDraftStandardApplicationArgs,
  MutationDraftStandardApplicationFromLeadArgs,
  MutationEnableAuthenticatorArgs,
  MutationEndTestDriveArgs,
  MutationExportCompanyConfigurationArgs,
  MutationExtendConfiguratorStockExpiryArgs,
  MutationExtendEventJourneyExpiryArgs,
  MutationExtendFinderVehicleExpiryArgs,
  MutationExtendGiftVoucherJourneyExpiryArgs,
  MutationExtendMobilityStockExpiryArgs,
  MutationGenerateAmendMobilityApplicationAccessArgs,
  MutationGenerateCancelMobilityApplicationAccessArgs,
  MutationGenerateRemoteJourneyPasscodeArgs,
  MutationGenerateSalesOfferJourneyTokenArgs,
  MutationGenerateSigningOtpArgs,
  MutationGenerateWebCredentialAuthenticationArgs,
  MutationGetAgreementsAndKycFieldsFromUpdatedConfigurationArgs,
  MutationImportCompanyConfigurationArgs,
  MutationImportConfiguratorVariantPricesArgs,
  MutationImportDealersArgs,
  MutationImportFinderVehiclesLtaArgs,
  MutationImportInventoriesArgs,
  MutationImportLanguagePackArgs,
  MutationImportMakeArgs,
  MutationImportModelArgs,
  MutationImportSalesControlBoardDataArgs,
  MutationImportSubModelArgs,
  MutationImportVariantArgs,
  MutationImportVariantImagesArgs,
  MutationInitialSalesOfferPaymentArgs,
  MutationInitialSalesOfferSigningArgs,
  MutationMarkAsContactedArgs,
  MutationMarkLeadAsLostArgs,
  MutationProceedWithCustomerDeviceArgs,
  MutationQualifyLeadArgs,
  MutationQualifyLeadWithCapValuesArgs,
  MutationRefreshApplicationStatusArgs,
  MutationReleaseFinderVehicleExpiryArgs,
  MutationReleaseReservedConfiguratorStockArgs,
  MutationReleaseReservedMobilityStockArgs,
  MutationRemoveDealerOnUserGroupArgs,
  MutationRemovePermissionOnRoleArgs,
  MutationRemoveUserOnRoleArgs,
  MutationRemoveUserOnUserGroupArgs,
  MutationRemoveVehicleLtaArgs,
  MutationRequestDisbursementArgs,
  MutationRequestReleaseLetterArgs,
  MutationResendSmsOtpForResetPasswordArgs,
  MutationResendSmsOtpForUpdateEmailArgs,
  MutationResetFinderVehicleArgs,
  MutationResubmitLeadToCapArgs,
  MutationRevokeUserSessionArgs,
  MutationRevokeWebPublicKeyCredentialArgs,
  MutationSendActivationLinkArgs,
  MutationSendMobileVerificationOtpArgs,
  MutationSendSalesOfferArgs,
  MutationShareSalesOfferDocumentArgs,
  MutationShareStandardApplicationArgs,
  MutationStartTestDriveArgs,
  MutationSubmitAdyenPaymentArgs,
  MutationSubmitApplicantAgreementsArgs,
  MutationSubmitApplicantAppointmentArgs,
  MutationSubmitApplicantKycArgs,
  MutationSubmitApplicantVisitAppointmentArgs,
  MutationSubmitApplicationQuotationArgs,
  MutationSubmitChangesArgs,
  MutationSubmitFiservPaymentArgs,
  MutationSubmitGiftVoucherAdyenPaymentArgs,
  MutationSubmitGiftVoucherApplicantAgreementsArgs,
  MutationSubmitGiftVoucherApplicantKycArgs,
  MutationSubmitGiftVoucherFiservPaymentArgs,
  MutationSubmitGiftVoucherPayGatePaymentArgs,
  MutationSubmitGiftVoucherPorschePaymentArgs,
  MutationSubmitGiftVoucherTtbPaymentArgs,
  MutationSubmitGuarantorAgreementsArgs,
  MutationSubmitGuarantorKycArgs,
  MutationSubmitIntentAndAssignArgs,
  MutationSubmitPayGatePaymentArgs,
  MutationSubmitPorschePaymentArgs,
  MutationSubmitSalesOfferPorschePaymentArgs,
  MutationSubmitSigningOtpArgs,
  MutationSubmitTestDriveAgreementsArgs,
  MutationSubmitTestDriveKycArgs,
  MutationSubmitTtbPaymentArgs,
  MutationSynchronizeFinderVehicleArgs,
  MutationSynchronizePorscheMasterDataArgs,
  MutationTakeOutStockInventoryArgs,
  MutationUnqualifyApplicationArgs,
  MutationUnqualifyLeadArgs,
  MutationUpdateAccountArgs,
  MutationUpdateAdditionalDetailArgs,
  MutationUpdateAdyenPaymentModuleArgs,
  MutationUpdateAdyenPaymentSettingsArgs,
  MutationUpdateApplicantAppointmentArgs,
  MutationUpdateApplicantVisitAppointmentArgs,
  MutationUpdateApplicationArgs,
  MutationUpdateApplicationDealershipAssignmentsByDealerArgs,
  MutationUpdateApplicationFieldsArgs,
  MutationUpdateApplicationListEndpointArgs,
  MutationUpdateAppointmentDataArgs,
  MutationUpdateAppointmentModuleArgs,
  MutationUpdateAppointmentModuleEmailContentArgs,
  MutationUpdateAssigneeOnApplicationArgs,
  MutationUpdateAssigneeOnLeadArgs,
  MutationUpdateAutoplayModuleArgs,
  MutationUpdateAutoplayModuleSettingArgs,
  MutationUpdateBankArgs,
  MutationUpdateBankAvailableFinanceProductTypesArgs,
  MutationUpdateBankModuleArgs,
  MutationUpdateBannerArgs,
  MutationUpdateBasicSigningModuleArgs,
  MutationUpdateCampaignArgs,
  MutationUpdateCapModuleArgs,
  MutationUpdateCheckboxConsentsAndDeclarationsArgs,
  MutationUpdateColorBlockArgs,
  MutationUpdateCompanyArgs,
  MutationUpdateConfiguratorApplicationArgs,
  MutationUpdateConfiguratorApplicationConfigurationArgs,
  MutationUpdateConfiguratorApplicationEntrypointArgs,
  MutationUpdateConfiguratorApplicationJourneyArgs,
  MutationUpdateConfiguratorModuleArgs,
  MutationUpdateConfiguratorModuleEmailContentsArgs,
  MutationUpdateConsentOrderListArgs,
  MutationUpdateConsentsAndDeclarationsModuleArgs,
  MutationUpdateCtsFinanceProductsArgs,
  MutationUpdateCtsInsuranceProductsArgs,
  MutationUpdateCtsModuleArgs,
  MutationUpdateCtsModuleSettingArgs,
  MutationUpdateCustomerArgs,
  MutationUpdateCustomerListEndpointArgs,
  MutationUpdateDbsBankIntegrationSettingArgs,
  MutationUpdateDealerArgs,
  MutationUpdateDealerSocialMediaArgs,
  MutationUpdateDepositSalesOfferArgs,
  MutationUpdateDisplayNameArgs,
  MutationUpdateDocusignModuleArgs,
  MutationUpdateDocusignSettingArgs,
  MutationUpdateDummyPrivatePageEndpointArgs,
  MutationUpdateDummyWelcomePageEndpointArgs,
  MutationUpdateEazyInsurerIntegrationSettingArgs,
  MutationUpdateEdmEmailSocialMediaArgs,
  MutationUpdateEmailBankIntegrationSettingArgs,
  MutationUpdateEmailFromTokenArgs,
  MutationUpdateEmailInsurerIntegrationSettingArgs,
  MutationUpdateEnbdBankIntegrationSettingArgs,
  MutationUpdateEventArgs,
  MutationUpdateEventApplicationArgs,
  MutationUpdateEventApplicationEntrypointArgs,
  MutationUpdateEventApplicationFinanceProductAssignmentsArgs,
  MutationUpdateEventApplicationModuleEmailContentsArgs,
  MutationUpdateEventApplicationModuleMainDetailsArgs,
  MutationUpdateEventApplicationVehicleAssignmentsArgs,
  MutationUpdateFinanceProductArgs,
  MutationUpdateFinanceSalesOfferArgs,
  MutationUpdateFinderApplicationArgs,
  MutationUpdateFinderApplicationDraftArgs,
  MutationUpdateFinderApplicationEntrypointArgs,
  MutationUpdateFinderApplicationJourneyArgs,
  MutationUpdateFinderApplicationPrivateModuleArgs,
  MutationUpdateFinderApplicationPrivateModuleEmailContentsArgs,
  MutationUpdateFinderApplicationPublicAccessEntrypointArgs,
  MutationUpdateFinderApplicationPublicModuleArgs,
  MutationUpdateFinderApplicationPublicModuleEmailContentsArgs,
  MutationUpdateFinderVehicleArgs,
  MutationUpdateFinderVehicleManagementModuleArgs,
  MutationUpdateFiservPaymentModuleArgs,
  MutationUpdateFiservPaymentSettingsArgs,
  MutationUpdateGiftVoucherArgs,
  MutationUpdateGiftVoucherModuleArgs,
  MutationUpdateGiftVoucherModuleEmailContentArgs,
  MutationUpdateGroupConsentsAndDeclarationsArgs,
  MutationUpdateHlfBankIntegrationSettingArgs,
  MutationUpdateHlfBankV2IntegrationSettingArgs,
  MutationUpdateInsuranceModuleArgs,
  MutationUpdateInsuranceProductArgs,
  MutationUpdateInsuranceSalesOfferArgs,
  MutationUpdateInsurerArgs,
  MutationUpdateInventoryArgs,
  MutationUpdateKycPresetArgs,
  MutationUpdateLabelsArgs,
  MutationUpdateLabelsModuleArgs,
  MutationUpdateLanguagePackArgs,
  MutationUpdateLaunchPadApplicationEntrypointArgs,
  MutationUpdateLaunchPadModuleArgs,
  MutationUpdateLaunchPadModuleVehicleAssignmentsArgs,
  MutationUpdateLaunchpadApplicationTradeInArgs,
  MutationUpdateLeadArgs,
  MutationUpdateLeadFollowUpArgs,
  MutationUpdateLeadListEndpointArgs,
  MutationUpdateLocalCustomerManagementArgs,
  MutationUpdateLocalCustomerManagementKycFieldsArgs,
  MutationUpdateMainDetailsSalesOfferArgs,
  MutationUpdateMaintenanceModuleArgs,
  MutationUpdateMaintenanceModuleDetailsArgs,
  MutationUpdateMakeArgs,
  MutationUpdateMarketingConsentsAndDeclarationsArgs,
  MutationUpdateMarketingModuleArgs,
  MutationUpdateMaybankIntegrationSettingArgs,
  MutationUpdateMobilityAdditionalInfoArgs,
  MutationUpdateMobilityAddonArgs,
  MutationUpdateMobilityApplicationArgs,
  MutationUpdateMobilityApplicationDraftArgs,
  MutationUpdateMobilityApplicationEntrypointArgs,
  MutationUpdateMobilityDepositAmountArgs,
  MutationUpdateMobilityEmailContentArgs,
  MutationUpdateMobilityModuleArgs,
  MutationUpdateModelArgs,
  MutationUpdateModelConfiguratorArgs,
  MutationUpdateMyInfoModuleArgs,
  MutationUpdateMyInfoSettingArgs,
  MutationUpdateNamirialSettingsArgs,
  MutationUpdateNamirialSigningModuleArgs,
  MutationUpdateNamirialSigningSettingsArgs,
  MutationUpdateOidcClientArgs,
  MutationUpdateOidcModuleArgs,
  MutationUpdateOptionsBlockArgs,
  MutationUpdatePackageBlockArgs,
  MutationUpdatePackageSettingArgs,
  MutationUpdatePayGatePaymentModuleArgs,
  MutationUpdatePayGatePaymentSettingsArgs,
  MutationUpdatePorscheIdModuleArgs,
  MutationUpdatePorscheMasterDataModuleArgs,
  MutationUpdatePorschePaymentModuleArgs,
  MutationUpdatePorschePaymentSettingsArgs,
  MutationUpdatePorscheRetainModuleArgs,
  MutationUpdatePromoCodeArgs,
  MutationUpdatePromoCodeModuleArgs,
  MutationUpdateRoleArgs,
  MutationUpdateRouterArgs,
  MutationUpdateRouterMenuArgs,
  MutationUpdateRouterPathScriptsArgs,
  MutationUpdateSalesControlBoardModuleArgs,
  MutationUpdateSalesControlBoardModuleByDealerArgs,
  MutationUpdateSalesOfferModuleArgs,
  MutationUpdateSalesOfferModuleEmailContentArgs,
  MutationUpdateSimpleVehicleManagementModuleArgs,
  MutationUpdateStandardApplicationConfigurationArgs,
  MutationUpdateStandardApplicationDealershipArgs,
  MutationUpdateStandardApplicationDraftArgs,
  MutationUpdateStandardApplicationEntrypointArgs,
  MutationUpdateStandardApplicationFinanceProductAssignmentsArgs,
  MutationUpdateStandardApplicationJourneyArgs,
  MutationUpdateStandardApplicationModuleEmailContentArgs,
  MutationUpdateStandardApplicationModuleMainDetailsArgs,
  MutationUpdateStandardApplicationPublicAccessEntrypointArgs,
  MutationUpdateStandardApplicationVehicleAssignmentsArgs,
  MutationUpdateStockInventoryArgs,
  MutationUpdateTestDriveDataArgs,
  MutationUpdateTextConsentsAndDeclarationsArgs,
  MutationUpdateTradeInModuleArgs,
  MutationUpdateTradeInSalesOfferArgs,
  MutationUpdateTradeInSettingArgs,
  MutationUpdateTrimBlockArgs,
  MutationUpdateTtbPaymentModuleArgs,
  MutationUpdateTtbPaymentSettingsArgs,
  MutationUpdateUobBankIntegrationSettingArgs,
  MutationUpdateUserArgs,
  MutationUpdateUserGroupArgs,
  MutationUpdateUserlikeChatbotModuleArgs,
  MutationUpdateUserlikeChatbotSettingsArgs,
  MutationUpdateVariantArgs,
  MutationUpdateVariantConfiguratorArgs,
  MutationUpdateVariantImageOrderingArgs,
  MutationUpdateVariantLabelsArgs,
  MutationUpdateVehicleDataWithPorscheCodeIntegrationModuleArgs,
  MutationUpdateVehicleSalesOfferArgs,
  MutationUpdateVisitAppointmentModuleArgs,
  MutationUpdateVisitAppointmentModuleEmailContentArgs,
  MutationUpdateWebCalcSettingArgs,
  MutationUpdateWebPageArgs,
  MutationUpdateWebPageEndpointArgs,
  MutationUpdateWebsiteModuleArgs,
  MutationUpdateWebsiteSocialMediaArgs,
  MutationUpdateWhatsappLiveChatModuleArgs,
  MutationUpdateWhatsappLiveChatSettingsArgs,
  MutationUploadApplicationDocumentArgs,
  MutationUploadApplicationDocumentsArgs,
  MutationUploadAppointmentModuleAssetArgs,
  MutationUploadBankAssetArgs,
  MutationUploadBannerImageArgs,
  MutationUploadCompanyAssetArgs,
  MutationUploadConfiguratorDescriptionImageArgs,
  MutationUploadConfiguratorModuleAssetArgs,
  MutationUploadDealerSocialMediaAssetArgs,
  MutationUploadEdmSocialMediaAssetArgs,
  MutationUploadEventApplicationModuleAssetArgs,
  MutationUploadEventLevelAssetArgs,
  MutationUploadFinderApplicationModuleAssetArgs,
  MutationUploadGiftVoucherDocumentArgs,
  MutationUploadGiftVoucherModuleAssetArgs,
  MutationUploadLeadDocumentArgs,
  MutationUploadLeadDocumentsArgs,
  MutationUploadMaintenanceModuleAssetArgs,
  MutationUploadMobilityModuleEmailAssetArgs,
  MutationUploadModelConfiguratorAssetArgs,
  MutationUploadSalesOfferDocumentArgs,
  MutationUploadStandardApplicationModuleAssetArgs,
  MutationUploadStockAssetArgs,
  MutationUploadUserAssetArgs,
  MutationUploadVariantAssetArgs,
  MutationUploadVariantConfiguratorColorSettingAssetArgs,
  MutationUploadVariantConfiguratorMatrixAssetArgs,
  MutationUploadVariantConfiguratorOptionAssetArgs,
  MutationUploadVariantConfiguratorOptionSettingAssetArgs,
  MutationUploadVariantConfiguratorPackageAdditionalDetailsAssetArgs,
  MutationUploadVariantConfiguratorPackageSectionImageAssetArgs,
  MutationUploadVariantConfiguratorTrimSettingAssetArgs,
  MutationUploadVehicleSalesOfferSpecificationDocumentArgs,
  MutationUploadVisitAppointmentModuleAssetArgs,
  MutationUploadWebPageImageArgs,
  MutationUploadWebsiteSocialMediaAssetArgs,
  MutationUpsertEventUserIdsArgs,
  MutationUpsertKycPresetInEventArgs,
  MutationValidateRemoteJourneyPasscodeArgs,
  MutationValidateSalesOfferRemoteJourneyPasscodeArgs,
  MutationValidateSmsOtpForResetPasswordArgs,
  MutationValidateSmsOtpForUpdateEmailArgs,
  MutationVerifyMobileOtpArgs,
  MyInfoCallbackLink,
  MyInfoData,
  MyInfoModule,
  MyInfoModuleSettings,
  MyInfoSetting,
  MyInfoSettingInput,
  NamirialSetting,
  NamirialSettingsInput,
  NamirialSigningLink,
  NamirialSigningModule,
  NamirialSigningModuleSettings,
  NearbyDealerFilteringRule,
  NewZealandApplicationFinanceSettings,
  NewZealandApplicationFinancing,
  NewZealandApplicationInsuranceSettings,
  NewZealandApplicationInsurancing,
  NewZealandApplicationMarket,
  NewZealandApplicationMarketTypeInput,
  NewZealandApplicationModuleMarketTypeInput,
  NumberUnit,
  NumberUnitLocalizeArgs,
  NzFeesDealershipMarket,
  NzFeesDealershipMarketInput,
  NzFeesDealershipMarketOverrides,
  NzFeesDealershipMarketOverridesInput,
  OidcAuthorizePayload,
  OidcClient,
  OidcClientSettings,
  OidcModule,
  OidcModuleSettings,
  OcrImage,
  Option,
  OptionInput,
  OptionSettings,
  OptionSettingsInput,
  OptionalPhone,
  OptionalPhonePayload,
  OptionalRangeValue,
  OptionalRangeValuePayload,
  OptionsBlock,
  OptionsBlockInput,
  OtherVehicleInformation,
  OtherVehicleInformationSettings,
  PackageBlock,
  PackageBlockInput,
  PackageInformation,
  PackageInformationContent,
  PackageSettings,
  PackageSettingsInput,
  PackageType,
  PackageTypeInput,
  PackageTypeWithDescription,
  PackageTypeWithDescriptionInput,
  PackageTypeWithPrice,
  PackageTypeWithPriceInput,
  PaginatedAdyenPaymentSettings,
  PaginatedApplications,
  PaginatedAuditTrails,
  PaginatedBanner,
  PaginatedCampaign,
  PaginatedCapBusinessPartnerData,
  PaginatedCapLeadData,
  PaginatedCompanies,
  PaginatedCompanyOptions,
  PaginatedConsentsAndDeclarations,
  PaginatedCustomers,
  PaginatedDealers,
  PaginatedDocusignSettings,
  PaginatedEvent,
  PaginatedFinanceProducts,
  PaginatedFiservPaymentSettings,
  PaginatedGiftVoucher,
  PaginatedInsuranceProducts,
  PaginatedInsurers,
  PaginatedInventory,
  PaginatedLabels,
  PaginatedLanguagePacks,
  PaginatedLeads,
  PaginatedLocalMakes,
  PaginatedLocalModels,
  PaginatedLocalVariants,
  PaginatedMobilities,
  PaginatedMobilityHomeDelivery,
  PaginatedMobilityLocation,
  PaginatedModelConfigurator,
  PaginatedModules,
  PaginatedModulesInDealer,
  PaginatedMyInfoModules,
  PaginatedMyInfoSettings,
  PaginatedPayGatePaymentSettings,
  PaginatedPorschePaymentSettings,
  PaginatedPromoCode,
  PaginatedRoles,
  PaginatedRouters,
  PaginatedStockAuditTrails,
  PaginatedStockInventory,
  PaginatedSystemBanks,
  PaginatedTradeIns,
  PaginatedTtbPaymentSettings,
  PaginatedUserGroups,
  PaginatedUserlikeChatbotSettings,
  PaginatedUsers,
  PaginatedVariantConfigurator,
  PaginatedVehicles,
  PaginatedWebCalcSettings,
  PaginatedWebPage,
  PaginatedWhatsappLiveChatSettings,
  Pagination,
  PaginationInfo,
  PathScript,
  PathScriptSettings,
  PayGatePaymentModule,
  PayGatePaymentModuleSettings,
  PayGatePaymentRedirectionLink,
  PayGatePaymentSetting,
  PayGatePaymentSettingsInput,
  PayGatePaymentUpdateSettingsInput,
  PaymentSetting,
  PaymentSettings,
  PaymentSettingsPayload,
  Period,
  PeriodPayload,
  Permission,
  PermissionWithOrigins,
  Phone,
  PhonePayload,
  PorscheFinderVehicle,
  PorscheFinderVehicleImagesArgs,
  PorscheIdCallbackLink,
  PorscheIdCurrentVehicle,
  PorscheIdData,
  PorscheIdModule,
  PorscheIdModuleSettings,
  PorscheIdSetting,
  PorscheIdSettingInput,
  PorscheMasterDataModule,
  PorscheMasterDataModuleSettings,
  PorscheMasterDataSecrets,
  PorscheMasterDataSetting,
  PorscheMasterDataSyncCreated,
  PorscheMasterDataSyncResult,
  PorschePaymentMethods,
  PorschePaymentModule,
  PorschePaymentModuleSettings,
  PorschePaymentRedirectionLink,
  PorschePaymentSetting,
  PorschePaymentSettingsInput,
  PorschePaymentType,
  PorschePaymentUpdateSettingsInput,
  PorscheRetain,
  PorscheRetainModule,
  PorscheRetainModuleIntegrationSetting,
  PorscheRetainModuleSettings,
  PorscheV3Layout,
  PorscheVehicleData,
  PorscheVehicleDataFeature,
  PorscheVehicleImages,
  Power,
  PowerLocalizeLabelArgs,
  PowerLocalizeValueArgs,
  PrefetchKycConsents,
  Price,
  PriceLocalizeArgs,
  ProceedWithCustomerLink,
  ProgressGoal,
  PromoCode,
  PromoCodeFilteringRule,
  PromoCodeModule,
  PromoCodeModuleSettings,
  PromoCodeSettings,
  PromoCodeSortingRule,
  PromoType,
  PromoTypeInput,
  QualifyLeadInput,
  Query,
  QueryCalculateLowestMonthlyInstalmentArgs,
  QueryCheckAvailableInventoryStockByVariantConfiguratorSettingsArgs,
  QueryCompanyFilterListArgs,
  QueryGenerateAuthenticatorChallengeArgs,
  QueryGetActiveCampaignsArgs,
  QueryGetAddressAutocompleteArgs,
  QueryGetApplicationArgs,
  QueryGetApplicationAuditTrailKindsArgs,
  QueryGetApplicationAuditTrailsArgs,
  QueryGetApplicationCounterArgs,
  QueryGetApplicationDocumentDownloadLinkArgs,
  QueryGetApplicationJourneyArgs,
  QueryGetApplicationSigningStatusArgs,
  QueryGetApplicationsListFiltersArgs,
  QueryGetBankArgs,
  QueryGetBannerArgs,
  QueryGetBannerByModuleArgs,
  QueryGetCampaignArgs,
  QueryGetCapBusinessPartnerDetailsArgs,
  QueryGetCapBusinessPartnersArgs,
  QueryGetCapLeadsFromBusinessPartnerArgs,
  QueryGetCapVehicleModelsArgs,
  QueryGetCompanyArgs,
  QueryGetConditionDetailArgs,
  QueryGetConsentsAndDeclarationsArgs,
  QueryGetContactsFromBpArgs,
  QueryGetCustomerArgs,
  QueryGetCustomerAuditTrailsArgs,
  QueryGetCustomerFieldsFromFilesArgs,
  QueryGetCustomerListFiltersArgs,
  QueryGetDataFromMyInfoArgs,
  QueryGetDataFromPorscheIdArgs,
  QueryGetDbsInstantApprovalRateArgs,
  QueryGetDealerArgs,
  QueryGetDepositPaymentPerDayArgs,
  QueryGetEventArgs,
  QueryGetEventApplicationsArgs,
  QueryGetFinanceProductArgs,
  QueryGetFinanceProductsArgs,
  QueryGetFinderVehicleArgs,
  QueryGetFinderVehicleSyncStatusArgs,
  QueryGetFirstEventRouterPathArgs,
  QueryGetGiftVoucherArgs,
  QueryGetGiftVoucherJourneyArgs,
  QueryGetGiftVouchersListFiltersArgs,
  QueryGetImportedFpTableFromExcelArgs,
  QueryGetImportedLocationFromExcelArgs,
  QueryGetInsurancePremiumArgs,
  QueryGetInsuranceProductArgs,
  QueryGetInsuranceProductsArgs,
  QueryGetInsurerArgs,
  QueryGetInsurersByModuleIdArgs,
  QueryGetInventoriesByMobilityModuleIdArgs,
  QueryGetInventoryArgs,
  QueryGetInventoryAuditTrailsArgs,
  QueryGetInventoryOptionsArgs,
  QueryGetLabelArgs,
  QueryGetLabelsByFeatureModuleIdArgs,
  QueryGetLanguagePackArgs,
  QueryGetLanguagePackListFiltersArgs,
  QueryGetLeadArgs,
  QueryGetLeadDocumentDownloadLinkArgs,
  QueryGetLeadsListFiltersArgs,
  QueryGetMarketingDashboardArgs,
  QueryGetMarketingDashboardFilterOptionArgs,
  QueryGetMobilityArgs,
  QueryGetMobilityHomeDeliveryArgs,
  QueryGetMobilityLocationArgs,
  QueryGetMobilityVehicleFiltersArgs,
  QueryGetModelConfiguratorArgs,
  QueryGetModelConfiguratorDescriptionImagesArgs,
  QueryGetModelConfiguratorFiltersArgs,
  QueryGetModuleArgs,
  QueryGetMonthOfImportOptionsArgs,
  QueryGetMyInfoAuthorizeUrlArgs,
  QueryGetNearbyDealersArgs,
  QueryGetPopularEventArgs,
  QueryGetPopularVariantArgs,
  QueryGetPorscheIdAuthorizeUrlArgs,
  QueryGetPorschePaymentMethodsArgs,
  QueryGetPorschePaymentMethodsForSalesOfferArgs,
  QueryGetPorscheVehicleDataArgs,
  QueryGetPromoCodeArgs,
  QueryGetPromoCodeListingArgs,
  QueryGetReferenceApplicationListEndpointsArgs,
  QueryGetRoleArgs,
  QueryGetRouterArgs,
  QueryGetRouterEndpointListArgs,
  QueryGetSalesControlBoardArgs,
  QueryGetSalesControlBoardFilterOptionsArgs,
  QueryGetSalesOfferArgs,
  QueryGetSalesOfferJourneyArgs,
  QueryGetStockArgs,
  QueryGetStockAuditTrailsArgs,
  QueryGetStockInventoryByVariantConfiguratorArgs,
  QueryGetSubmittedApplicationPerDayArgs,
  QueryGetTopSalesPersonsArgs,
  QueryGetTradeInArgs,
  QueryGetTradeInAuditTrailsArgs,
  QueryGetTradeInListFiltersArgs,
  QueryGetUserArgs,
  QueryGetUserGroupArgs,
  QueryGetValidDiscountCodeArgs,
  QueryGetValidGiftVoucherByCodeArgs,
  QueryGetValidPromoCodeByCodeArgs,
  QueryGetVariantConfiguratorArgs,
  QueryGetVariantsOfAllConfiguratorsArgs,
  QueryGetVehicleArgs,
  QueryGetVehicleBySuiteIdArgs,
  QueryGetWebPageArgs,
  QueryGetWebPageOptionsArgs,
  QueryGetWebauthnKeysArgs,
  QueryHasValidPromoCodeArgs,
  QueryListAdyenPaymentSettingsArgs,
  QueryListApplicationsArgs,
  QueryListApplicationsModulesArgs,
  QueryListApplicationsStatusArgs,
  QueryListAvailablePermissionsArgs,
  QueryListAvailableUserGroupsArgs,
  QueryListAvailableUsersByRoleArgs,
  QueryListAvailableUsersForUserGroupArgs,
  QueryListBankOptionsArgs,
  QueryListBanksArgs,
  QueryListBannersArgs,
  QueryListCampaignsArgs,
  QueryListCompaniesArgs,
  QueryListCompaniesForSelectionArgs,
  QueryListConsentsAndDeclarationsArgs,
  QueryListCustomersArgs,
  QueryListDealerOptionsArgs,
  QueryListDealersArgs,
  QueryListDocusignSettingsArgs,
  QueryListEventFiltersArgs,
  QueryListEventsArgs,
  QueryListFinanceProductsArgs,
  QueryListFinderVehiclesArgs,
  QueryListFiservPaymentSettingsArgs,
  QueryListGiftVouchersArgs,
  QueryListInsuranceProductsArgs,
  QueryListInsurersArgs,
  QueryListInventoriesArgs,
  QueryListInventoriesFilterArgs,
  QueryListLabelsArgs,
  QueryListLanguagePacksArgs,
  QueryListLeadAuditTrailsArgs,
  QueryListLeadsArgs,
  QueryListLocalMakesArgs,
  QueryListLocalModelsArgs,
  QueryListLocalVariantsArgs,
  QueryListLocationConditionsArgs,
  QueryListLocationsArgs,
  QueryListMobilitiesArgs,
  QueryListMobilityHomeDeliveriesArgs,
  QueryListModelConfiguratorsArgs,
  QueryListModuleTypeArgs,
  QueryListModulesArgs,
  QueryListModulesForApplicationDownloadArgs,
  QueryListMyInfoModulesArgs,
  QueryListMyInfoSettingArgs,
  QueryListPorschePaymentSettingsArgs,
  QueryListPromoCodeCompaniesArgs,
  QueryListPublicAssigneesArgs,
  QueryListRolesArgs,
  QueryListRoutersArgs,
  QueryListSalesOfferAuditTrailsArgs,
  QueryListStockInventoriesArgs,
  QueryListTradeInsArgs,
  QueryListUserGroupsArgs,
  QueryListUserlikeChatbotSettingsArgs,
  QueryListUsersArgs,
  QueryListVariantConfiguratorsArgs,
  QueryListVehiclesArgs,
  QueryListVehiclesWithParametersArgs,
  QueryListWebCalcSettingsArgs,
  QueryListWebPagesArgs,
  QueryListWhatsappLiveChatSettingsArgs,
  QueryPrefetchAgreementsForContactArgs,
  QueryPrefetchAgreementsForEventArgs,
  QueryPrefetchAgreementsForLaunchPadArgs,
  QueryPrefetchAgreementsForLaunchPadAppointmentArgs,
  QueryPrefetchAgreementsForLaunchPadShowroomVisitArgs,
  QueryPrefetchAgreementsForStandardApplicationArgs,
  QueryPrefetchGiftVoucherKycConsentsArgs,
  QueryPrefetchKycFieldsForContactArgs,
  QueryPrefetchKycFieldsForEventArgs,
  QueryPrefetchKycFieldsForLaunchPadApplicationArgs,
  QueryPrefetchKycFieldsForLaunchPadAppointmentArgs,
  QueryPrefetchKycFieldsForQualifyArgs,
  QueryPrefetchKycFieldsForStandardApplicationArgs,
  QueryRefineTextArgs,
  QueryRetrieveBlockedAppointmentTimeSlotArgs,
  QueryRetrieveLinkArgs,
  QueryValidateFileArgs,
  RangeAmount,
  RangeAmountPayload,
  Rate,
  RateLocalizeArgs,
  ReferenceApplicationListEndpointFilter,
  ReferenceDetailPayload,
  ReferenceDetailSet,
  RelatedEntity,
  RequestReleaseLetterSettings,
  ReservationStage,
  ResetPasswordLink,
  ResetPasswordResponse,
  ResidualValueSettings,
  ResidualValueSettingsPayload,
  ResidualValueTableCell,
  ResidualValueTableCellForVehicleParameterFilter,
  ResidualValueTableCellForVehicleParameterPayload,
  ResidualValueTableCellPayload,
  RetainFinancingDetail,
  RetainInfo,
  RetainVehicleDetail,
  Role,
  RoleFilteringRule,
  RoleSettings,
  RoleSortingRule,
  RooftopColor,
  Rounding,
  RoundingPayload,
  Roundings,
  RoundingsPayload,
  Router,
  RouterFilteringRule,
  RouterSettings,
  SmtpEmailSettings,
  SalaryTransferredBankPayload,
  SalaryTransferredBankSet,
  SalesConsultantOption,
  SalesControlBoard,
  SalesControlBoardFilterOptions,
  SalesControlBoardFilterPayload,
  SalesControlBoardItemPerformance,
  SalesControlBoardLeadPerformance,
  SalesControlBoardModelTotalPerformance,
  SalesControlBoardModule,
  SalesControlBoardPerformance,
  SalesControlBoardTotalPerformance,
  SalesIncentive,
  SalesOffer,
  SalesOfferAgreementsCondition,
  SalesOfferApplication,
  SalesOfferApplicationConfiguration,
  SalesOfferConditionSettings,
  SalesOfferConsents,
  SalesOfferDocument,
  SalesOfferEmailContents,
  SalesOfferEmailContentsInput,
  SalesOfferFeatureConfigurationCore,
  SalesOfferJourney,
  SalesOfferKycPreset,
  SalesOfferModule,
  SalesOfferModuleDealerSpecificEmailContentInput,
  SalesOfferModuleEmailContents,
  SalesOfferModuleEmailContentsInput,
  SalesOfferModuleInitialSettings,
  SalesOfferModuleUpdateSettings,
  SalesOfferNamirialSigningLink,
  SalesOfferSignings,
  SalesOfferSinglePreOfferTemplateEmailContentInput,
  SalesOfferTemplateGroup,
  SalesOfferTemplateGroupInput,
  SalesPerformanceOverview,
  SalesPersonCodeMapping,
  Salesforce,
  Seller,
  SendSalesOfferLink,
  ShareSalesOfferDocumentInput,
  SimpleVehicleManagementModule,
  SimpleVehicleManagementSettings,
  SimpleVehicleManagementUpdateSettings,
  SimpleVersioning,
  SingaporeApplicationFinanceSettings,
  SingaporeApplicationFinancing,
  SingaporeApplicationInsuranceSettings,
  SingaporeApplicationInsurancing,
  SingaporeApplicationMarket,
  SingaporeApplicationMarketTypeInput,
  SingaporeApplicationModuleMarketTypeInput,
  SingleSelectOption,
  SingleSelectOptionSettings,
  SingleSelectOptionSettingsInput,
  SmsSettings,
  SmsSettingsPayload,
  StandardApplication,
  StandardApplicationConfiguration,
  StandardApplicationDraftFlow,
  StandardApplicationEntrypoint,
  StandardApplicationEntrypointSettings,
  StandardApplicationFinanceProductPayload,
  StandardApplicationLink,
  StandardApplicationModule,
  StandardApplicationModuleEmailContent,
  StandardApplicationModuleEmailContentCustomer,
  StandardApplicationModuleEmailContentCustomerInput,
  StandardApplicationModuleEmailContentInput,
  StandardApplicationModuleEmailContentSalesPerson,
  StandardApplicationModuleEmailContentSalesPersonInput,
  StandardApplicationModuleEmailContentShareSubmission,
  StandardApplicationModuleEmailContents,
  StandardApplicationModuleEmailContentsInput,
  StandardApplicationModuleEmailShareComparisonContentInput,
  StandardApplicationModuleInitialSettings,
  StandardApplicationModuleUpdateSettings,
  StandardApplicationPublicAccessEntrypoint,
  StandardApplicationPublicAccessEntrypointSettings,
  StandardApplicationVehiclePayload,
  StandardEquipmentCategory,
  StandardLead,
  StatisticCounterResult,
  StatisticCounterResultMonth,
  StatisticFilterPayload,
  StatisticInstantApprovalResult,
  StatisticInstantApprovalResultMonth,
  StatisticMonthlyResult,
  StatisticMonthlyResultItem,
  StockBlockingPeriod,
  StockBlockingPeriodInput,
  StockFilteringRule,
  StockInventory,
  StockInventoryUpdationSettings,
  StockSortingRule,
  StringDescription,
  StringDescriptionPayload,
  SubmitAppointmentBookingTimeSlot,
  SubmitIntentAndAssignInput,
  SubmodelFilter,
  Subscription,
  SubscriptionListenApplicationMessagesArgs,
  SubscriptionListenSystemMessagesArgs,
  SystemBank,
  SystemEmailSettings,
  SystemMessage,
  SystemSmsSettings,
  TaxLossSettingPayload,
  TaxLossSettings,
  TechnicalDataCategory,
  TechnicalDataItem,
  Template,
  TermSettings,
  TermSettingsPayload,
  TermWithVariant,
  TermWithVariantMileage,
  TestDriveBookingWindowSettings,
  TestDriveBookingWindowSettingsInput,
  TestDriveEmailOnEvent,
  TestDriveEmailOnEventInput,
  TestDriveFixedPeriod,
  TestDriveFixedPeriodInput,
  TestDriveProcessRedirectionLink,
  TextApplicationAgreement,
  TextCarouselWebPageBlock,
  TextCarouselWebPageBlockInput,
  TextConsentsAndDeclarations,
  TextConsentsAndDeclarationsSettings,
  TextImageWebPageBlock,
  TextImageWebPageBlockInput,
  TextImageWebPageButton,
  ThankYouPageContent,
  ThankYouPageContentInput,
  ThankyouPageRedirectButton,
  ThankyouPageRedirectButtonInput,
  TimeSlot,
  TimeSlotInput,
  TradeIn,
  TradeInCondition,
  TradeInFilteringRule,
  TradeInListFilter,
  TradeInModule,
  TradeInModuleSettings,
  TradeInSalesOffer,
  TradeInSalesOfferInput,
  TradeInSetting,
  TradeInSettingInput,
  TradeInSortingRule,
  TradeInStage,
  TradeInVehicle,
  TradeInVehiclePayload,
  TranslatedString,
  TranslatedStringInput,
  TranslatedStringOverrides,
  TranslatedStringOverridesInput,
  TranslatedText,
  Translation,
  TrimBlock,
  TrimBlockInput,
  TtbPaymentModule,
  TtbPaymentModuleSettings,
  TtbPaymentRedirectionLink,
  TtbPaymentSetting,
  TtbPaymentSettingsInput,
  TtbPaymentUpdateSettingsInput,
  TwilioSmsSettings,
  UaeIdentityPayload,
  UaeIdentitySet,
  UnavailableListing,
  UnladenWeight,
  UobBankIntegration,
  UobBankIntegrationSettings,
  UpdateApplicationJourneyFinancingSettings,
  UpdateEmailResponse,
  UpdateGiftVoucherSettings,
  UpdateLeadFollowUpInput,
  UpdateLeadInput,
  UpdateRouterSettings,
  UpdateWebsiteModulePayload,
  UpdatedConfigurationSettings,
  UpdatedMobilityApplication,
  UploadApplicationDocumentResult,
  UploadFileInterface,
  UploadGiftVoucherDocumentResult,
  UploadLeadDocumentResult,
  UploadedFile,
  UploadedFileWithPreview,
  UploadedVariantAsset,
  UpsertUserPayload,
  User,
  UserAvailableCompaniesArgs,
  UserPermissionWithOriginsArgs,
  UserRolesArgs,
  UserUserGroupsArgs,
  UserAssigned,
  UserDeletionResult,
  UserFilteringRule,
  UserGroup,
  UserGroupFilteringRule,
  UserGroupSettings,
  UserGroupSortingRule,
  UserSession,
  UserSessionRevoked,
  UserSettings,
  UserSortingRule,
  UserWebAuthnKey,
  UserlikeChatbotModule,
  UserlikeChatbotModuleInput,
  UserlikeChatbotSetting,
  UserlikeChatbotSettingsInput,
  VatRateSettingItem,
  VatRateSettingItemInput,
  VatRateSettings,
  VatRateSettingsInput,
  VsaSalesOffer,
  VariantBankCodeMapping,
  VariantBankCodeMappingInput,
  VariantConfigurator,
  VariantConfiguratorInventoriesArgs,
  VariantConfiguratorFilteringRule,
  VariantConfiguratorSettings,
  VariantConfiguratorSortingRule,
  VariantReferenceParameters,
  VariantReferenceParametersPayload,
  Vehicle,
  VehicleCharacteristic,
  VehicleDataWithPorscheCodeIntegrationModule,
  VehicleDataWithPorscheCodeIntegrationSetting,
  VehicleDataWithPorscheCodeIntegrationSettingInput,
  VehicleFilteringRule,
  VehicleImage,
  VehicleImageVariantsArgs,
  VehicleImageTypeEdge,
  VehicleImageVariant,
  VehicleImagesConnection,
  VehicleModelOption,
  VehicleModule,
  VehicleReferenceParameters,
  VehicleReferenceParametersPayload,
  VehicleSalesOffer,
  VehicleSalesOfferInput,
  VehicleSortingRule,
  VehicleVideo,
  VehiclesListByParameters,
  VerifyEmailUpdateLink,
  VisitAppointmentModule,
  VisitAppointmentModuleEmailContent,
  VisitAppointmentModuleEmailContentCustomer,
  VisitAppointmentModuleEmailContentCustomerInput,
  VisitAppointmentModuleEmailContentInput,
  VisitAppointmentModuleEmailContentSalesPerson,
  VisitAppointmentModuleEmailContentSalesPersonInput,
  VisitAppointmentModuleEmailContents,
  VisitAppointmentModuleEmailContentsInput,
  VisitAppointmentModuleInitialSettings,
  VisitAppointmentStage,
  Warranty,
  WebCalcSetting,
  WebCalcSettingPayload,
  WebPage,
  WebPageBlock,
  WebPageBlockInput,
  WebPageButton,
  WebPageButtonInput,
  WebPageEndpoint,
  WebPageEndpointSettings,
  WebPageFilteringRule,
  WebPageInput,
  WebPagePath,
  WebPageSortingRule,
  WebPublicKeyCredentialRegistrationRequest,
  WebsiteModule,
  WebsiteModuleLocationOption,
  WebsiteModuleLocationOptionPayload,
  WebsiteModuleOptions,
  WebsiteModuleOptionsPayload,
  WeekFunnel,
  WhatsappLiveChatModule,
  WhatsappLiveChatModuleInput,
  WhatsappLiveChatSetting,
  WhatsappLiveChatSettingsInput,
} from "./types";

export {
  AddressAutofillType,
  AddressComponentType,
  AddressSearchType,
  AddressType,
  AffinAutoFinanceCentre,
  AgeCalculationMethod,
  AmountUnit,
  ApplicationDocumentKind,
  ApplicationJourneySigningMode,
  ApplicationKind,
  ApplicationMarket,
  ApplicationQuotationDownPaymentTarget,
  ApplicationQuotationSource,
  ApplicationScenario,
  ApplicationSigningPurpose,
  ApplicationSigningStatus,
  ApplicationSortingField,
  ApplicationStage,
  ApplicationStatus,
  ApplicationType,
  AppointmentChangedVehicleKind,
  AppointmentModuleAsset,
  AssetCondition,
  AttendeeType,
  AuditTrailKind,
  AutoplayApiVersion,
  AverageMileageValueUnit,
  BalloonBasedOn,
  BankAsset,
  BankDisplayInSharePdf,
  BankIntegrationProvider,
  BankKind,
  BankSortingField,
  BannerAsset,
  BannerSortingField,
  BannerTextPosition,
  BlockType,
  BodyType,
  BookingPeriodType,
  BooleanWithEmpty,
  BusinessPartnerSearchField,
  CalculationMode,
  CalculationRounding,
  CalendarEventAttendeeRole,
  CalendarEventAttendeeStatus,
  CalendarEventEmailType,
  CalendarEventStatus,
  CalendarEventType,
  CampaignSortingField,
  CapMetadataKind,
  CoeCategory,
  ComboType,
  CompanyAsset,
  CompanyExportFormat,
  CompanyFilterListCollection,
  CompanySortingField,
  CompanyTheme,
  ConditionType,
  ConfiguratorModuleAsset,
  ConsentFeatureType,
  ConsentsAndDeclarationsPurpose,
  ConsentsAndDeclarationsSortingField,
  ConsentsAndDeclarationsType,
  ContentRefinementSourceAction,
  ContentRefinementSourceKind,
  CounterMethod,
  CurrentVehicleEquipmentLine,
  CurrentVehicleSource,
  CustomerKind,
  CustomerSortingField,
  DataField,
  DateTimeUnit,
  DayOfWeek,
  DbsPayloadScheme,
  DealerAsset,
  DealerSortingField,
  DealershipSettingType,
  DeriveMethod,
  DiscountType,
  DisplayPreference,
  DrivingLicenseType,
  Education,
  EmailContentUpdateType,
  EmailProvider,
  Emirate,
  EmploymentStatus,
  EnergyUnit,
  EngineType,
  Environment,
  EventLeadOriginType,
  EventMediumType,
  EventModuleAsset,
  EventSortingField,
  FieldDisplaySetting,
  FilterRuleType,
  FinanceProductBasedOn,
  FinanceProductKind,
  FinanceProductSortingField,
  FinanceProductType,
  FinancingPreferenceValue,
  FinderApplicationModuleAsset,
  FinderVehicleCondition,
  FinderVehicleFunctionality,
  FinderVehicleSortingField,
  FinderVehicleStatus,
  FinderVehicleSyncStatus,
  FpTableKind,
  FpTableType,
  GiftVoucherModuleAsset,
  GiftVoucherSortingField,
  GiftVoucherStatus,
  HomeDeliverySortingField,
  ImportStatus,
  IncomeType,
  InsuranceProductKind,
  InsuranceProductSortingField,
  InsuranceProductType,
  InsurerIntegrationProvider,
  InsurerSortingField,
  IntentType,
  InterestRateTableBasedOn,
  InventoryKind,
  InventorySortingField,
  JobTitle,
  JobTitleTh,
  KycFieldPurpose,
  LtaCoeCategory,
  LtaPreOwnerIdType,
  LabelsSortingField,
  LanguageOrientation,
  LanguagePackSortingField,
  LayoutType,
  LeadSortingField,
  LeadStageOption,
  LeadStatus,
  LeadType,
  LegalTextPosition,
  LocalCustomerFieldKey,
  LocalCustomerFieldSource,
  LocalMakeSortingField,
  LocalModelSortingField,
  LocalVariantSortingField,
  LocationSortingField,
  MfaSettingsType,
  MaskDirection,
  MatrixAsset,
  MaybankEnvironment,
  MileageUnit,
  MobilityAsset,
  MobilityBookingLocationType,
  MobilityEmailAssetType,
  MobilityKind,
  MobilityRecipient,
  MobilitySortingField,
  ModelConfiguratorAsset,
  ModelConfiguratorSortingField,
  ModuleAsset,
  ModuleRole,
  ModuleSortingField,
  ModuleType,
  MyInfoSettingVersion,
  OnlineOrderableState,
  OptionKind,
  OptionType,
  OrganizerType,
  PackageKind,
  PasswordConfiguration,
  PaymentMode,
  PaymentStatus,
  PreferenceValue,
  PromoCodeSortingField,
  PromoTypeEnum,
  PurchaseIntention,
  Purpose,
  PurposeOfVisit,
  ReferenceDetailRelationship,
  RelatedEntityType,
  RelationshipApplicant,
  ReservationStockStatus,
  ResidenceType,
  ResidentialStatus,
  RoleSortingField,
  SalesControlBoardDataType,
  SalesFilter,
  SalesOfferAgreementKind,
  SalesOfferDepositMethod,
  SalesOfferDocumentKind,
  SalesOfferDocumentStatus,
  SalesOfferFeatureKind,
  SalesOfferFeatureStatus,
  SettingType,
  SettlementInstalmentOn,
  SmsProvider,
  SortOption,
  SortingOrder,
  SortingRuleType,
  StandardApplicationModuleAsset,
  StockInventoryKind,
  StockSortingField,
  TemplateType,
  Timeframe,
  TradeInSortingField,
  TradeInStatus,
  TransmissionKind,
  TransportProtocolType,
  TtbPaymentFlag3ds,
  UserAsset,
  UserGroupSortingField,
  UserSortingField,
  VariantAsset,
  VariantConfiguratorAsset,
  VariantConfiguratorSortingField,
  VehicleKind,
  VehicleType,
  VisitAppointmentModuleAsset,
  WebPageAsset,
  WebPageBlockBackground,
  WebPageBlockPosition,
  WebPageBlockType,
  WebPageSortingField,
} from "./types";

export type {
  ValidateFileQueryVariables,
  ValidateFileQuery,
  ValidateFileQueryHookResult,
  ValidateFileLazyQueryHookResult,
  ValidateFileQueryResult,
  RetrieveNamespacesQueryVariables,
  RetrieveNamespacesQuery,
  RetrieveNamespacesQueryHookResult,
  RetrieveNamespacesLazyQueryHookResult,
  RetrieveNamespacesQueryResult,
  RetrieveLinkQueryVariables,
  RetrieveLinkQuery,
  RetrieveLinkQueryHookResult,
  RetrieveLinkLazyQueryHookResult,
  RetrieveLinkQueryResult,
  RetrieveBlockedAppointmentTimeSlotQueryVariables,
  RetrieveBlockedAppointmentTimeSlotQuery,
  RetrieveBlockedAppointmentTimeSlotQueryHookResult,
  RetrieveBlockedAppointmentTimeSlotLazyQueryHookResult,
  RetrieveBlockedAppointmentTimeSlotQueryResult,
  RefineTextQueryVariables,
  RefineTextQuery,
  RefineTextQueryHookResult,
  RefineTextLazyQueryHookResult,
  RefineTextQueryResult,
  PrefetchKycFieldsForStandardApplicationQueryVariables,
  PrefetchKycFieldsForStandardApplicationQuery,
  PrefetchKycFieldsForStandardApplicationQueryHookResult,
  PrefetchKycFieldsForStandardApplicationLazyQueryHookResult,
  PrefetchKycFieldsForStandardApplicationQueryResult,
  PrefetchKycFieldsForQualifyQueryVariables,
  PrefetchKycFieldsForQualifyQuery,
  PrefetchKycFieldsForQualifyQueryHookResult,
  PrefetchKycFieldsForQualifyLazyQueryHookResult,
  PrefetchKycFieldsForQualifyQueryResult,
  PrefetchKycFieldsForLaunchPadAppointmentQueryVariables,
  PrefetchKycFieldsForLaunchPadAppointmentQuery,
  PrefetchKycFieldsForLaunchPadAppointmentQueryHookResult,
  PrefetchKycFieldsForLaunchPadAppointmentLazyQueryHookResult,
  PrefetchKycFieldsForLaunchPadAppointmentQueryResult,
  PrefetchKycFieldsForLaunchPadApplicationQueryVariables,
  PrefetchKycFieldsForLaunchPadApplicationQuery,
  PrefetchKycFieldsForLaunchPadApplicationQueryHookResult,
  PrefetchKycFieldsForLaunchPadApplicationLazyQueryHookResult,
  PrefetchKycFieldsForLaunchPadApplicationQueryResult,
  PrefetchKycFieldsForEventQueryVariables,
  PrefetchKycFieldsForEventQuery,
  PrefetchKycFieldsForEventQueryHookResult,
  PrefetchKycFieldsForEventLazyQueryHookResult,
  PrefetchKycFieldsForEventQueryResult,
  PrefetchKycFieldsForContactQueryVariables,
  PrefetchKycFieldsForContactQuery,
  PrefetchKycFieldsForContactQueryHookResult,
  PrefetchKycFieldsForContactLazyQueryHookResult,
  PrefetchKycFieldsForContactQueryResult,
  PrefetchGiftVoucherKycConsentsQueryVariables,
  PrefetchGiftVoucherKycConsentsQuery,
  PrefetchGiftVoucherKycConsentsQueryHookResult,
  PrefetchGiftVoucherKycConsentsLazyQueryHookResult,
  PrefetchGiftVoucherKycConsentsQueryResult,
  PrefetchAgreementsForStandardApplicationQueryVariables,
  PrefetchAgreementsForStandardApplicationQuery,
  PrefetchAgreementsForStandardApplicationQueryHookResult,
  PrefetchAgreementsForStandardApplicationLazyQueryHookResult,
  PrefetchAgreementsForStandardApplicationQueryResult,
  PrefetchAgreementsForLaunchPadShowroomVisitQueryVariables,
  PrefetchAgreementsForLaunchPadShowroomVisitQuery,
  PrefetchAgreementsForLaunchPadShowroomVisitQueryHookResult,
  PrefetchAgreementsForLaunchPadShowroomVisitLazyQueryHookResult,
  PrefetchAgreementsForLaunchPadShowroomVisitQueryResult,
  PrefetchAgreementsForLaunchPadAppointmentQueryVariables,
  PrefetchAgreementsForLaunchPadAppointmentQuery,
  PrefetchAgreementsForLaunchPadAppointmentQueryHookResult,
  PrefetchAgreementsForLaunchPadAppointmentLazyQueryHookResult,
  PrefetchAgreementsForLaunchPadAppointmentQueryResult,
  PrefetchAgreementsForLaunchPadQueryVariables,
  PrefetchAgreementsForLaunchPadQuery,
  PrefetchAgreementsForLaunchPadQueryHookResult,
  PrefetchAgreementsForLaunchPadLazyQueryHookResult,
  PrefetchAgreementsForLaunchPadQueryResult,
  PrefetchAgreementsForEventQueryVariables,
  PrefetchAgreementsForEventQuery,
  PrefetchAgreementsForEventQueryHookResult,
  PrefetchAgreementsForEventLazyQueryHookResult,
  PrefetchAgreementsForEventQueryResult,
  PrefetchAgreementsForContactQueryVariables,
  PrefetchAgreementsForContactQuery,
  PrefetchAgreementsForContactQueryHookResult,
  PrefetchAgreementsForContactLazyQueryHookResult,
  PrefetchAgreementsForContactQueryResult,
  ListWhatsappLiveChatSettingsQueryVariables,
  ListWhatsappLiveChatSettingsQuery,
  ListWhatsappLiveChatSettingsQueryHookResult,
  ListWhatsappLiveChatSettingsLazyQueryHookResult,
  ListWhatsappLiveChatSettingsQueryResult,
  ListWebPagesQueryVariables,
  ListWebPagesQuery,
  ListWebPagesQueryHookResult,
  ListWebPagesLazyQueryHookResult,
  ListWebPagesQueryResult,
  ListWebCalcSettingsQueryVariables,
  ListWebCalcSettingsQuery,
  ListWebCalcSettingsQueryHookResult,
  ListWebCalcSettingsLazyQueryHookResult,
  ListWebCalcSettingsQueryResult,
  ListVehiclesWithParametersQueryVariables,
  ListVehiclesWithParametersQuery,
  ListVehiclesWithParametersQueryHookResult,
  ListVehiclesWithParametersLazyQueryHookResult,
  ListVehiclesWithParametersQueryResult,
  ListVehiclesQueryVariables,
  ListVehiclesQuery,
  ListVehiclesQueryHookResult,
  ListVehiclesLazyQueryHookResult,
  ListVehiclesQueryResult,
  ListVariantConfiguratorsQueryVariables,
  ListVariantConfiguratorsQuery,
  ListVariantConfiguratorsQueryHookResult,
  ListVariantConfiguratorsLazyQueryHookResult,
  ListVariantConfiguratorsQueryResult,
  ListUsersQueryVariables,
  ListUsersQuery,
  ListUsersQueryHookResult,
  ListUsersLazyQueryHookResult,
  ListUsersQueryResult,
  ListUserlikeChatbotSettingsQueryVariables,
  ListUserlikeChatbotSettingsQuery,
  ListUserlikeChatbotSettingsQueryHookResult,
  ListUserlikeChatbotSettingsLazyQueryHookResult,
  ListUserlikeChatbotSettingsQueryResult,
  ListUserGroupsQueryVariables,
  ListUserGroupsQuery,
  ListUserGroupsQueryHookResult,
  ListUserGroupsLazyQueryHookResult,
  ListUserGroupsQueryResult,
  ListTradeInsQueryVariables,
  ListTradeInsQuery,
  ListTradeInsQueryHookResult,
  ListTradeInsLazyQueryHookResult,
  ListTradeInsQueryResult,
  ListSalesOfferAuditTrailsQueryVariables,
  ListSalesOfferAuditTrailsQuery,
  ListSalesOfferAuditTrailsQueryHookResult,
  ListSalesOfferAuditTrailsLazyQueryHookResult,
  ListSalesOfferAuditTrailsQueryResult,
  ListRoutersQueryVariables,
  ListRoutersQuery,
  ListRoutersQueryHookResult,
  ListRoutersLazyQueryHookResult,
  ListRoutersQueryResult,
  ListRolesQueryVariables,
  ListRolesQuery,
  ListRolesQueryHookResult,
  ListRolesLazyQueryHookResult,
  ListRolesQueryResult,
  ListPorschePaymentSettingsQueryVariables,
  ListPorschePaymentSettingsQuery,
  ListPorschePaymentSettingsQueryHookResult,
  ListPorschePaymentSettingsLazyQueryHookResult,
  ListPorschePaymentSettingsQueryResult,
  ListMyInfoSettingQueryVariables,
  ListMyInfoSettingQuery,
  ListMyInfoSettingQueryHookResult,
  ListMyInfoSettingLazyQueryHookResult,
  ListMyInfoSettingQueryResult,
  ListMyInfoModulesQueryVariables,
  ListMyInfoModulesQuery,
  ListMyInfoModulesQueryHookResult,
  ListMyInfoModulesLazyQueryHookResult,
  ListMyInfoModulesQueryResult,
  ListModulesForApplicationDownloadQueryVariables,
  ListModulesForApplicationDownloadQuery,
  ListModulesForApplicationDownloadQueryHookResult,
  ListModulesForApplicationDownloadLazyQueryHookResult,
  ListModulesForApplicationDownloadQueryResult,
  ListModulesQueryVariables,
  ListModulesQuery,
  ListModulesQueryHookResult,
  ListModulesLazyQueryHookResult,
  ListModulesQueryResult,
  ListModuleTypeQueryVariables,
  ListModuleTypeQuery,
  ListModuleTypeQueryHookResult,
  ListModuleTypeLazyQueryHookResult,
  ListModuleTypeQueryResult,
  ListModelConfiguratorsQueryVariables,
  ListModelConfiguratorsQuery,
  ListModelConfiguratorsQueryHookResult,
  ListModelConfiguratorsLazyQueryHookResult,
  ListModelConfiguratorsQueryResult,
  ListMobilityStocksQueryVariables,
  ListMobilityStocksQuery,
  ListMobilityStocksQueryHookResult,
  ListMobilityStocksLazyQueryHookResult,
  ListMobilityStocksQueryResult,
  ListMobilityHomeDeliveriesQueryVariables,
  ListMobilityHomeDeliveriesQuery,
  ListMobilityHomeDeliveriesQueryHookResult,
  ListMobilityHomeDeliveriesLazyQueryHookResult,
  ListMobilityHomeDeliveriesQueryResult,
  ListMobilitiesQueryVariables,
  ListMobilitiesQuery,
  ListMobilitiesQueryHookResult,
  ListMobilitiesLazyQueryHookResult,
  ListMobilitiesQueryResult,
  ListLocationsQueryVariables,
  ListLocationsQuery,
  ListLocationsQueryHookResult,
  ListLocationsLazyQueryHookResult,
  ListLocationsQueryResult,
  ListLocationConditionsQueryVariables,
  ListLocationConditionsQuery,
  ListLocationConditionsQueryHookResult,
  ListLocationConditionsLazyQueryHookResult,
  ListLocationConditionsQueryResult,
  ListLocalVariantsForSelectionQueryVariables,
  ListLocalVariantsForSelectionQuery,
  ListLocalVariantsForSelectionQueryHookResult,
  ListLocalVariantsForSelectionLazyQueryHookResult,
  ListLocalVariantsForSelectionQueryResult,
  ListLocalVariantsForCalculatorQueryVariables,
  ListLocalVariantsForCalculatorQuery,
  ListLocalVariantsForCalculatorQueryHookResult,
  ListLocalVariantsForCalculatorLazyQueryHookResult,
  ListLocalVariantsForCalculatorQueryResult,
  ListLocalVariantsQueryVariables,
  ListLocalVariantsQuery,
  ListLocalVariantsQueryHookResult,
  ListLocalVariantsLazyQueryHookResult,
  ListLocalVariantsQueryResult,
  ListLocalModelsQueryVariables,
  ListLocalModelsQuery,
  ListLocalModelsQueryHookResult,
  ListLocalModelsLazyQueryHookResult,
  ListLocalModelsQueryResult,
  ListLocalMakesQueryVariables,
  ListLocalMakesQuery,
  ListLocalMakesQueryHookResult,
  ListLocalMakesLazyQueryHookResult,
  ListLocalMakesQueryResult,
  ListLeadsQueryVariables,
  ListLeadsQuery,
  ListLeadsQueryHookResult,
  ListLeadsLazyQueryHookResult,
  ListLeadsQueryResult,
  ListLeadAuditTrailsQueryVariables,
  ListLeadAuditTrailsQuery,
  ListLeadAuditTrailsQueryHookResult,
  ListLeadAuditTrailsLazyQueryHookResult,
  ListLeadAuditTrailsQueryResult,
  ListLanguagePacksQueryVariables,
  ListLanguagePacksQuery,
  ListLanguagePacksQueryHookResult,
  ListLanguagePacksLazyQueryHookResult,
  ListLanguagePacksQueryResult,
  ListLabelsQueryVariables,
  ListLabelsQuery,
  ListLabelsQueryHookResult,
  ListLabelsLazyQueryHookResult,
  ListLabelsQueryResult,
  ListInventoriesFilterQueryVariables,
  ListInventoriesFilterQuery,
  ListInventoriesFilterQueryHookResult,
  ListInventoriesFilterLazyQueryHookResult,
  ListInventoriesFilterQueryResult,
  ListInventoriesQueryVariables,
  ListInventoriesQuery,
  ListInventoriesQueryHookResult,
  ListInventoriesLazyQueryHookResult,
  ListInventoriesQueryResult,
  ListInsurersQueryVariables,
  ListInsurersQuery,
  ListInsurersQueryHookResult,
  ListInsurersLazyQueryHookResult,
  ListInsurersQueryResult,
  ListInsuranceProductsForProductionQueryVariables,
  ListInsuranceProductsForProductionQuery,
  ListInsuranceProductsForProductionQueryHookResult,
  ListInsuranceProductsForProductionLazyQueryHookResult,
  ListInsuranceProductsForProductionQueryResult,
  ListInsuranceProductsQueryVariables,
  ListInsuranceProductsQuery,
  ListInsuranceProductsQueryHookResult,
  ListInsuranceProductsLazyQueryHookResult,
  ListInsuranceProductsQueryResult,
  ListGiftVouchersQueryVariables,
  ListGiftVouchersQuery,
  ListGiftVouchersQueryHookResult,
  ListGiftVouchersLazyQueryHookResult,
  ListGiftVouchersQueryResult,
  ListFiservPaymentSettingsQueryVariables,
  ListFiservPaymentSettingsQuery,
  ListFiservPaymentSettingsQueryHookResult,
  ListFiservPaymentSettingsLazyQueryHookResult,
  ListFiservPaymentSettingsQueryResult,
  ListFinderVehiclesForSelectionQueryVariables,
  ListFinderVehiclesForSelectionQuery,
  ListFinderVehiclesForSelectionQueryHookResult,
  ListFinderVehiclesForSelectionLazyQueryHookResult,
  ListFinderVehiclesForSelectionQueryResult,
  ListFinderVehiclesForCalculatorQueryVariables,
  ListFinderVehiclesForCalculatorQuery,
  ListFinderVehiclesForCalculatorQueryHookResult,
  ListFinderVehiclesForCalculatorLazyQueryHookResult,
  ListFinderVehiclesForCalculatorQueryResult,
  ListFinderVehiclesQueryVariables,
  ListFinderVehiclesQuery,
  ListFinderVehiclesQueryHookResult,
  ListFinderVehiclesLazyQueryHookResult,
  ListFinderVehiclesQueryResult,
  ListFinanceProductsForProductionQueryVariables,
  ListFinanceProductsForProductionQuery,
  ListFinanceProductsForProductionQueryHookResult,
  ListFinanceProductsForProductionLazyQueryHookResult,
  ListFinanceProductsForProductionQueryResult,
  ListFinanceProductsQueryVariables,
  ListFinanceProductsQuery,
  ListFinanceProductsQueryHookResult,
  ListFinanceProductsLazyQueryHookResult,
  ListFinanceProductsQueryResult,
  ListEventsQueryVariables,
  ListEventsQuery,
  ListEventsQueryHookResult,
  ListEventsLazyQueryHookResult,
  ListEventsQueryResult,
  ListEventFiltersQueryVariables,
  ListEventFiltersQuery,
  ListEventFiltersQueryHookResult,
  ListEventFiltersLazyQueryHookResult,
  ListEventFiltersQueryResult,
  ListDocusignSettingsQueryVariables,
  ListDocusignSettingsQuery,
  ListDocusignSettingsQueryHookResult,
  ListDocusignSettingsLazyQueryHookResult,
  ListDocusignSettingsQueryResult,
  ListDealersQueryVariables,
  ListDealersQuery,
  ListDealersQueryHookResult,
  ListDealersLazyQueryHookResult,
  ListDealersQueryResult,
  ListCustomersQueryVariables,
  ListCustomersQuery,
  ListCustomersQueryHookResult,
  ListCustomersLazyQueryHookResult,
  ListCustomersQueryResult,
  ListConsentsAndDeclarationsQueryVariables,
  ListConsentsAndDeclarationsQuery,
  ListConsentsAndDeclarationsQueryHookResult,
  ListConsentsAndDeclarationsLazyQueryHookResult,
  ListConsentsAndDeclarationsQueryResult,
  ListCompaniesForSelectionQueryVariables,
  ListCompaniesForSelectionQuery,
  ListCompaniesForSelectionQueryHookResult,
  ListCompaniesForSelectionLazyQueryHookResult,
  ListCompaniesForSelectionQueryResult,
  ListCompaniesForModuleQueryVariables,
  ListCompaniesForModuleQuery,
  ListCompaniesForModuleQueryHookResult,
  ListCompaniesForModuleLazyQueryHookResult,
  ListCompaniesForModuleQueryResult,
  ListCompaniesContextQueryVariables,
  ListCompaniesContextQuery,
  ListCompaniesContextQueryHookResult,
  ListCompaniesContextLazyQueryHookResult,
  ListCompaniesContextQueryResult,
  ListCompaniesQueryVariables,
  ListCompaniesQuery,
  ListCompaniesQueryHookResult,
  ListCompaniesLazyQueryHookResult,
  ListCompaniesQueryResult,
  ListCampaignsQueryVariables,
  ListCampaignsQuery,
  ListCampaignsQueryHookResult,
  ListCampaignsLazyQueryHookResult,
  ListCampaignsQueryResult,
  ListBannersQueryVariables,
  ListBannersQuery,
  ListBannersQueryHookResult,
  ListBannersLazyQueryHookResult,
  ListBannersQueryResult,
  ListBanksQueryVariables,
  ListBanksQuery,
  ListBanksQueryHookResult,
  ListBanksLazyQueryHookResult,
  ListBanksQueryResult,
  ListBankOptionsQueryVariables,
  ListBankOptionsQuery,
  ListBankOptionsQueryHookResult,
  ListBankOptionsLazyQueryHookResult,
  ListBankOptionsQueryResult,
  ListAvailableUsersForUserGroupQueryVariables,
  ListAvailableUsersForUserGroupQuery,
  ListAvailableUsersForUserGroupQueryHookResult,
  ListAvailableUsersForUserGroupLazyQueryHookResult,
  ListAvailableUsersForUserGroupQueryResult,
  ListAvailableUsersByRoleQueryVariables,
  ListAvailableUsersByRoleQuery,
  ListAvailableUsersByRoleQueryHookResult,
  ListAvailableUsersByRoleLazyQueryHookResult,
  ListAvailableUsersByRoleQueryResult,
  ListAvailableUserGroupsQueryVariables,
  ListAvailableUserGroupsQuery,
  ListAvailableUserGroupsQueryHookResult,
  ListAvailableUserGroupsLazyQueryHookResult,
  ListAvailableUserGroupsQueryResult,
  ListAvailablePermissionsQueryVariables,
  ListAvailablePermissionsQuery,
  ListAvailablePermissionsQueryHookResult,
  ListAvailablePermissionsLazyQueryHookResult,
  ListAvailablePermissionsQueryResult,
  ListApplicationsStatusQueryVariables,
  ListApplicationsStatusQuery,
  ListApplicationsStatusQueryHookResult,
  ListApplicationsStatusLazyQueryHookResult,
  ListApplicationsStatusQueryResult,
  ListApplicationsModulesQueryVariables,
  ListApplicationsModulesQuery,
  ListApplicationsModulesQueryHookResult,
  ListApplicationsModulesLazyQueryHookResult,
  ListApplicationsModulesQueryResult,
  ListApplicationsQueryVariables,
  ListApplicationsQuery,
  ListApplicationsQueryHookResult,
  ListApplicationsLazyQueryHookResult,
  ListApplicationsQueryResult,
  ListAdyenPaymentSettingsQueryVariables,
  ListAdyenPaymentSettingsQuery,
  ListAdyenPaymentSettingsQueryHookResult,
  ListAdyenPaymentSettingsLazyQueryHookResult,
  ListAdyenPaymentSettingsQueryResult,
  HasValidPromoCodeQueryVariables,
  HasValidPromoCodeQuery,
  HasValidPromoCodeQueryHookResult,
  HasValidPromoCodeLazyQueryHookResult,
  HasValidPromoCodeQueryResult,
  GetWebPageOptionsQueryVariables,
  GetWebPageOptionsQuery,
  GetWebPageOptionsQueryHookResult,
  GetWebPageOptionsLazyQueryHookResult,
  GetWebPageOptionsQueryResult,
  GetWebPageQueryVariables,
  GetWebPageQuery,
  GetWebPageQueryHookResult,
  GetWebPageLazyQueryHookResult,
  GetWebPageQueryResult,
  GetVehicleOptionsQueryVariables,
  GetVehicleOptionsQuery,
  GetVehicleOptionsQueryHookResult,
  GetVehicleOptionsLazyQueryHookResult,
  GetVehicleOptionsQueryResult,
  GetVehicleBySuiteIdQueryVariables,
  GetVehicleBySuiteIdQuery,
  GetVehicleBySuiteIdQueryHookResult,
  GetVehicleBySuiteIdLazyQueryHookResult,
  GetVehicleBySuiteIdQueryResult,
  GetVehicleQueryVariables,
  GetVehicleQuery,
  GetVehicleQueryHookResult,
  GetVehicleLazyQueryHookResult,
  GetVehicleQueryResult,
  GetVariantsOfAllConfiguratorsQueryVariables,
  GetVariantsOfAllConfiguratorsQuery,
  GetVariantsOfAllConfiguratorsQueryHookResult,
  GetVariantsOfAllConfiguratorsLazyQueryHookResult,
  GetVariantsOfAllConfiguratorsQueryResult,
  GetVariantConfiguratorQueryVariables,
  GetVariantConfiguratorQuery,
  GetVariantConfiguratorQueryHookResult,
  GetVariantConfiguratorLazyQueryHookResult,
  GetVariantConfiguratorQueryResult,
  GetValidPromoCodeByCodeQueryVariables,
  GetValidPromoCodeByCodeQuery,
  GetValidPromoCodeByCodeQueryHookResult,
  GetValidPromoCodeByCodeLazyQueryHookResult,
  GetValidPromoCodeByCodeQueryResult,
  GetValidDiscountCodeQueryVariables,
  GetValidDiscountCodeQuery,
  GetValidDiscountCodeQueryHookResult,
  GetValidDiscountCodeLazyQueryHookResult,
  GetValidDiscountCodeQueryResult,
  GetUsersOptionsForUserGroupQueryVariables,
  GetUsersOptionsForUserGroupQuery,
  GetUsersOptionsForUserGroupQueryHookResult,
  GetUsersOptionsForUserGroupLazyQueryHookResult,
  GetUsersOptionsForUserGroupQueryResult,
  GetUsersOptionsQueryVariables,
  GetUsersOptionsQuery,
  GetUsersOptionsQueryHookResult,
  GetUsersOptionsLazyQueryHookResult,
  GetUsersOptionsQueryResult,
  GetUserGroupOptionsQueryVariables,
  GetUserGroupOptionsQuery,
  GetUserGroupOptionsQueryHookResult,
  GetUserGroupOptionsLazyQueryHookResult,
  GetUserGroupOptionsQueryResult,
  GetUserGroupQueryVariables,
  GetUserGroupQuery,
  GetUserGroupQueryHookResult,
  GetUserGroupLazyQueryHookResult,
  GetUserGroupQueryResult,
  GetUserQueryVariables,
  GetUserQuery,
  GetUserQueryHookResult,
  GetUserLazyQueryHookResult,
  GetUserQueryResult,
  GetTradeInListFiltersQueryVariables,
  GetTradeInListFiltersQuery,
  GetTradeInListFiltersQueryHookResult,
  GetTradeInListFiltersLazyQueryHookResult,
  GetTradeInListFiltersQueryResult,
  GetTradeInAuditTrailsQueryVariables,
  GetTradeInAuditTrailsQuery,
  GetTradeInAuditTrailsQueryHookResult,
  GetTradeInAuditTrailsLazyQueryHookResult,
  GetTradeInAuditTrailsQueryResult,
  GetTradeInQueryVariables,
  GetTradeInQuery,
  GetTradeInQueryHookResult,
  GetTradeInLazyQueryHookResult,
  GetTradeInQueryResult,
  GetTopSalesPersonsQueryVariables,
  GetTopSalesPersonsQuery,
  GetTopSalesPersonsQueryHookResult,
  GetTopSalesPersonsLazyQueryHookResult,
  GetTopSalesPersonsQueryResult,
  GetStockInventoryByVariantConfiguratorQueryVariables,
  GetStockInventoryByVariantConfiguratorQuery,
  GetStockInventoryByVariantConfiguratorQueryHookResult,
  GetStockInventoryByVariantConfiguratorLazyQueryHookResult,
  GetStockInventoryByVariantConfiguratorQueryResult,
  GetStockAuditTrailsQueryVariables,
  GetStockAuditTrailsQuery,
  GetStockAuditTrailsQueryHookResult,
  GetStockAuditTrailsLazyQueryHookResult,
  GetStockAuditTrailsQueryResult,
  GetSettingOptionsForPorschePaymentModuleQueryVariables,
  GetSettingOptionsForPorschePaymentModuleQuery,
  GetSettingOptionsForPorschePaymentModuleQueryHookResult,
  GetSettingOptionsForPorschePaymentModuleLazyQueryHookResult,
  GetSettingOptionsForPorschePaymentModuleQueryResult,
  GetSettingOptionsForFiservPaymentModuleQueryVariables,
  GetSettingOptionsForFiservPaymentModuleQuery,
  GetSettingOptionsForFiservPaymentModuleQueryHookResult,
  GetSettingOptionsForFiservPaymentModuleLazyQueryHookResult,
  GetSettingOptionsForFiservPaymentModuleQueryResult,
  GetSettingOptionsForAdyenPaymentModuleQueryVariables,
  GetSettingOptionsForAdyenPaymentModuleQuery,
  GetSettingOptionsForAdyenPaymentModuleQueryHookResult,
  GetSettingOptionsForAdyenPaymentModuleLazyQueryHookResult,
  GetSettingOptionsForAdyenPaymentModuleQueryResult,
  GetSalesOfferJourneyQueryVariables,
  GetSalesOfferJourneyQuery,
  GetSalesOfferJourneyQueryHookResult,
  GetSalesOfferJourneyLazyQueryHookResult,
  GetSalesOfferJourneyQueryResult,
  GetSalesOfferQueryVariables,
  GetSalesOfferQuery,
  GetSalesOfferQueryHookResult,
  GetSalesOfferLazyQueryHookResult,
  GetSalesOfferQueryResult,
  GetSalesControlBoardFilterOptionsQueryVariables,
  GetSalesControlBoardFilterOptionsQuery,
  GetSalesControlBoardFilterOptionsQueryHookResult,
  GetSalesControlBoardFilterOptionsLazyQueryHookResult,
  GetSalesControlBoardFilterOptionsQueryResult,
  GetSalesControlBoardQueryVariables,
  GetSalesControlBoardQuery,
  GetSalesControlBoardQueryHookResult,
  GetSalesControlBoardLazyQueryHookResult,
  GetSalesControlBoardQueryResult,
  GetRouterSpecsQueryVariables,
  GetRouterSpecsQuery,
  GetRouterSpecsQueryHookResult,
  GetRouterSpecsLazyQueryHookResult,
  GetRouterSpecsQueryResult,
  GetRouterEndpointListQueryVariables,
  GetRouterEndpointListQuery,
  GetRouterEndpointListQueryHookResult,
  GetRouterEndpointListLazyQueryHookResult,
  GetRouterEndpointListQueryResult,
  GetRouterContextQueryVariables,
  GetRouterContextQuery,
  GetRouterContextQueryHookResult,
  GetRouterContextLazyQueryHookResult,
  GetRouterContextQueryResult,
  GetRoleQueryVariables,
  GetRoleQuery,
  GetRoleQueryHookResult,
  GetRoleLazyQueryHookResult,
  GetRoleQueryResult,
  GetReferenceApplicationListEndpointsQueryVariables,
  GetReferenceApplicationListEndpointsQuery,
  GetReferenceApplicationListEndpointsQueryHookResult,
  GetReferenceApplicationListEndpointsLazyQueryHookResult,
  GetReferenceApplicationListEndpointsQueryResult,
  GetPublicStockQueryVariables,
  GetPublicStockQuery,
  GetPublicStockQueryHookResult,
  GetPublicStockLazyQueryHookResult,
  GetPublicStockQueryResult,
  GetPublicAssigneesQueryVariables,
  GetPublicAssigneesQuery,
  GetPublicAssigneesQueryHookResult,
  GetPublicAssigneesLazyQueryHookResult,
  GetPublicAssigneesQueryResult,
  GetPromoCodeModuleOptionsQueryVariables,
  GetPromoCodeModuleOptionsQuery,
  GetPromoCodeModuleOptionsQueryHookResult,
  GetPromoCodeModuleOptionsLazyQueryHookResult,
  GetPromoCodeModuleOptionsQueryResult,
  GetPromoCodeListingQueryVariables,
  GetPromoCodeListingQuery,
  GetPromoCodeListingQueryHookResult,
  GetPromoCodeListingLazyQueryHookResult,
  GetPromoCodeListingQueryResult,
  GetPromoCodeQueryVariables,
  GetPromoCodeQuery,
  GetPromoCodeQueryHookResult,
  GetPromoCodeLazyQueryHookResult,
  GetPromoCodeQueryResult,
  GetPorscheVehicleDataQueryVariables,
  GetPorscheVehicleDataQuery,
  GetPorscheVehicleDataQueryHookResult,
  GetPorscheVehicleDataLazyQueryHookResult,
  GetPorscheVehicleDataQueryResult,
  GetPorschePaymentMethodsForSalesOfferQueryVariables,
  GetPorschePaymentMethodsForSalesOfferQuery,
  GetPorschePaymentMethodsForSalesOfferQueryHookResult,
  GetPorschePaymentMethodsForSalesOfferLazyQueryHookResult,
  GetPorschePaymentMethodsForSalesOfferQueryResult,
  GetPorschePaymentMethodsQueryVariables,
  GetPorschePaymentMethodsQuery,
  GetPorschePaymentMethodsQueryHookResult,
  GetPorschePaymentMethodsLazyQueryHookResult,
  GetPorschePaymentMethodsQueryResult,
  GetPorscheIdAuthorizeUrlQueryVariables,
  GetPorscheIdAuthorizeUrlQuery,
  GetPorscheIdAuthorizeUrlQueryHookResult,
  GetPorscheIdAuthorizeUrlLazyQueryHookResult,
  GetPorscheIdAuthorizeUrlQueryResult,
  GetPopularVariantApplicationQueryVariables,
  GetPopularVariantApplicationQuery,
  GetPopularVariantApplicationQueryHookResult,
  GetPopularVariantApplicationLazyQueryHookResult,
  GetPopularVariantApplicationQueryResult,
  GetPopularEventApplicationQueryVariables,
  GetPopularEventApplicationQuery,
  GetPopularEventApplicationQueryHookResult,
  GetPopularEventApplicationLazyQueryHookResult,
  GetPopularEventApplicationQueryResult,
  GetNearbyDealersQueryVariables,
  GetNearbyDealersQuery,
  GetNearbyDealersQueryHookResult,
  GetNearbyDealersLazyQueryHookResult,
  GetNearbyDealersQueryResult,
  GetMyInfoAuthorizeUrlQueryVariables,
  GetMyInfoAuthorizeUrlQuery,
  GetMyInfoAuthorizeUrlQueryHookResult,
  GetMyInfoAuthorizeUrlLazyQueryHookResult,
  GetMyInfoAuthorizeUrlQueryResult,
  GetMonthOfImportOptionsQueryVariables,
  GetMonthOfImportOptionsQuery,
  GetMonthOfImportOptionsQueryHookResult,
  GetMonthOfImportOptionsLazyQueryHookResult,
  GetMonthOfImportOptionsQueryResult,
  GetModulesWithScenariosQueryVariables,
  GetModulesWithScenariosQuery,
  GetModulesWithScenariosQueryHookResult,
  GetModulesWithScenariosLazyQueryHookResult,
  GetModulesWithScenariosQueryResult,
  GetModulesOptionsQueryVariables,
  GetModulesOptionsQuery,
  GetModulesOptionsQueryHookResult,
  GetModulesOptionsLazyQueryHookResult,
  GetModulesOptionsQueryResult,
  GetModulesListQueryVariables,
  GetModulesListQuery,
  GetModulesListQueryHookResult,
  GetModulesListLazyQueryHookResult,
  GetModulesListQueryResult,
  GetModuleWithPermissionsSpecsQueryVariables,
  GetModuleWithPermissionsSpecsQuery,
  GetModuleWithPermissionsSpecsQueryHookResult,
  GetModuleWithPermissionsSpecsLazyQueryHookResult,
  GetModuleWithPermissionsSpecsQueryResult,
  GetModuleSpecsQueryVariables,
  GetModuleSpecsQuery,
  GetModuleSpecsQueryHookResult,
  GetModuleSpecsLazyQueryHookResult,
  GetModuleSpecsQueryResult,
  GetModelConfiguratorForApplicationQueryVariables,
  GetModelConfiguratorForApplicationQuery,
  GetModelConfiguratorForApplicationQueryHookResult,
  GetModelConfiguratorForApplicationLazyQueryHookResult,
  GetModelConfiguratorForApplicationQueryResult,
  GetModelConfiguratorFiltersQueryVariables,
  GetModelConfiguratorFiltersQuery,
  GetModelConfiguratorFiltersQueryHookResult,
  GetModelConfiguratorFiltersLazyQueryHookResult,
  GetModelConfiguratorFiltersQueryResult,
  GetModelConfiguratorQueryVariables,
  GetModelConfiguratorQuery,
  GetModelConfiguratorQueryHookResult,
  GetModelConfiguratorLazyQueryHookResult,
  GetModelConfiguratorQueryResult,
  GetMobilityVehicleFiltersQueryVariables,
  GetMobilityVehicleFiltersQuery,
  GetMobilityVehicleFiltersQueryHookResult,
  GetMobilityVehicleFiltersLazyQueryHookResult,
  GetMobilityVehicleFiltersQueryResult,
  GetMobilityLocationQueryVariables,
  GetMobilityLocationQuery,
  GetMobilityLocationQueryHookResult,
  GetMobilityLocationLazyQueryHookResult,
  GetMobilityLocationQueryResult,
  GetMobilityHomeDeliveryQueryVariables,
  GetMobilityHomeDeliveryQuery,
  GetMobilityHomeDeliveryQueryHookResult,
  GetMobilityHomeDeliveryLazyQueryHookResult,
  GetMobilityHomeDeliveryQueryResult,
  GetMobilityQueryVariables,
  GetMobilityQuery,
  GetMobilityQueryHookResult,
  GetMobilityLazyQueryHookResult,
  GetMobilityQueryResult,
  GetMarketingDashboardFilterOptionQueryVariables,
  GetMarketingDashboardFilterOptionQuery,
  GetMarketingDashboardFilterOptionQueryHookResult,
  GetMarketingDashboardFilterOptionLazyQueryHookResult,
  GetMarketingDashboardFilterOptionQueryResult,
  GetMarketingDashboardQueryVariables,
  GetMarketingDashboardQuery,
  GetMarketingDashboardQueryHookResult,
  GetMarketingDashboardLazyQueryHookResult,
  GetMarketingDashboardQueryResult,
  GetLowestMonthlyInstalmentQueryVariables,
  GetLowestMonthlyInstalmentQuery,
  GetLowestMonthlyInstalmentQueryHookResult,
  GetLowestMonthlyInstalmentLazyQueryHookResult,
  GetLowestMonthlyInstalmentQueryResult,
  GetLocalVariantsOptionsQueryVariables,
  GetLocalVariantsOptionsQuery,
  GetLocalVariantsOptionsQueryHookResult,
  GetLocalVariantsOptionsLazyQueryHookResult,
  GetLocalVariantsOptionsQueryResult,
  GetLeadsListFiltersQueryVariables,
  GetLeadsListFiltersQuery,
  GetLeadsListFiltersQueryHookResult,
  GetLeadsListFiltersLazyQueryHookResult,
  GetLeadsListFiltersQueryResult,
  GetLeadDocumentDownloadLinkQueryVariables,
  GetLeadDocumentDownloadLinkQuery,
  GetLeadDocumentDownloadLinkQueryHookResult,
  GetLeadDocumentDownloadLinkLazyQueryHookResult,
  GetLeadDocumentDownloadLinkQueryResult,
  GetLeadQueryVariables,
  GetLeadQuery,
  GetLeadQueryHookResult,
  GetLeadLazyQueryHookResult,
  GetLeadQueryResult,
  GetLanguagePackOptionsQueryVariables,
  GetLanguagePackOptionsQuery,
  GetLanguagePackOptionsQueryHookResult,
  GetLanguagePackOptionsLazyQueryHookResult,
  GetLanguagePackOptionsQueryResult,
  GetLanguagePackListFiltersQueryVariables,
  GetLanguagePackListFiltersQuery,
  GetLanguagePackListFiltersQueryHookResult,
  GetLanguagePackListFiltersLazyQueryHookResult,
  GetLanguagePackListFiltersQueryResult,
  GetLanguagePackQueryVariables,
  GetLanguagePackQuery,
  GetLanguagePackQueryHookResult,
  GetLanguagePackLazyQueryHookResult,
  GetLanguagePackQueryResult,
  GetLabelsByFeatureModuleIdQueryVariables,
  GetLabelsByFeatureModuleIdQuery,
  GetLabelsByFeatureModuleIdQueryHookResult,
  GetLabelsByFeatureModuleIdLazyQueryHookResult,
  GetLabelsByFeatureModuleIdQueryResult,
  GetLabelQueryVariables,
  GetLabelQuery,
  GetLabelQueryHookResult,
  GetLabelLazyQueryHookResult,
  GetLabelQueryResult,
  GetJourneyEventQueryVariables,
  GetJourneyEventQuery,
  GetJourneyEventQueryHookResult,
  GetJourneyEventLazyQueryHookResult,
  GetJourneyEventQueryResult,
  GetInventoryOptionsQueryVariables,
  GetInventoryOptionsQuery,
  GetInventoryOptionsQueryHookResult,
  GetInventoryOptionsLazyQueryHookResult,
  GetInventoryOptionsQueryResult,
  GetInventoryAuditTrailsQueryVariables,
  GetInventoryAuditTrailsQuery,
  GetInventoryAuditTrailsQueryHookResult,
  GetInventoryAuditTrailsLazyQueryHookResult,
  GetInventoryAuditTrailsQueryResult,
  GetInventoryQueryVariables,
  GetInventoryQuery,
  GetInventoryQueryHookResult,
  GetInventoryLazyQueryHookResult,
  GetInventoryQueryResult,
  GetInventoriesByMobilityModuleIdQueryVariables,
  GetInventoriesByMobilityModuleIdQuery,
  GetInventoriesByMobilityModuleIdQueryHookResult,
  GetInventoriesByMobilityModuleIdLazyQueryHookResult,
  GetInventoriesByMobilityModuleIdQueryResult,
  GetInsurersByModuleIdQueryVariables,
  GetInsurersByModuleIdQuery,
  GetInsurersByModuleIdQueryHookResult,
  GetInsurersByModuleIdLazyQueryHookResult,
  GetInsurersByModuleIdQueryResult,
  GetInsurerQueryVariables,
  GetInsurerQuery,
  GetInsurerQueryHookResult,
  GetInsurerLazyQueryHookResult,
  GetInsurerQueryResult,
  GetInsuranceProductsQueryVariables,
  GetInsuranceProductsQuery,
  GetInsuranceProductsQueryHookResult,
  GetInsuranceProductsLazyQueryHookResult,
  GetInsuranceProductsQueryResult,
  GetInsuranceProductQueryVariables,
  GetInsuranceProductQuery,
  GetInsuranceProductQueryHookResult,
  GetInsuranceProductLazyQueryHookResult,
  GetInsuranceProductQueryResult,
  GetInsurancePremiumQueryVariables,
  GetInsurancePremiumQuery,
  GetInsurancePremiumQueryHookResult,
  GetInsurancePremiumLazyQueryHookResult,
  GetInsurancePremiumQueryResult,
  GetImportedLocationFromExcelQueryVariables,
  GetImportedLocationFromExcelQuery,
  GetImportedLocationFromExcelQueryHookResult,
  GetImportedLocationFromExcelLazyQueryHookResult,
  GetImportedLocationFromExcelQueryResult,
  GetImportedFpTableFromExcelQueryVariables,
  GetImportedFpTableFromExcelQuery,
  GetImportedFpTableFromExcelQueryHookResult,
  GetImportedFpTableFromExcelLazyQueryHookResult,
  GetImportedFpTableFromExcelQueryResult,
  GetGiftVouchersListFiltersQueryVariables,
  GetGiftVouchersListFiltersQuery,
  GetGiftVouchersListFiltersQueryHookResult,
  GetGiftVouchersListFiltersLazyQueryHookResult,
  GetGiftVouchersListFiltersQueryResult,
  GetGiftVoucherJourneyQueryVariables,
  GetGiftVoucherJourneyQuery,
  GetGiftVoucherJourneyQueryHookResult,
  GetGiftVoucherJourneyLazyQueryHookResult,
  GetGiftVoucherJourneyQueryResult,
  GetGiftVoucherQueryVariables,
  GetGiftVoucherQuery,
  GetGiftVoucherQueryHookResult,
  GetGiftVoucherLazyQueryHookResult,
  GetGiftVoucherQueryResult,
  GetFirstEventRouterPathQueryVariables,
  GetFirstEventRouterPathQuery,
  GetFirstEventRouterPathQueryHookResult,
  GetFirstEventRouterPathLazyQueryHookResult,
  GetFirstEventRouterPathQueryResult,
  GetFinderVehicleSyncStatusQueryVariables,
  GetFinderVehicleSyncStatusQuery,
  GetFinderVehicleSyncStatusQueryHookResult,
  GetFinderVehicleSyncStatusLazyQueryHookResult,
  GetFinderVehicleSyncStatusQueryResult,
  GetFinderVehicleOptionsQueryVariables,
  GetFinderVehicleOptionsQuery,
  GetFinderVehicleOptionsQueryHookResult,
  GetFinderVehicleOptionsLazyQueryHookResult,
  GetFinderVehicleOptionsQueryResult,
  GetFinderVehicleQueryVariables,
  GetFinderVehicleQuery,
  GetFinderVehicleQueryHookResult,
  GetFinderVehicleLazyQueryHookResult,
  GetFinderVehicleQueryResult,
  GetFinanceProductsQueryVariables,
  GetFinanceProductsQuery,
  GetFinanceProductsQueryHookResult,
  GetFinanceProductsLazyQueryHookResult,
  GetFinanceProductsQueryResult,
  GetFinanceProductQueryVariables,
  GetFinanceProductQuery,
  GetFinanceProductQueryHookResult,
  GetFinanceProductLazyQueryHookResult,
  GetFinanceProductQueryResult,
  GetEventApplicationsQueryVariables,
  GetEventApplicationsQuery,
  GetEventApplicationsQueryHookResult,
  GetEventApplicationsLazyQueryHookResult,
  GetEventApplicationsQueryResult,
  GetEventQueryVariables,
  GetEventQuery,
  GetEventQueryHookResult,
  GetEventLazyQueryHookResult,
  GetEventQueryResult,
  GetDepositPaymentPerDayQueryVariables,
  GetDepositPaymentPerDayQuery,
  GetDepositPaymentPerDayQueryHookResult,
  GetDepositPaymentPerDayLazyQueryHookResult,
  GetDepositPaymentPerDayQueryResult,
  GetDealersOptionsQueryVariables,
  GetDealersOptionsQuery,
  GetDealersOptionsQueryHookResult,
  GetDealersOptionsLazyQueryHookResult,
  GetDealersOptionsQueryResult,
  GetDealerQueryVariables,
  GetDealerQuery,
  GetDealerQueryHookResult,
  GetDealerLazyQueryHookResult,
  GetDealerQueryResult,
  GetDbsInstantApprovalRateQueryVariables,
  GetDbsInstantApprovalRateQuery,
  GetDbsInstantApprovalRateQueryHookResult,
  GetDbsInstantApprovalRateLazyQueryHookResult,
  GetDbsInstantApprovalRateQueryResult,
  GetDataFromPorscheIdQueryVariables,
  GetDataFromPorscheIdQuery,
  GetDataFromPorscheIdQueryHookResult,
  GetDataFromPorscheIdLazyQueryHookResult,
  GetDataFromPorscheIdQueryResult,
  GetDataFromMyInfoQueryVariables,
  GetDataFromMyInfoQuery,
  GetDataFromMyInfoQueryHookResult,
  GetDataFromMyInfoLazyQueryHookResult,
  GetDataFromMyInfoQueryResult,
  GetCustomerListFiltersQueryVariables,
  GetCustomerListFiltersQuery,
  GetCustomerListFiltersQueryHookResult,
  GetCustomerListFiltersLazyQueryHookResult,
  GetCustomerListFiltersQueryResult,
  GetCustomerFieldsFromFilesQueryVariables,
  GetCustomerFieldsFromFilesQuery,
  GetCustomerFieldsFromFilesQueryHookResult,
  GetCustomerFieldsFromFilesLazyQueryHookResult,
  GetCustomerFieldsFromFilesQueryResult,
  GetCustomerAuditTrailsQueryVariables,
  GetCustomerAuditTrailsQuery,
  GetCustomerAuditTrailsQueryHookResult,
  GetCustomerAuditTrailsLazyQueryHookResult,
  GetCustomerAuditTrailsQueryResult,
  GetCustomerQueryVariables,
  GetCustomerQuery,
  GetCustomerQueryHookResult,
  GetCustomerLazyQueryHookResult,
  GetCustomerQueryResult,
  GetContactsFromBpQueryVariables,
  GetContactsFromBpQuery,
  GetContactsFromBpQueryHookResult,
  GetContactsFromBpLazyQueryHookResult,
  GetContactsFromBpQueryResult,
  GetConsentsAndDeclarationsQueryVariables,
  GetConsentsAndDeclarationsQuery,
  GetConsentsAndDeclarationsQueryHookResult,
  GetConsentsAndDeclarationsLazyQueryHookResult,
  GetConsentsAndDeclarationsQueryResult,
  GetConditionDetailQueryVariables,
  GetConditionDetailQuery,
  GetConditionDetailQueryHookResult,
  GetConditionDetailLazyQueryHookResult,
  GetConditionDetailQueryResult,
  GetCompanySpecsForNewModuleQueryVariables,
  GetCompanySpecsForNewModuleQuery,
  GetCompanySpecsForNewModuleQueryHookResult,
  GetCompanySpecsForNewModuleLazyQueryHookResult,
  GetCompanySpecsForNewModuleQueryResult,
  GetCompanyContextQueryVariables,
  GetCompanyContextQuery,
  GetCompanyContextQueryHookResult,
  GetCompanyContextLazyQueryHookResult,
  GetCompanyContextQueryResult,
  GetCompanyQueryVariables,
  GetCompanyQuery,
  GetCompanyQueryHookResult,
  GetCompanyLazyQueryHookResult,
  GetCompanyQueryResult,
  GetCapVehicleModelsQueryVariables,
  GetCapVehicleModelsQuery,
  GetCapVehicleModelsQueryHookResult,
  GetCapVehicleModelsLazyQueryHookResult,
  GetCapVehicleModelsQueryResult,
  GetCapVehicleMakesQueryVariables,
  GetCapVehicleMakesQuery,
  GetCapVehicleMakesQueryHookResult,
  GetCapVehicleMakesLazyQueryHookResult,
  GetCapVehicleMakesQueryResult,
  GetCapLeadsFromBusinessPartnerQueryVariables,
  GetCapLeadsFromBusinessPartnerQuery,
  GetCapLeadsFromBusinessPartnerQueryHookResult,
  GetCapLeadsFromBusinessPartnerLazyQueryHookResult,
  GetCapLeadsFromBusinessPartnerQueryResult,
  GetCapBusinessPartnersFromLeadQueryVariables,
  GetCapBusinessPartnersFromLeadQuery,
  GetCapBusinessPartnersFromLeadQueryHookResult,
  GetCapBusinessPartnersFromLeadLazyQueryHookResult,
  GetCapBusinessPartnersFromLeadQueryResult,
  GetCapBusinessPartnersQueryVariables,
  GetCapBusinessPartnersQuery,
  GetCapBusinessPartnersQueryHookResult,
  GetCapBusinessPartnersLazyQueryHookResult,
  GetCapBusinessPartnersQueryResult,
  GetCapBusinessPartnerDetailsQueryVariables,
  GetCapBusinessPartnerDetailsQuery,
  GetCapBusinessPartnerDetailsQueryHookResult,
  GetCapBusinessPartnerDetailsLazyQueryHookResult,
  GetCapBusinessPartnerDetailsQueryResult,
  GetCampaignQueryVariables,
  GetCampaignQuery,
  GetCampaignQueryHookResult,
  GetCampaignLazyQueryHookResult,
  GetCampaignQueryResult,
  GetBannerByModuleQueryVariables,
  GetBannerByModuleQuery,
  GetBannerByModuleQueryHookResult,
  GetBannerByModuleLazyQueryHookResult,
  GetBannerByModuleQueryResult,
  GetBannerQueryVariables,
  GetBannerQuery,
  GetBannerQueryHookResult,
  GetBannerLazyQueryHookResult,
  GetBannerQueryResult,
  GetBankOptionsQueryVariables,
  GetBankOptionsQuery,
  GetBankOptionsQueryHookResult,
  GetBankOptionsLazyQueryHookResult,
  GetBankOptionsQueryResult,
  GetBankQueryVariables,
  GetBankQuery,
  GetBankQueryHookResult,
  GetBankLazyQueryHookResult,
  GetBankQueryResult,
  GetAuthenticatedUserWebAuthnKeysQueryVariables,
  GetAuthenticatedUserWebAuthnKeysQuery,
  GetAuthenticatedUserWebAuthnKeysQueryHookResult,
  GetAuthenticatedUserWebAuthnKeysLazyQueryHookResult,
  GetAuthenticatedUserWebAuthnKeysQueryResult,
  GetAuthenticatedUserSessionsQueryVariables,
  GetAuthenticatedUserSessionsQuery,
  GetAuthenticatedUserSessionsQueryHookResult,
  GetAuthenticatedUserSessionsLazyQueryHookResult,
  GetAuthenticatedUserSessionsQueryResult,
  GetAuthenticatedUserCompaniesQueryVariables,
  GetAuthenticatedUserCompaniesQuery,
  GetAuthenticatedUserCompaniesQueryHookResult,
  GetAuthenticatedUserCompaniesLazyQueryHookResult,
  GetAuthenticatedUserCompaniesQueryResult,
  GetAuthenticatedUserQueryVariables,
  GetAuthenticatedUserQuery,
  GetAuthenticatedUserQueryHookResult,
  GetAuthenticatedUserLazyQueryHookResult,
  GetAuthenticatedUserQueryResult,
  GetApplicationsListFiltersQueryVariables,
  GetApplicationsListFiltersQuery,
  GetApplicationsListFiltersQueryHookResult,
  GetApplicationsListFiltersLazyQueryHookResult,
  GetApplicationsListFiltersQueryResult,
  GetApplicationSubmittedQueryVariables,
  GetApplicationSubmittedQuery,
  GetApplicationSubmittedQueryHookResult,
  GetApplicationSubmittedLazyQueryHookResult,
  GetApplicationSubmittedQueryResult,
  GetApplicationSigningStatusQueryVariables,
  GetApplicationSigningStatusQuery,
  GetApplicationSigningStatusQueryHookResult,
  GetApplicationSigningStatusLazyQueryHookResult,
  GetApplicationSigningStatusQueryResult,
  GetApplicationJourneyQueryVariables,
  GetApplicationJourneyQuery,
  GetApplicationJourneyQueryHookResult,
  GetApplicationJourneyLazyQueryHookResult,
  GetApplicationJourneyQueryResult,
  GetApplicationDocumentDownloadLinkQueryVariables,
  GetApplicationDocumentDownloadLinkQuery,
  GetApplicationDocumentDownloadLinkQueryHookResult,
  GetApplicationDocumentDownloadLinkLazyQueryHookResult,
  GetApplicationDocumentDownloadLinkQueryResult,
  GetApplicationCounterQueryVariables,
  GetApplicationCounterQuery,
  GetApplicationCounterQueryHookResult,
  GetApplicationCounterLazyQueryHookResult,
  GetApplicationCounterQueryResult,
  GetApplicationAuditTrailsQueryVariables,
  GetApplicationAuditTrailsQuery,
  GetApplicationAuditTrailsQueryHookResult,
  GetApplicationAuditTrailsLazyQueryHookResult,
  GetApplicationAuditTrailsQueryResult,
  GetApplicationAuditTrailKindsQueryVariables,
  GetApplicationAuditTrailKindsQuery,
  GetApplicationAuditTrailKindsQueryHookResult,
  GetApplicationAuditTrailKindsLazyQueryHookResult,
  GetApplicationAuditTrailKindsQueryResult,
  GetApplicationQueryVariables,
  GetApplicationQuery,
  GetApplicationQueryHookResult,
  GetApplicationLazyQueryHookResult,
  GetApplicationQueryResult,
  GetAddressAutoCompleteQueryVariables,
  GetAddressAutoCompleteQuery,
  GetAddressAutoCompleteQueryHookResult,
  GetAddressAutoCompleteLazyQueryHookResult,
  GetAddressAutoCompleteQueryResult,
  GetActiveCampaignsQueryVariables,
  GetActiveCampaignsQuery,
  GetActiveCampaignsQueryHookResult,
  GetActiveCampaignsLazyQueryHookResult,
  GetActiveCampaignsQueryResult,
  GenerateAuthenticatorSetupQueryVariables,
  GenerateAuthenticatorSetupQuery,
  GenerateAuthenticatorSetupQueryHookResult,
  GenerateAuthenticatorSetupLazyQueryHookResult,
  GenerateAuthenticatorSetupQueryResult,
  GenerateAuthenticatorChallengeQueryVariables,
  GenerateAuthenticatorChallengeQuery,
  GenerateAuthenticatorChallengeQueryHookResult,
  GenerateAuthenticatorChallengeLazyQueryHookResult,
  GenerateAuthenticatorChallengeQueryResult,
  CompanyFilterListQueryVariables,
  CompanyFilterListQuery,
  CompanyFilterListQueryHookResult,
  CompanyFilterListLazyQueryHookResult,
  CompanyFilterListQueryResult,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsQueryVariables,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsQuery,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsQueryHookResult,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsLazyQueryHookResult,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsQueryResult,
} from "./queries/index";

export {
  ValidateFileDocument,
  useValidateFileQuery,
  useValidateFileLazyQuery,
  RetrieveNamespacesDocument,
  useRetrieveNamespacesQuery,
  useRetrieveNamespacesLazyQuery,
  RetrieveLinkDocument,
  useRetrieveLinkQuery,
  useRetrieveLinkLazyQuery,
  RetrieveBlockedAppointmentTimeSlotDocument,
  useRetrieveBlockedAppointmentTimeSlotQuery,
  useRetrieveBlockedAppointmentTimeSlotLazyQuery,
  RefineTextDocument,
  useRefineTextQuery,
  useRefineTextLazyQuery,
  PrefetchKycFieldsForStandardApplicationDocument,
  usePrefetchKycFieldsForStandardApplicationQuery,
  usePrefetchKycFieldsForStandardApplicationLazyQuery,
  PrefetchKycFieldsForQualifyDocument,
  usePrefetchKycFieldsForQualifyQuery,
  usePrefetchKycFieldsForQualifyLazyQuery,
  PrefetchKycFieldsForLaunchPadAppointmentDocument,
  usePrefetchKycFieldsForLaunchPadAppointmentQuery,
  usePrefetchKycFieldsForLaunchPadAppointmentLazyQuery,
  PrefetchKycFieldsForLaunchPadApplicationDocument,
  usePrefetchKycFieldsForLaunchPadApplicationQuery,
  usePrefetchKycFieldsForLaunchPadApplicationLazyQuery,
  PrefetchKycFieldsForEventDocument,
  usePrefetchKycFieldsForEventQuery,
  usePrefetchKycFieldsForEventLazyQuery,
  PrefetchKycFieldsForContactDocument,
  usePrefetchKycFieldsForContactQuery,
  usePrefetchKycFieldsForContactLazyQuery,
  PrefetchGiftVoucherKycConsentsDocument,
  usePrefetchGiftVoucherKycConsentsQuery,
  usePrefetchGiftVoucherKycConsentsLazyQuery,
  PrefetchAgreementsForStandardApplicationDocument,
  usePrefetchAgreementsForStandardApplicationQuery,
  usePrefetchAgreementsForStandardApplicationLazyQuery,
  PrefetchAgreementsForLaunchPadShowroomVisitDocument,
  usePrefetchAgreementsForLaunchPadShowroomVisitQuery,
  usePrefetchAgreementsForLaunchPadShowroomVisitLazyQuery,
  PrefetchAgreementsForLaunchPadAppointmentDocument,
  usePrefetchAgreementsForLaunchPadAppointmentQuery,
  usePrefetchAgreementsForLaunchPadAppointmentLazyQuery,
  PrefetchAgreementsForLaunchPadDocument,
  usePrefetchAgreementsForLaunchPadQuery,
  usePrefetchAgreementsForLaunchPadLazyQuery,
  PrefetchAgreementsForEventDocument,
  usePrefetchAgreementsForEventQuery,
  usePrefetchAgreementsForEventLazyQuery,
  PrefetchAgreementsForContactDocument,
  usePrefetchAgreementsForContactQuery,
  usePrefetchAgreementsForContactLazyQuery,
  ListWhatsappLiveChatSettingsDocument,
  useListWhatsappLiveChatSettingsQuery,
  useListWhatsappLiveChatSettingsLazyQuery,
  ListWebPagesDocument,
  useListWebPagesQuery,
  useListWebPagesLazyQuery,
  ListWebCalcSettingsDocument,
  useListWebCalcSettingsQuery,
  useListWebCalcSettingsLazyQuery,
  ListVehiclesWithParametersDocument,
  useListVehiclesWithParametersQuery,
  useListVehiclesWithParametersLazyQuery,
  ListVehiclesDocument,
  useListVehiclesQuery,
  useListVehiclesLazyQuery,
  ListVariantConfiguratorsDocument,
  useListVariantConfiguratorsQuery,
  useListVariantConfiguratorsLazyQuery,
  ListUsersDocument,
  useListUsersQuery,
  useListUsersLazyQuery,
  ListUserlikeChatbotSettingsDocument,
  useListUserlikeChatbotSettingsQuery,
  useListUserlikeChatbotSettingsLazyQuery,
  ListUserGroupsDocument,
  useListUserGroupsQuery,
  useListUserGroupsLazyQuery,
  ListTradeInsDocument,
  useListTradeInsQuery,
  useListTradeInsLazyQuery,
  ListSalesOfferAuditTrailsDocument,
  useListSalesOfferAuditTrailsQuery,
  useListSalesOfferAuditTrailsLazyQuery,
  ListRoutersDocument,
  useListRoutersQuery,
  useListRoutersLazyQuery,
  ListRolesDocument,
  useListRolesQuery,
  useListRolesLazyQuery,
  ListPorschePaymentSettingsDocument,
  useListPorschePaymentSettingsQuery,
  useListPorschePaymentSettingsLazyQuery,
  ListMyInfoSettingDocument,
  useListMyInfoSettingQuery,
  useListMyInfoSettingLazyQuery,
  ListMyInfoModulesDocument,
  useListMyInfoModulesQuery,
  useListMyInfoModulesLazyQuery,
  ListModulesForApplicationDownloadDocument,
  useListModulesForApplicationDownloadQuery,
  useListModulesForApplicationDownloadLazyQuery,
  ListModulesDocument,
  useListModulesQuery,
  useListModulesLazyQuery,
  ListModuleTypeDocument,
  useListModuleTypeQuery,
  useListModuleTypeLazyQuery,
  ListModelConfiguratorsDocument,
  useListModelConfiguratorsQuery,
  useListModelConfiguratorsLazyQuery,
  ListMobilityStocksDocument,
  useListMobilityStocksQuery,
  useListMobilityStocksLazyQuery,
  ListMobilityHomeDeliveriesDocument,
  useListMobilityHomeDeliveriesQuery,
  useListMobilityHomeDeliveriesLazyQuery,
  ListMobilitiesDocument,
  useListMobilitiesQuery,
  useListMobilitiesLazyQuery,
  ListLocationsDocument,
  useListLocationsQuery,
  useListLocationsLazyQuery,
  ListLocationConditionsDocument,
  useListLocationConditionsQuery,
  useListLocationConditionsLazyQuery,
  ListLocalVariantsForSelectionDocument,
  useListLocalVariantsForSelectionQuery,
  useListLocalVariantsForSelectionLazyQuery,
  ListLocalVariantsForCalculatorDocument,
  useListLocalVariantsForCalculatorQuery,
  useListLocalVariantsForCalculatorLazyQuery,
  ListLocalVariantsDocument,
  useListLocalVariantsQuery,
  useListLocalVariantsLazyQuery,
  ListLocalModelsDocument,
  useListLocalModelsQuery,
  useListLocalModelsLazyQuery,
  ListLocalMakesDocument,
  useListLocalMakesQuery,
  useListLocalMakesLazyQuery,
  ListLeadsDocument,
  useListLeadsQuery,
  useListLeadsLazyQuery,
  ListLeadAuditTrailsDocument,
  useListLeadAuditTrailsQuery,
  useListLeadAuditTrailsLazyQuery,
  ListLanguagePacksDocument,
  useListLanguagePacksQuery,
  useListLanguagePacksLazyQuery,
  ListLabelsDocument,
  useListLabelsQuery,
  useListLabelsLazyQuery,
  ListInventoriesFilterDocument,
  useListInventoriesFilterQuery,
  useListInventoriesFilterLazyQuery,
  ListInventoriesDocument,
  useListInventoriesQuery,
  useListInventoriesLazyQuery,
  ListInsurersDocument,
  useListInsurersQuery,
  useListInsurersLazyQuery,
  ListInsuranceProductsForProductionDocument,
  useListInsuranceProductsForProductionQuery,
  useListInsuranceProductsForProductionLazyQuery,
  ListInsuranceProductsDocument,
  useListInsuranceProductsQuery,
  useListInsuranceProductsLazyQuery,
  ListGiftVouchersDocument,
  useListGiftVouchersQuery,
  useListGiftVouchersLazyQuery,
  ListFiservPaymentSettingsDocument,
  useListFiservPaymentSettingsQuery,
  useListFiservPaymentSettingsLazyQuery,
  ListFinderVehiclesForSelectionDocument,
  useListFinderVehiclesForSelectionQuery,
  useListFinderVehiclesForSelectionLazyQuery,
  ListFinderVehiclesForCalculatorDocument,
  useListFinderVehiclesForCalculatorQuery,
  useListFinderVehiclesForCalculatorLazyQuery,
  ListFinderVehiclesDocument,
  useListFinderVehiclesQuery,
  useListFinderVehiclesLazyQuery,
  ListFinanceProductsForProductionDocument,
  useListFinanceProductsForProductionQuery,
  useListFinanceProductsForProductionLazyQuery,
  ListFinanceProductsDocument,
  useListFinanceProductsQuery,
  useListFinanceProductsLazyQuery,
  ListEventsDocument,
  useListEventsQuery,
  useListEventsLazyQuery,
  ListEventFiltersDocument,
  useListEventFiltersQuery,
  useListEventFiltersLazyQuery,
  ListDocusignSettingsDocument,
  useListDocusignSettingsQuery,
  useListDocusignSettingsLazyQuery,
  ListDealersDocument,
  useListDealersQuery,
  useListDealersLazyQuery,
  ListCustomersDocument,
  useListCustomersQuery,
  useListCustomersLazyQuery,
  ListConsentsAndDeclarationsDocument,
  useListConsentsAndDeclarationsQuery,
  useListConsentsAndDeclarationsLazyQuery,
  ListCompaniesForSelectionDocument,
  useListCompaniesForSelectionQuery,
  useListCompaniesForSelectionLazyQuery,
  ListCompaniesForModuleDocument,
  useListCompaniesForModuleQuery,
  useListCompaniesForModuleLazyQuery,
  ListCompaniesContextDocument,
  useListCompaniesContextQuery,
  useListCompaniesContextLazyQuery,
  ListCompaniesDocument,
  useListCompaniesQuery,
  useListCompaniesLazyQuery,
  ListCampaignsDocument,
  useListCampaignsQuery,
  useListCampaignsLazyQuery,
  ListBannersDocument,
  useListBannersQuery,
  useListBannersLazyQuery,
  ListBanksDocument,
  useListBanksQuery,
  useListBanksLazyQuery,
  ListBankOptionsDocument,
  useListBankOptionsQuery,
  useListBankOptionsLazyQuery,
  ListAvailableUsersForUserGroupDocument,
  useListAvailableUsersForUserGroupQuery,
  useListAvailableUsersForUserGroupLazyQuery,
  ListAvailableUsersByRoleDocument,
  useListAvailableUsersByRoleQuery,
  useListAvailableUsersByRoleLazyQuery,
  ListAvailableUserGroupsDocument,
  useListAvailableUserGroupsQuery,
  useListAvailableUserGroupsLazyQuery,
  ListAvailablePermissionsDocument,
  useListAvailablePermissionsQuery,
  useListAvailablePermissionsLazyQuery,
  ListApplicationsStatusDocument,
  useListApplicationsStatusQuery,
  useListApplicationsStatusLazyQuery,
  ListApplicationsModulesDocument,
  useListApplicationsModulesQuery,
  useListApplicationsModulesLazyQuery,
  ListApplicationsDocument,
  useListApplicationsQuery,
  useListApplicationsLazyQuery,
  ListAdyenPaymentSettingsDocument,
  useListAdyenPaymentSettingsQuery,
  useListAdyenPaymentSettingsLazyQuery,
  HasValidPromoCodeDocument,
  useHasValidPromoCodeQuery,
  useHasValidPromoCodeLazyQuery,
  GetWebPageOptionsDocument,
  useGetWebPageOptionsQuery,
  useGetWebPageOptionsLazyQuery,
  GetWebPageDocument,
  useGetWebPageQuery,
  useGetWebPageLazyQuery,
  GetVehicleOptionsDocument,
  useGetVehicleOptionsQuery,
  useGetVehicleOptionsLazyQuery,
  GetVehicleBySuiteIdDocument,
  useGetVehicleBySuiteIdQuery,
  useGetVehicleBySuiteIdLazyQuery,
  GetVehicleDocument,
  useGetVehicleQuery,
  useGetVehicleLazyQuery,
  GetVariantsOfAllConfiguratorsDocument,
  useGetVariantsOfAllConfiguratorsQuery,
  useGetVariantsOfAllConfiguratorsLazyQuery,
  GetVariantConfiguratorDocument,
  useGetVariantConfiguratorQuery,
  useGetVariantConfiguratorLazyQuery,
  GetValidPromoCodeByCodeDocument,
  useGetValidPromoCodeByCodeQuery,
  useGetValidPromoCodeByCodeLazyQuery,
  GetValidDiscountCodeDocument,
  useGetValidDiscountCodeQuery,
  useGetValidDiscountCodeLazyQuery,
  GetUsersOptionsForUserGroupDocument,
  useGetUsersOptionsForUserGroupQuery,
  useGetUsersOptionsForUserGroupLazyQuery,
  GetUsersOptionsDocument,
  useGetUsersOptionsQuery,
  useGetUsersOptionsLazyQuery,
  GetUserGroupOptionsDocument,
  useGetUserGroupOptionsQuery,
  useGetUserGroupOptionsLazyQuery,
  GetUserGroupDocument,
  useGetUserGroupQuery,
  useGetUserGroupLazyQuery,
  GetUserDocument,
  useGetUserQuery,
  useGetUserLazyQuery,
  GetTradeInListFiltersDocument,
  useGetTradeInListFiltersQuery,
  useGetTradeInListFiltersLazyQuery,
  GetTradeInAuditTrailsDocument,
  useGetTradeInAuditTrailsQuery,
  useGetTradeInAuditTrailsLazyQuery,
  GetTradeInDocument,
  useGetTradeInQuery,
  useGetTradeInLazyQuery,
  GetTopSalesPersonsDocument,
  useGetTopSalesPersonsQuery,
  useGetTopSalesPersonsLazyQuery,
  GetStockInventoryByVariantConfiguratorDocument,
  useGetStockInventoryByVariantConfiguratorQuery,
  useGetStockInventoryByVariantConfiguratorLazyQuery,
  GetStockAuditTrailsDocument,
  useGetStockAuditTrailsQuery,
  useGetStockAuditTrailsLazyQuery,
  GetSettingOptionsForPorschePaymentModuleDocument,
  useGetSettingOptionsForPorschePaymentModuleQuery,
  useGetSettingOptionsForPorschePaymentModuleLazyQuery,
  GetSettingOptionsForFiservPaymentModuleDocument,
  useGetSettingOptionsForFiservPaymentModuleQuery,
  useGetSettingOptionsForFiservPaymentModuleLazyQuery,
  GetSettingOptionsForAdyenPaymentModuleDocument,
  useGetSettingOptionsForAdyenPaymentModuleQuery,
  useGetSettingOptionsForAdyenPaymentModuleLazyQuery,
  GetSalesOfferJourneyDocument,
  useGetSalesOfferJourneyQuery,
  useGetSalesOfferJourneyLazyQuery,
  GetSalesOfferDocument,
  useGetSalesOfferQuery,
  useGetSalesOfferLazyQuery,
  GetSalesControlBoardFilterOptionsDocument,
  useGetSalesControlBoardFilterOptionsQuery,
  useGetSalesControlBoardFilterOptionsLazyQuery,
  GetSalesControlBoardDocument,
  useGetSalesControlBoardQuery,
  useGetSalesControlBoardLazyQuery,
  GetRouterSpecsDocument,
  useGetRouterSpecsQuery,
  useGetRouterSpecsLazyQuery,
  GetRouterEndpointListDocument,
  useGetRouterEndpointListQuery,
  useGetRouterEndpointListLazyQuery,
  GetRouterContextDocument,
  useGetRouterContextQuery,
  useGetRouterContextLazyQuery,
  GetRoleDocument,
  useGetRoleQuery,
  useGetRoleLazyQuery,
  GetReferenceApplicationListEndpointsDocument,
  useGetReferenceApplicationListEndpointsQuery,
  useGetReferenceApplicationListEndpointsLazyQuery,
  GetPublicStockDocument,
  useGetPublicStockQuery,
  useGetPublicStockLazyQuery,
  GetPublicAssigneesDocument,
  useGetPublicAssigneesQuery,
  useGetPublicAssigneesLazyQuery,
  GetPromoCodeModuleOptionsDocument,
  useGetPromoCodeModuleOptionsQuery,
  useGetPromoCodeModuleOptionsLazyQuery,
  GetPromoCodeListingDocument,
  useGetPromoCodeListingQuery,
  useGetPromoCodeListingLazyQuery,
  GetPromoCodeDocument,
  useGetPromoCodeQuery,
  useGetPromoCodeLazyQuery,
  GetPorscheVehicleDataDocument,
  useGetPorscheVehicleDataQuery,
  useGetPorscheVehicleDataLazyQuery,
  GetPorschePaymentMethodsForSalesOfferDocument,
  useGetPorschePaymentMethodsForSalesOfferQuery,
  useGetPorschePaymentMethodsForSalesOfferLazyQuery,
  GetPorschePaymentMethodsDocument,
  useGetPorschePaymentMethodsQuery,
  useGetPorschePaymentMethodsLazyQuery,
  GetPorscheIdAuthorizeUrlDocument,
  useGetPorscheIdAuthorizeUrlQuery,
  useGetPorscheIdAuthorizeUrlLazyQuery,
  GetPopularVariantApplicationDocument,
  useGetPopularVariantApplicationQuery,
  useGetPopularVariantApplicationLazyQuery,
  GetPopularEventApplicationDocument,
  useGetPopularEventApplicationQuery,
  useGetPopularEventApplicationLazyQuery,
  GetNearbyDealersDocument,
  useGetNearbyDealersQuery,
  useGetNearbyDealersLazyQuery,
  GetMyInfoAuthorizeUrlDocument,
  useGetMyInfoAuthorizeUrlQuery,
  useGetMyInfoAuthorizeUrlLazyQuery,
  GetMonthOfImportOptionsDocument,
  useGetMonthOfImportOptionsQuery,
  useGetMonthOfImportOptionsLazyQuery,
  GetModulesWithScenariosDocument,
  useGetModulesWithScenariosQuery,
  useGetModulesWithScenariosLazyQuery,
  GetModulesOptionsDocument,
  useGetModulesOptionsQuery,
  useGetModulesOptionsLazyQuery,
  GetModulesListDocument,
  useGetModulesListQuery,
  useGetModulesListLazyQuery,
  GetModuleWithPermissionsSpecsDocument,
  useGetModuleWithPermissionsSpecsQuery,
  useGetModuleWithPermissionsSpecsLazyQuery,
  GetModuleSpecsDocument,
  useGetModuleSpecsQuery,
  useGetModuleSpecsLazyQuery,
  GetModelConfiguratorForApplicationDocument,
  useGetModelConfiguratorForApplicationQuery,
  useGetModelConfiguratorForApplicationLazyQuery,
  GetModelConfiguratorFiltersDocument,
  useGetModelConfiguratorFiltersQuery,
  useGetModelConfiguratorFiltersLazyQuery,
  GetModelConfiguratorDocument,
  useGetModelConfiguratorQuery,
  useGetModelConfiguratorLazyQuery,
  GetMobilityVehicleFiltersDocument,
  useGetMobilityVehicleFiltersQuery,
  useGetMobilityVehicleFiltersLazyQuery,
  GetMobilityLocationDocument,
  useGetMobilityLocationQuery,
  useGetMobilityLocationLazyQuery,
  GetMobilityHomeDeliveryDocument,
  useGetMobilityHomeDeliveryQuery,
  useGetMobilityHomeDeliveryLazyQuery,
  GetMobilityDocument,
  useGetMobilityQuery,
  useGetMobilityLazyQuery,
  GetMarketingDashboardFilterOptionDocument,
  useGetMarketingDashboardFilterOptionQuery,
  useGetMarketingDashboardFilterOptionLazyQuery,
  GetMarketingDashboardDocument,
  useGetMarketingDashboardQuery,
  useGetMarketingDashboardLazyQuery,
  GetLowestMonthlyInstalmentDocument,
  useGetLowestMonthlyInstalmentQuery,
  useGetLowestMonthlyInstalmentLazyQuery,
  GetLocalVariantsOptionsDocument,
  useGetLocalVariantsOptionsQuery,
  useGetLocalVariantsOptionsLazyQuery,
  GetLeadsListFiltersDocument,
  useGetLeadsListFiltersQuery,
  useGetLeadsListFiltersLazyQuery,
  GetLeadDocumentDownloadLinkDocument,
  useGetLeadDocumentDownloadLinkQuery,
  useGetLeadDocumentDownloadLinkLazyQuery,
  GetLeadDocument,
  useGetLeadQuery,
  useGetLeadLazyQuery,
  GetLanguagePackOptionsDocument,
  useGetLanguagePackOptionsQuery,
  useGetLanguagePackOptionsLazyQuery,
  GetLanguagePackListFiltersDocument,
  useGetLanguagePackListFiltersQuery,
  useGetLanguagePackListFiltersLazyQuery,
  GetLanguagePackDocument,
  useGetLanguagePackQuery,
  useGetLanguagePackLazyQuery,
  GetLabelsByFeatureModuleIdDocument,
  useGetLabelsByFeatureModuleIdQuery,
  useGetLabelsByFeatureModuleIdLazyQuery,
  GetLabelDocument,
  useGetLabelQuery,
  useGetLabelLazyQuery,
  GetJourneyEventDocument,
  useGetJourneyEventQuery,
  useGetJourneyEventLazyQuery,
  GetInventoryOptionsDocument,
  useGetInventoryOptionsQuery,
  useGetInventoryOptionsLazyQuery,
  GetInventoryAuditTrailsDocument,
  useGetInventoryAuditTrailsQuery,
  useGetInventoryAuditTrailsLazyQuery,
  GetInventoryDocument,
  useGetInventoryQuery,
  useGetInventoryLazyQuery,
  GetInventoriesByMobilityModuleIdDocument,
  useGetInventoriesByMobilityModuleIdQuery,
  useGetInventoriesByMobilityModuleIdLazyQuery,
  GetInsurersByModuleIdDocument,
  useGetInsurersByModuleIdQuery,
  useGetInsurersByModuleIdLazyQuery,
  GetInsurerDocument,
  useGetInsurerQuery,
  useGetInsurerLazyQuery,
  GetInsuranceProductsDocument,
  useGetInsuranceProductsQuery,
  useGetInsuranceProductsLazyQuery,
  GetInsuranceProductDocument,
  useGetInsuranceProductQuery,
  useGetInsuranceProductLazyQuery,
  GetInsurancePremiumDocument,
  useGetInsurancePremiumQuery,
  useGetInsurancePremiumLazyQuery,
  GetImportedLocationFromExcelDocument,
  useGetImportedLocationFromExcelQuery,
  useGetImportedLocationFromExcelLazyQuery,
  GetImportedFpTableFromExcelDocument,
  useGetImportedFpTableFromExcelQuery,
  useGetImportedFpTableFromExcelLazyQuery,
  GetGiftVouchersListFiltersDocument,
  useGetGiftVouchersListFiltersQuery,
  useGetGiftVouchersListFiltersLazyQuery,
  GetGiftVoucherJourneyDocument,
  useGetGiftVoucherJourneyQuery,
  useGetGiftVoucherJourneyLazyQuery,
  GetGiftVoucherDocument,
  useGetGiftVoucherQuery,
  useGetGiftVoucherLazyQuery,
  GetFirstEventRouterPathDocument,
  useGetFirstEventRouterPathQuery,
  useGetFirstEventRouterPathLazyQuery,
  GetFinderVehicleSyncStatusDocument,
  useGetFinderVehicleSyncStatusQuery,
  useGetFinderVehicleSyncStatusLazyQuery,
  GetFinderVehicleOptionsDocument,
  useGetFinderVehicleOptionsQuery,
  useGetFinderVehicleOptionsLazyQuery,
  GetFinderVehicleDocument,
  useGetFinderVehicleQuery,
  useGetFinderVehicleLazyQuery,
  GetFinanceProductsDocument,
  useGetFinanceProductsQuery,
  useGetFinanceProductsLazyQuery,
  GetFinanceProductDocument,
  useGetFinanceProductQuery,
  useGetFinanceProductLazyQuery,
  GetEventApplicationsDocument,
  useGetEventApplicationsQuery,
  useGetEventApplicationsLazyQuery,
  GetEventDocument,
  useGetEventQuery,
  useGetEventLazyQuery,
  GetDepositPaymentPerDayDocument,
  useGetDepositPaymentPerDayQuery,
  useGetDepositPaymentPerDayLazyQuery,
  GetDealersOptionsDocument,
  useGetDealersOptionsQuery,
  useGetDealersOptionsLazyQuery,
  GetDealerDocument,
  useGetDealerQuery,
  useGetDealerLazyQuery,
  GetDbsInstantApprovalRateDocument,
  useGetDbsInstantApprovalRateQuery,
  useGetDbsInstantApprovalRateLazyQuery,
  GetDataFromPorscheIdDocument,
  useGetDataFromPorscheIdQuery,
  useGetDataFromPorscheIdLazyQuery,
  GetDataFromMyInfoDocument,
  useGetDataFromMyInfoQuery,
  useGetDataFromMyInfoLazyQuery,
  GetCustomerListFiltersDocument,
  useGetCustomerListFiltersQuery,
  useGetCustomerListFiltersLazyQuery,
  GetCustomerFieldsFromFilesDocument,
  useGetCustomerFieldsFromFilesQuery,
  useGetCustomerFieldsFromFilesLazyQuery,
  GetCustomerAuditTrailsDocument,
  useGetCustomerAuditTrailsQuery,
  useGetCustomerAuditTrailsLazyQuery,
  GetCustomerDocument,
  useGetCustomerQuery,
  useGetCustomerLazyQuery,
  GetContactsFromBpDocument,
  useGetContactsFromBpQuery,
  useGetContactsFromBpLazyQuery,
  GetConsentsAndDeclarationsDocument,
  useGetConsentsAndDeclarationsQuery,
  useGetConsentsAndDeclarationsLazyQuery,
  GetConditionDetailDocument,
  useGetConditionDetailQuery,
  useGetConditionDetailLazyQuery,
  GetCompanySpecsForNewModuleDocument,
  useGetCompanySpecsForNewModuleQuery,
  useGetCompanySpecsForNewModuleLazyQuery,
  GetCompanyContextDocument,
  useGetCompanyContextQuery,
  useGetCompanyContextLazyQuery,
  GetCompanyDocument,
  useGetCompanyQuery,
  useGetCompanyLazyQuery,
  GetCapVehicleModelsDocument,
  useGetCapVehicleModelsQuery,
  useGetCapVehicleModelsLazyQuery,
  GetCapVehicleMakesDocument,
  useGetCapVehicleMakesQuery,
  useGetCapVehicleMakesLazyQuery,
  GetCapLeadsFromBusinessPartnerDocument,
  useGetCapLeadsFromBusinessPartnerQuery,
  useGetCapLeadsFromBusinessPartnerLazyQuery,
  GetCapBusinessPartnersFromLeadDocument,
  useGetCapBusinessPartnersFromLeadQuery,
  useGetCapBusinessPartnersFromLeadLazyQuery,
  GetCapBusinessPartnersDocument,
  useGetCapBusinessPartnersQuery,
  useGetCapBusinessPartnersLazyQuery,
  GetCapBusinessPartnerDetailsDocument,
  useGetCapBusinessPartnerDetailsQuery,
  useGetCapBusinessPartnerDetailsLazyQuery,
  GetCampaignDocument,
  useGetCampaignQuery,
  useGetCampaignLazyQuery,
  GetBannerByModuleDocument,
  useGetBannerByModuleQuery,
  useGetBannerByModuleLazyQuery,
  GetBannerDocument,
  useGetBannerQuery,
  useGetBannerLazyQuery,
  GetBankOptionsDocument,
  useGetBankOptionsQuery,
  useGetBankOptionsLazyQuery,
  GetBankDocument,
  useGetBankQuery,
  useGetBankLazyQuery,
  GetAuthenticatedUserWebAuthnKeysDocument,
  useGetAuthenticatedUserWebAuthnKeysQuery,
  useGetAuthenticatedUserWebAuthnKeysLazyQuery,
  GetAuthenticatedUserSessionsDocument,
  useGetAuthenticatedUserSessionsQuery,
  useGetAuthenticatedUserSessionsLazyQuery,
  GetAuthenticatedUserCompaniesDocument,
  useGetAuthenticatedUserCompaniesQuery,
  useGetAuthenticatedUserCompaniesLazyQuery,
  GetAuthenticatedUserDocument,
  useGetAuthenticatedUserQuery,
  useGetAuthenticatedUserLazyQuery,
  GetApplicationsListFiltersDocument,
  useGetApplicationsListFiltersQuery,
  useGetApplicationsListFiltersLazyQuery,
  GetApplicationSubmittedDocument,
  useGetApplicationSubmittedQuery,
  useGetApplicationSubmittedLazyQuery,
  GetApplicationSigningStatusDocument,
  useGetApplicationSigningStatusQuery,
  useGetApplicationSigningStatusLazyQuery,
  GetApplicationJourneyDocument,
  useGetApplicationJourneyQuery,
  useGetApplicationJourneyLazyQuery,
  GetApplicationDocumentDownloadLinkDocument,
  useGetApplicationDocumentDownloadLinkQuery,
  useGetApplicationDocumentDownloadLinkLazyQuery,
  GetApplicationCounterDocument,
  useGetApplicationCounterQuery,
  useGetApplicationCounterLazyQuery,
  GetApplicationAuditTrailsDocument,
  useGetApplicationAuditTrailsQuery,
  useGetApplicationAuditTrailsLazyQuery,
  GetApplicationAuditTrailKindsDocument,
  useGetApplicationAuditTrailKindsQuery,
  useGetApplicationAuditTrailKindsLazyQuery,
  GetApplicationDocument,
  useGetApplicationQuery,
  useGetApplicationLazyQuery,
  GetAddressAutoCompleteDocument,
  useGetAddressAutoCompleteQuery,
  useGetAddressAutoCompleteLazyQuery,
  GetActiveCampaignsDocument,
  useGetActiveCampaignsQuery,
  useGetActiveCampaignsLazyQuery,
  GenerateAuthenticatorSetupDocument,
  useGenerateAuthenticatorSetupQuery,
  useGenerateAuthenticatorSetupLazyQuery,
  GenerateAuthenticatorChallengeDocument,
  useGenerateAuthenticatorChallengeQuery,
  useGenerateAuthenticatorChallengeLazyQuery,
  CompanyFilterListDocument,
  useCompanyFilterListQuery,
  useCompanyFilterListLazyQuery,
  CheckAvailableInventoryStockByVariantConfiguratorSettingsDocument,
  useCheckAvailableInventoryStockByVariantConfiguratorSettingsQuery,
  useCheckAvailableInventoryStockByVariantConfiguratorSettingsLazyQuery,
} from "./queries/index";

export type {
  StatisticNameCounterFragment,
  StatisticNameCountResultFragment,
  StatisticNameAmountResultFragment,
  StatisticNameAmountNumberFragment,
  StatisticDateCounterFragment,
  StatisticDateCountResultFragment,
  StatisticDateAmountResultFragment,
  StatisticDateAmountFragment,
  FullListingValueFragment,
  FormattedDateDataFragment,
  LocalizedStringDataFragment,
  CurrencyDataFragment,
  LocalizedValueDataFragment,
  NumberUnitDataFragment,
  WhatsappLiveChatSettingsSpecFragment,
  WhatsappLiveChatModuleWithPermissionsSpecsFragment,
  WhatsappLiveChatModuleSpecsFragment,
  WebsiteModuleWithPermissionsSpecsFragment,
  WebsiteModuleSpecsFragment,
  WebsiteModulePublicSpecsFragment,
  WebpageEndpointContextDataFragment,
  WebPagePathDataFragment,
  WebPageOptionDataFragment,
  WebPageListDataFragment,
  WebPageEndpointSpecsFragment,
  WebPageDataFragment,
  WebPageButtonDataFragment,
  WebPageBlockData_ColumnWebPageBlock_Fragment,
  WebPageBlockData_CustomWebPageBlock_Fragment,
  WebPageBlockData_ImageWebPageBlock_Fragment,
  WebPageBlockData_TextCarouselWebPageBlock_Fragment,
  WebPageBlockData_TextImageWebPageBlock_Fragment,
  WebPageBlockDataFragment,
  WebCalcSettingDataFragment,
  VisitAppointmentModuleWithPermissionsSpecsFragment,
  VisitAppointmentModuleSpecsFragment,
  VisitAppointmentModuleInDealerSpecsFragment,
  VisitAppointmentModuleEmailContentSpecsFragment,
  VisitAppointmentModuleEmailContentCustomerSpecsFragment,
  VisitAppointmentModuleEmailContentSalesPersonSpecsFragment,
  VisitAppointmentModuleEmailContentsSpecsFragment,
  VisitAppointmentModuleApplicationJourneyFragment,
  VehiclesListByParametersSpecsFragment,
  VehicleWithPermissionsSpecs_FinderVehicle_Fragment,
  VehicleWithPermissionsSpecs_LocalMake_Fragment,
  VehicleWithPermissionsSpecs_LocalModel_Fragment,
  VehicleWithPermissionsSpecs_LocalVariant_Fragment,
  VehicleWithPermissionsSpecsFragment,
  VehicleSpecs_FinderVehicle_Fragment,
  VehicleSpecs_LocalMake_Fragment,
  VehicleSpecs_LocalModel_Fragment,
  VehicleSpecs_LocalVariant_Fragment,
  VehicleSpecsFragment,
  VehicleSalesOfferSpecsFragment,
  VehicleReferenceParametersDataFragment,
  VehicleOptionsData_FinderVehicle_Fragment,
  VehicleOptionsData_LocalMake_Fragment,
  VehicleOptionsData_LocalModel_Fragment,
  VehicleOptionsData_LocalVariant_Fragment,
  VehicleOptionsDataFragment,
  VehicleListData_FinderVehicle_Fragment,
  VehicleListData_LocalMake_Fragment,
  VehicleListData_LocalModel_Fragment,
  VehicleListData_LocalVariant_Fragment,
  VehicleListDataFragment,
  VehicleDetails_FinderVehicle_Fragment,
  VehicleDetails_LocalMake_Fragment,
  VehicleDetails_LocalModel_Fragment,
  VehicleDetails_LocalVariant_Fragment,
  VehicleDetailsFragment,
  VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment,
  VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment,
  VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment,
  VehicleCalculatorSpecs_FinderVehicle_Fragment,
  VehicleCalculatorSpecs_LocalMake_Fragment,
  VehicleCalculatorSpecs_LocalModel_Fragment,
  VehicleCalculatorSpecs_LocalVariant_Fragment,
  VehicleCalculatorSpecsFragment,
  VariantConfiguratorWithPermissionsSpecsFragment,
  VariantConfiguratorSpecsFragment,
  VariantConfiguratorListItemFragment,
  VariantConfiguratorJourneyDataFragment,
  VariantConfiguratorDetailsFragment,
  VsaSalesOfferSpecsFragment,
  UsersOptionsDataFragment,
  UserlikeChatbotSettingsSpecFragment,
  UserlikeChatbotModuleWithPermissionsSpecsFragment,
  UserlikeChatbotModuleSpecsFragment,
  UserSpecsFragment,
  UserPreviewDataFragment,
  UserListDataFragment,
  UserGroupSpecsFragment,
  UserGroupOptionsDataFragment,
  UserGroupListDataFragment,
  UserFullDataFragment,
  UserCompanyContextDataFragment,
  UserAvatarSpecsFragment,
  UploadedVariantAssetDataFragment,
  UploadFileWithPreviewFormDataFragment,
  UploadFileFormDataFragment,
  TtbPaymentSettingsSpecFragment,
  TtbPaymentModuleWithPermissionsSpecsFragment,
  TtbPaymentModuleSpecsFragment,
  TrimBlockSpecsFragment,
  TranslatedTextDataFragment,
  TranslatedStringSpecsFragment,
  TranslatedStringDataFragment,
  TradeInVehicleDataFragment,
  TradeInSettingSpecFragment,
  TradeInSalesOfferSpecsFragment,
  TradeInModuleWithPermissionsSpecsFragment,
  TradeInModuleSpecsFragment,
  TradeInListDataFragment,
  TradeInDataFragment,
  TimeSlotDataFragment,
  ThankYouPageContentSpecsFragment,
  TextImageWebPageBlockDataFragment,
  TextCarouselWebPageBlockDataFragment,
  TestDriveFixedPeriodDataFragment,
  TestDriveBookingWindowSettingsDataFragment,
  TermSettingsDetails_DeferredPrincipalTermSettings_Fragment,
  TermSettingsDetails_GenericPrincipalTermSettings_Fragment,
  TermSettingsDetailsFragment,
  SystemMessageData_MaintenanceUpdate_Fragment,
  SystemMessageData_MessageNotice_Fragment,
  SystemMessageData_UserSessionRevoked_Fragment,
  SystemMessageDataFragment,
  StockPublicData_ConfiguratorStockInventory_Fragment,
  StockPublicData_MobilityStockInventory_Fragment,
  StockPublicDataFragment,
  StockInventorySpecs_ConfiguratorStockInventory_Fragment,
  StockInventorySpecs_MobilityStockInventory_Fragment,
  StockInventorySpecsFragment,
  StockDetailsPublicData_ConfiguratorStockInventory_Fragment,
  StockDetailsPublicData_MobilityStockInventory_Fragment,
  StockDetailsPublicDataFragment,
  StockBlockingPeriodDataFragment,
  StandardLeadDataFragment,
  StandardApplicationSpecFragment,
  StandardApplicationPublicAccessEntrypointSpecsFragment,
  StandardApplicationPublicAccessEntrypointContextDataFragment,
  StandardApplicationModuleWithPermissionsSpecsFragment,
  StandardApplicationModuleSpecsForApplicationFragment,
  StandardApplicationModuleSpecsFragment,
  StandardApplicationModuleInDealerSpecsFragment,
  StandardApplicationModuleEmailContentSpecsFragment,
  StandardApplicationModuleEmailContentShareSubmissionSpecsFragment,
  StandardApplicationModuleEmailContentCustomerSpecsFragment,
  StandardApplicationModuleEmailContentSalesPersonSpecsFragment,
  StandardApplicationModuleEmailContentsSpecsFragment,
  StandardApplicationModuleDebugJourneyFragment,
  StandardApplicationEntrypointSpecsFragment,
  StandardApplicationEntrypointContextDataFragment,
  StandardApplicationDataFragment,
  SimpleVersioningDataFragment,
  SimpleVehicleManagementModuleWithPermissionsSpecsFragment,
  SimpleVehicleManagementModuleSpecsFragment,
  SalesOfferSpecsFragment,
  SalesOfferSigningsSpecsFragment,
  SalesOfferModuleWithPermissionsSpecsFragment,
  SalesOfferModuleSpecsFragment,
  SalesOfferModuleInDealerSpecsFragment,
  SalesOfferModuleEmailContentsSpecsFragment,
  SalesOfferEmailContentsSpecsFragment,
  SalesOfferModuleDebugJourneyFragment,
  SalesOfferKycPresetSpecsFragment,
  SalesOfferJourneyDataFragment,
  SalesOfferDocumentDataFragment,
  SalesOfferConsentsSpecsFragment,
  SalesOfferApplicationSpecsFragment,
  SalesOfferApplicationDataFragment,
  SalesOfferApplicationConfigurationDataFragment,
  SalesControlBoardModuleWithPermissionsSpecsFragment,
  SalesControlBoardModuleSpecsFragment,
  SalesControlBoardModuleInDealerSpecsFragment,
  RouterWithPermissionsSpecsFragment,
  RouterFinderApplicationModuleFragment,
  RouterFinderApplicationPrivateModuleFragment,
  RouterSpecsFragment,
  RouterListWithEndpointsDataFragment,
  RouterListDataFragment,
  RouterLayoutContextData_BasicLayout_Fragment,
  RouterLayoutContextData_BasicProLayout_Fragment,
  RouterLayoutContextData_PorscheV3Layout_Fragment,
  RouterLayoutContextDataFragment,
  RouterContextDataFragment,
  RoleSpecsFragment,
  RoleListDataFragment,
  ResidualValueSettingsDetailsFragment,
  ReferenceDepositData_ApplicationAdyenDeposit_Fragment,
  ReferenceDepositData_ApplicationFiservDeposit_Fragment,
  ReferenceDepositData_ApplicationPayGateDeposit_Fragment,
  ReferenceDepositData_ApplicationPorscheDeposit_Fragment,
  ReferenceDepositData_ApplicationTtbDeposit_Fragment,
  ReferenceDepositDataFragment,
  ReferenceFinancingData_DefaultApplicationFinancing_Fragment,
  ReferenceFinancingData_NewZealandApplicationFinancing_Fragment,
  ReferenceFinancingData_SingaporeApplicationFinancing_Fragment,
  ReferenceFinancingDataFragment,
  ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment,
  ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment,
  ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment,
  ReferenceInsuranceDataFragment,
  ReferenceApplicationData_ConfiguratorApplication_Fragment,
  ReferenceApplicationData_EventApplication_Fragment,
  ReferenceApplicationData_FinderApplication_Fragment,
  ReferenceApplicationData_LaunchpadApplication_Fragment,
  ReferenceApplicationData_MobilityApplication_Fragment,
  ReferenceApplicationData_SalesOfferApplication_Fragment,
  ReferenceApplicationData_StandardApplication_Fragment,
  ReferenceApplicationDataFragment,
  PromoCodeSpecFragment,
  PromoCodeModuleWithPermissionsSpecsFragment,
  PromoCodeModuleSpecsFragment,
  PromoCodeListingDataFragment,
  PromoCodeDataFragment,
  ProductionInsuranceProductDetails_Eazy_Fragment,
  ProductionInsuranceProductDetails_ErgoLookupTable_Fragment,
  ProductionInsuranceProductDetailsFragment,
  ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchase_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment,
  ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment,
  ProductionFinanceProductDetails_LocalLease_Fragment,
  ProductionFinanceProductDetails_LocalLeasePurchase_Fragment,
  ProductionFinanceProductDetails_LocalUcclLeasing_Fragment,
  ProductionFinanceProductDetailsFragment,
  ProductionBankDetailsFragment,
  PrefetchKycConsentsDataFragment,
  PorscheVehicleDataSpecsFragment,
  PorscheVehicleDataFeatureSpecsFragment,
  PorscheVehicleImagesSpecsFragment,
  PorscheRetainModuleWithPermissionsSpecsFragment,
  PorscheRetainModuleSpecsFragment,
  PorschePaymentSettingsSpecFragment,
  PorschePaymentModuleWithPermissionsSpecsFragment,
  PorschePaymentModuleSpecsFragment,
  PorscheMasterDataModuleWithPermissionsSpecsFragment,
  PorscheMasterDataModuleSpecsFragment,
  PorscheIdSettingSpecFragment,
  PorscheIdModuleWithPermissionsSpecsFragment,
  PorscheIdModuleSpecsFragment,
  PermissionSpecsFragment,
  PeriodDataFragment,
  PaymentSettingsDetailsFragment,
  PayGatePaymentSettingsSpecFragment,
  PayGatePaymentModuleWithPermissionsSpecsFragment,
  PayGatePaymentModuleSpecsFragment,
  PathScriptSpecsFragment,
  PackageTypeSpecs_PackageTypeWithDescription_Fragment,
  PackageTypeSpecs_PackageTypeWithPrice_Fragment,
  PackageTypeSpecsFragment,
  PackageSettingsSpecsFragment,
  PackageBlockSpecsFragment,
  OptionsBlockSpecsFragment,
  OptionSettingDetails_ComboOptionSettings_Fragment,
  OptionSettingDetails_DropdownOptionSettings_Fragment,
  OptionSettingDetails_MultiSelectOptionSettings_Fragment,
  OptionSettingDetails_SingleSelectOptionSettings_Fragment,
  OptionSettingDetailsFragment,
  OidcModuleSpecsFragment,
  NzFeesDealerMarketDataFragment,
  NearbyDealersDataFragment,
  NamirialSigningModuleWithPermissionsSpecsFragment,
  NamirialSigningModuleSpecsFragment,
  NamirialSigningDataFragment,
  NamirialSettingsSpecFragment,
  MyInfoSettingsListFragment,
  MyInfoSettingSpecFragment,
  MyInfoModuleWithPermissionsSpecsFragment,
  MyInfoModuleSpecsFragment,
  ModulesOptionsData_AdyenPaymentModule_Fragment,
  ModulesOptionsData_AppointmentModule_Fragment,
  ModulesOptionsData_AutoplayModule_Fragment,
  ModulesOptionsData_BankModule_Fragment,
  ModulesOptionsData_BasicSigningModule_Fragment,
  ModulesOptionsData_CapModule_Fragment,
  ModulesOptionsData_ConfiguratorModule_Fragment,
  ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment,
  ModulesOptionsData_CtsModule_Fragment,
  ModulesOptionsData_DocusignModule_Fragment,
  ModulesOptionsData_EventApplicationModule_Fragment,
  ModulesOptionsData_FinderApplicationPrivateModule_Fragment,
  ModulesOptionsData_FinderApplicationPublicModule_Fragment,
  ModulesOptionsData_FinderVehicleManagementModule_Fragment,
  ModulesOptionsData_FiservPaymentModule_Fragment,
  ModulesOptionsData_GiftVoucherModule_Fragment,
  ModulesOptionsData_InsuranceModule_Fragment,
  ModulesOptionsData_LabelsModule_Fragment,
  ModulesOptionsData_LaunchPadModule_Fragment,
  ModulesOptionsData_LocalCustomerManagementModule_Fragment,
  ModulesOptionsData_MaintenanceModule_Fragment,
  ModulesOptionsData_MarketingModule_Fragment,
  ModulesOptionsData_MobilityModule_Fragment,
  ModulesOptionsData_MyInfoModule_Fragment,
  ModulesOptionsData_NamirialSigningModule_Fragment,
  ModulesOptionsData_OidcModule_Fragment,
  ModulesOptionsData_PayGatePaymentModule_Fragment,
  ModulesOptionsData_PorscheIdModule_Fragment,
  ModulesOptionsData_PorscheMasterDataModule_Fragment,
  ModulesOptionsData_PorschePaymentModule_Fragment,
  ModulesOptionsData_PorscheRetainModule_Fragment,
  ModulesOptionsData_PromoCodeModule_Fragment,
  ModulesOptionsData_SalesControlBoardModule_Fragment,
  ModulesOptionsData_SalesOfferModule_Fragment,
  ModulesOptionsData_SimpleVehicleManagementModule_Fragment,
  ModulesOptionsData_StandardApplicationModule_Fragment,
  ModulesOptionsData_TradeInModule_Fragment,
  ModulesOptionsData_TtbPaymentModule_Fragment,
  ModulesOptionsData_UserlikeChatbotModule_Fragment,
  ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModulesOptionsData_VisitAppointmentModule_Fragment,
  ModulesOptionsData_WebsiteModule_Fragment,
  ModulesOptionsData_WhatsappLiveChatModule_Fragment,
  ModulesOptionsDataFragment,
  ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment,
  ModulesCompanyTimezoneData_AppointmentModule_Fragment,
  ModulesCompanyTimezoneData_AutoplayModule_Fragment,
  ModulesCompanyTimezoneData_BankModule_Fragment,
  ModulesCompanyTimezoneData_BasicSigningModule_Fragment,
  ModulesCompanyTimezoneData_CapModule_Fragment,
  ModulesCompanyTimezoneData_ConfiguratorModule_Fragment,
  ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment,
  ModulesCompanyTimezoneData_CtsModule_Fragment,
  ModulesCompanyTimezoneData_DocusignModule_Fragment,
  ModulesCompanyTimezoneData_EventApplicationModule_Fragment,
  ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment,
  ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment,
  ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment,
  ModulesCompanyTimezoneData_FiservPaymentModule_Fragment,
  ModulesCompanyTimezoneData_GiftVoucherModule_Fragment,
  ModulesCompanyTimezoneData_InsuranceModule_Fragment,
  ModulesCompanyTimezoneData_LabelsModule_Fragment,
  ModulesCompanyTimezoneData_LaunchPadModule_Fragment,
  ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment,
  ModulesCompanyTimezoneData_MaintenanceModule_Fragment,
  ModulesCompanyTimezoneData_MarketingModule_Fragment,
  ModulesCompanyTimezoneData_MobilityModule_Fragment,
  ModulesCompanyTimezoneData_MyInfoModule_Fragment,
  ModulesCompanyTimezoneData_NamirialSigningModule_Fragment,
  ModulesCompanyTimezoneData_OidcModule_Fragment,
  ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment,
  ModulesCompanyTimezoneData_PorscheIdModule_Fragment,
  ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment,
  ModulesCompanyTimezoneData_PorschePaymentModule_Fragment,
  ModulesCompanyTimezoneData_PorscheRetainModule_Fragment,
  ModulesCompanyTimezoneData_PromoCodeModule_Fragment,
  ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment,
  ModulesCompanyTimezoneData_SalesOfferModule_Fragment,
  ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment,
  ModulesCompanyTimezoneData_StandardApplicationModule_Fragment,
  ModulesCompanyTimezoneData_TradeInModule_Fragment,
  ModulesCompanyTimezoneData_TtbPaymentModule_Fragment,
  ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment,
  ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment,
  ModulesCompanyTimezoneData_WebsiteModule_Fragment,
  ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment,
  ModulesCompanyTimezoneDataFragment,
  ModuleWithScenarios_AdyenPaymentModule_Fragment,
  ModuleWithScenarios_AppointmentModule_Fragment,
  ModuleWithScenarios_AutoplayModule_Fragment,
  ModuleWithScenarios_BankModule_Fragment,
  ModuleWithScenarios_BasicSigningModule_Fragment,
  ModuleWithScenarios_CapModule_Fragment,
  ModuleWithScenarios_ConfiguratorModule_Fragment,
  ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment,
  ModuleWithScenarios_CtsModule_Fragment,
  ModuleWithScenarios_DocusignModule_Fragment,
  ModuleWithScenarios_EventApplicationModule_Fragment,
  ModuleWithScenarios_FinderApplicationPrivateModule_Fragment,
  ModuleWithScenarios_FinderApplicationPublicModule_Fragment,
  ModuleWithScenarios_FinderVehicleManagementModule_Fragment,
  ModuleWithScenarios_FiservPaymentModule_Fragment,
  ModuleWithScenarios_GiftVoucherModule_Fragment,
  ModuleWithScenarios_InsuranceModule_Fragment,
  ModuleWithScenarios_LabelsModule_Fragment,
  ModuleWithScenarios_LaunchPadModule_Fragment,
  ModuleWithScenarios_LocalCustomerManagementModule_Fragment,
  ModuleWithScenarios_MaintenanceModule_Fragment,
  ModuleWithScenarios_MarketingModule_Fragment,
  ModuleWithScenarios_MobilityModule_Fragment,
  ModuleWithScenarios_MyInfoModule_Fragment,
  ModuleWithScenarios_NamirialSigningModule_Fragment,
  ModuleWithScenarios_OidcModule_Fragment,
  ModuleWithScenarios_PayGatePaymentModule_Fragment,
  ModuleWithScenarios_PorscheIdModule_Fragment,
  ModuleWithScenarios_PorscheMasterDataModule_Fragment,
  ModuleWithScenarios_PorschePaymentModule_Fragment,
  ModuleWithScenarios_PorscheRetainModule_Fragment,
  ModuleWithScenarios_PromoCodeModule_Fragment,
  ModuleWithScenarios_SalesControlBoardModule_Fragment,
  ModuleWithScenarios_SalesOfferModule_Fragment,
  ModuleWithScenarios_SimpleVehicleManagementModule_Fragment,
  ModuleWithScenarios_StandardApplicationModule_Fragment,
  ModuleWithScenarios_TradeInModule_Fragment,
  ModuleWithScenarios_TtbPaymentModule_Fragment,
  ModuleWithScenarios_UserlikeChatbotModule_Fragment,
  ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleWithScenarios_VisitAppointmentModule_Fragment,
  ModuleWithScenarios_WebsiteModule_Fragment,
  ModuleWithScenarios_WhatsappLiveChatModule_Fragment,
  ModuleWithScenariosFragment,
  ModuleWithPermissionsSpecs_AdyenPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_AppointmentModule_Fragment,
  ModuleWithPermissionsSpecs_AutoplayModule_Fragment,
  ModuleWithPermissionsSpecs_BankModule_Fragment,
  ModuleWithPermissionsSpecs_BasicSigningModule_Fragment,
  ModuleWithPermissionsSpecs_CapModule_Fragment,
  ModuleWithPermissionsSpecs_ConfiguratorModule_Fragment,
  ModuleWithPermissionsSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleWithPermissionsSpecs_CtsModule_Fragment,
  ModuleWithPermissionsSpecs_DocusignModule_Fragment,
  ModuleWithPermissionsSpecs_EventApplicationModule_Fragment,
  ModuleWithPermissionsSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleWithPermissionsSpecs_FinderApplicationPublicModule_Fragment,
  ModuleWithPermissionsSpecs_FinderVehicleManagementModule_Fragment,
  ModuleWithPermissionsSpecs_FiservPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_GiftVoucherModule_Fragment,
  ModuleWithPermissionsSpecs_InsuranceModule_Fragment,
  ModuleWithPermissionsSpecs_LabelsModule_Fragment,
  ModuleWithPermissionsSpecs_LaunchPadModule_Fragment,
  ModuleWithPermissionsSpecs_LocalCustomerManagementModule_Fragment,
  ModuleWithPermissionsSpecs_MaintenanceModule_Fragment,
  ModuleWithPermissionsSpecs_MarketingModule_Fragment,
  ModuleWithPermissionsSpecs_MobilityModule_Fragment,
  ModuleWithPermissionsSpecs_MyInfoModule_Fragment,
  ModuleWithPermissionsSpecs_NamirialSigningModule_Fragment,
  ModuleWithPermissionsSpecs_OidcModule_Fragment,
  ModuleWithPermissionsSpecs_PayGatePaymentModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheIdModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheMasterDataModule_Fragment,
  ModuleWithPermissionsSpecs_PorschePaymentModule_Fragment,
  ModuleWithPermissionsSpecs_PorscheRetainModule_Fragment,
  ModuleWithPermissionsSpecs_PromoCodeModule_Fragment,
  ModuleWithPermissionsSpecs_SalesControlBoardModule_Fragment,
  ModuleWithPermissionsSpecs_SalesOfferModule_Fragment,
  ModuleWithPermissionsSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleWithPermissionsSpecs_StandardApplicationModule_Fragment,
  ModuleWithPermissionsSpecs_TradeInModule_Fragment,
  ModuleWithPermissionsSpecs_TtbPaymentModule_Fragment,
  ModuleWithPermissionsSpecs_UserlikeChatbotModule_Fragment,
  ModuleWithPermissionsSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleWithPermissionsSpecs_VisitAppointmentModule_Fragment,
  ModuleWithPermissionsSpecs_WebsiteModule_Fragment,
  ModuleWithPermissionsSpecs_WhatsappLiveChatModule_Fragment,
  ModuleWithPermissionsSpecsFragment,
  ModuleVariantDataFragment,
  ModuleSpecs_AdyenPaymentModule_Fragment,
  ModuleSpecs_AppointmentModule_Fragment,
  ModuleSpecs_AutoplayModule_Fragment,
  ModuleSpecs_BankModule_Fragment,
  ModuleSpecs_BasicSigningModule_Fragment,
  ModuleSpecs_CapModule_Fragment,
  ModuleSpecs_ConfiguratorModule_Fragment,
  ModuleSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleSpecs_CtsModule_Fragment,
  ModuleSpecs_DocusignModule_Fragment,
  ModuleSpecs_EventApplicationModule_Fragment,
  ModuleSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleSpecs_FinderApplicationPublicModule_Fragment,
  ModuleSpecs_FinderVehicleManagementModule_Fragment,
  ModuleSpecs_FiservPaymentModule_Fragment,
  ModuleSpecs_GiftVoucherModule_Fragment,
  ModuleSpecs_InsuranceModule_Fragment,
  ModuleSpecs_LabelsModule_Fragment,
  ModuleSpecs_LaunchPadModule_Fragment,
  ModuleSpecs_LocalCustomerManagementModule_Fragment,
  ModuleSpecs_MaintenanceModule_Fragment,
  ModuleSpecs_MarketingModule_Fragment,
  ModuleSpecs_MobilityModule_Fragment,
  ModuleSpecs_MyInfoModule_Fragment,
  ModuleSpecs_NamirialSigningModule_Fragment,
  ModuleSpecs_OidcModule_Fragment,
  ModuleSpecs_PayGatePaymentModule_Fragment,
  ModuleSpecs_PorscheIdModule_Fragment,
  ModuleSpecs_PorscheMasterDataModule_Fragment,
  ModuleSpecs_PorschePaymentModule_Fragment,
  ModuleSpecs_PorscheRetainModule_Fragment,
  ModuleSpecs_PromoCodeModule_Fragment,
  ModuleSpecs_SalesControlBoardModule_Fragment,
  ModuleSpecs_SalesOfferModule_Fragment,
  ModuleSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleSpecs_StandardApplicationModule_Fragment,
  ModuleSpecs_TradeInModule_Fragment,
  ModuleSpecs_TtbPaymentModule_Fragment,
  ModuleSpecs_UserlikeChatbotModule_Fragment,
  ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleSpecs_VisitAppointmentModule_Fragment,
  ModuleSpecs_WebsiteModule_Fragment,
  ModuleSpecs_WhatsappLiveChatModule_Fragment,
  ModuleSpecsFragment,
  ModuleListData_AdyenPaymentModule_Fragment,
  ModuleListData_AppointmentModule_Fragment,
  ModuleListData_AutoplayModule_Fragment,
  ModuleListData_BankModule_Fragment,
  ModuleListData_BasicSigningModule_Fragment,
  ModuleListData_CapModule_Fragment,
  ModuleListData_ConfiguratorModule_Fragment,
  ModuleListData_ConsentsAndDeclarationsModule_Fragment,
  ModuleListData_CtsModule_Fragment,
  ModuleListData_DocusignModule_Fragment,
  ModuleListData_EventApplicationModule_Fragment,
  ModuleListData_FinderApplicationPrivateModule_Fragment,
  ModuleListData_FinderApplicationPublicModule_Fragment,
  ModuleListData_FinderVehicleManagementModule_Fragment,
  ModuleListData_FiservPaymentModule_Fragment,
  ModuleListData_GiftVoucherModule_Fragment,
  ModuleListData_InsuranceModule_Fragment,
  ModuleListData_LabelsModule_Fragment,
  ModuleListData_LaunchPadModule_Fragment,
  ModuleListData_LocalCustomerManagementModule_Fragment,
  ModuleListData_MaintenanceModule_Fragment,
  ModuleListData_MarketingModule_Fragment,
  ModuleListData_MobilityModule_Fragment,
  ModuleListData_MyInfoModule_Fragment,
  ModuleListData_NamirialSigningModule_Fragment,
  ModuleListData_OidcModule_Fragment,
  ModuleListData_PayGatePaymentModule_Fragment,
  ModuleListData_PorscheIdModule_Fragment,
  ModuleListData_PorscheMasterDataModule_Fragment,
  ModuleListData_PorschePaymentModule_Fragment,
  ModuleListData_PorscheRetainModule_Fragment,
  ModuleListData_PromoCodeModule_Fragment,
  ModuleListData_SalesControlBoardModule_Fragment,
  ModuleListData_SalesOfferModule_Fragment,
  ModuleListData_SimpleVehicleManagementModule_Fragment,
  ModuleListData_StandardApplicationModule_Fragment,
  ModuleListData_TradeInModule_Fragment,
  ModuleListData_TtbPaymentModule_Fragment,
  ModuleListData_UserlikeChatbotModule_Fragment,
  ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleListData_VisitAppointmentModule_Fragment,
  ModuleListData_WebsiteModule_Fragment,
  ModuleListData_WhatsappLiveChatModule_Fragment,
  ModuleListDataFragment,
  ModuleInDealerSpecs_AdyenPaymentModule_Fragment,
  ModuleInDealerSpecs_AppointmentModule_Fragment,
  ModuleInDealerSpecs_AutoplayModule_Fragment,
  ModuleInDealerSpecs_BankModule_Fragment,
  ModuleInDealerSpecs_BasicSigningModule_Fragment,
  ModuleInDealerSpecs_CapModule_Fragment,
  ModuleInDealerSpecs_ConfiguratorModule_Fragment,
  ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment,
  ModuleInDealerSpecs_CtsModule_Fragment,
  ModuleInDealerSpecs_DocusignModule_Fragment,
  ModuleInDealerSpecs_EventApplicationModule_Fragment,
  ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment,
  ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment,
  ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment,
  ModuleInDealerSpecs_FiservPaymentModule_Fragment,
  ModuleInDealerSpecs_GiftVoucherModule_Fragment,
  ModuleInDealerSpecs_InsuranceModule_Fragment,
  ModuleInDealerSpecs_LabelsModule_Fragment,
  ModuleInDealerSpecs_LaunchPadModule_Fragment,
  ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment,
  ModuleInDealerSpecs_MaintenanceModule_Fragment,
  ModuleInDealerSpecs_MarketingModule_Fragment,
  ModuleInDealerSpecs_MobilityModule_Fragment,
  ModuleInDealerSpecs_MyInfoModule_Fragment,
  ModuleInDealerSpecs_NamirialSigningModule_Fragment,
  ModuleInDealerSpecs_OidcModule_Fragment,
  ModuleInDealerSpecs_PayGatePaymentModule_Fragment,
  ModuleInDealerSpecs_PorscheIdModule_Fragment,
  ModuleInDealerSpecs_PorscheMasterDataModule_Fragment,
  ModuleInDealerSpecs_PorschePaymentModule_Fragment,
  ModuleInDealerSpecs_PorscheRetainModule_Fragment,
  ModuleInDealerSpecs_PromoCodeModule_Fragment,
  ModuleInDealerSpecs_SalesControlBoardModule_Fragment,
  ModuleInDealerSpecs_SalesOfferModule_Fragment,
  ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment,
  ModuleInDealerSpecs_StandardApplicationModule_Fragment,
  ModuleInDealerSpecs_TradeInModule_Fragment,
  ModuleInDealerSpecs_TtbPaymentModule_Fragment,
  ModuleInDealerSpecs_UserlikeChatbotModule_Fragment,
  ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  ModuleInDealerSpecs_VisitAppointmentModule_Fragment,
  ModuleInDealerSpecs_WebsiteModule_Fragment,
  ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment,
  ModuleInDealerSpecsFragment,
  ModuleDisclaimersDataFragment,
  ModelConfiguratorWithPermissionsSpecsFragment,
  ModelConfiguratorSpecsFragment,
  ModelConfiguratorListDataFragment,
  ModelConfiguratorDetailsFragment,
  MobilityStockPublicList_ConfiguratorStockInventory_Fragment,
  MobilityStockPublicList_MobilityStockInventory_Fragment,
  MobilityStockPublicListFragment,
  MobilityStockGiftVoucherDataFragment,
  MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment,
  MobilitySnapshotData_MobilityAddonSnapshot_Fragment,
  MobilitySnapshotDataFragment,
  MobilitySigningSettingSpecsFragment,
  MobilityOperatorEmailContentDataFragment,
  MobilityModuleWithPermissionsSpecsFragment,
  MobilityModuleSpecsFragment,
  MobilityModuleInDealerSpecsFragment,
  MobilityModuleGiftVoucherDataFragment,
  MobilityModuleEmailScenarioContentSpecsFragment,
  MobilityLocationDataFragment,
  MobilityListData_MobilityAdditionalInfo_Fragment,
  MobilityListData_MobilityAddon_Fragment,
  MobilityListDataFragment,
  MobilityLeadDataFragment,
  MobilityInventorySpecsFragment,
  MobilityInventoryPublicSpecsFragment,
  MobilityInventoryOptionFragment,
  MobilityHomeDeliveryDataFragment,
  MobilityEmailContentDataFragment,
  MobilityData_MobilityAdditionalInfo_Fragment,
  MobilityData_MobilityAddon_Fragment,
  MobilityDataFragment,
  MobilityCustomerEmailContentDataFragment,
  MobilityBookingLocationPickupDataFragment,
  MobilityBookingLocationHomeDataFragment,
  MobilityBookingDetailsDataFragment,
  MobilityApplicationSpecsFragment,
  MobilityApplicationModuleDataFragment,
  MobilityApplicationExpiredFragment,
  MobilityApplicationEntrypointSpecsFragment,
  MobilityApplicationEntrypointContextDataFragment,
  MobilityApplicationDataFragment,
  MobilityAddonDataFragment,
  MobilityAdditionalInfoDataFragment,
  MenuItemSpecs_MenuCustomPathItem_Fragment,
  MenuItemSpecs_MenuEndpointItem_Fragment,
  MenuItemSpecs_MenuLogoutActionItem_Fragment,
  MenuItemSpecsFragment,
  MatrixSpecsFragment,
  MatrixDataFragment,
  MarketingPlatformsAgreedSpecsFragment,
  MarketingPlatformSpecsFragment,
  MarketingModuleWithPermissionsSpecsFragment,
  MarketingModuleSpecsFragment,
  MaintenanceUpdateFragment,
  MaintenanceModuleWithPermissionsSpecsFragment,
  MaintenanceModuleSpecsFragment,
  MainDetailsSalesOfferSpecsFragment,
  LocalVariantsListDataFragment,
  LocalVariantWithPermissionsSpecsFragment,
  LocalVariantSpecsFragment,
  LocalVariantPublicSpecsFragment,
  LocalVariantProductionListSpecsFragment,
  LocalVariantCalculatorSpecsFragment,
  LocalUcclLeasingOnlyDetailsFragment,
  LocalUcclLeasingDetailsFragment,
  LocalModelsListDataFragment,
  LocalModelWithPermissionsSpecsFragment,
  LocalModelSpecsFragment,
  LocalModelPublicSpecsFragment,
  LocalModelCalculatorSpecsFragment,
  LocalMakesListDataFragment,
  LocalMakeWithPermissionsSpecsFragment,
  LocalMakeSpecsFragment,
  LocalMakePublicSpecsFragment,
  LocalMakeCalculatorSpecsFragment,
  LocalLeasePurchaseDetailsFragment,
  LocalLeaseDetailsFragment,
  LocalHirePurchaseWithBalloonGfvDetailsFragment,
  LocalHirePurchaseWithBalloonDetailsFragment,
  LocalHirePurchaseDetailsFragment,
  LocalFittedOptionsSpecsFragment,
  LocalDeferredPrincipalDetailsFragment,
  LocalCustomerManagementModuleWithPermissionsSpecsFragment,
  LocalCustomerManagementModuleSpecsFragment,
  LocalCustomerManagementModuleKycFieldSpecsFragment,
  LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment,
  LocalCustomerFieldData_LocalCustomerDateField_Fragment,
  LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment,
  LocalCustomerFieldData_LocalCustomerNumberField_Fragment,
  LocalCustomerFieldData_LocalCustomerPhoneField_Fragment,
  LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment,
  LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment,
  LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment,
  LocalCustomerFieldData_LocalCustomerStringField_Fragment,
  LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment,
  LocalCustomerFieldData_LocalCustomerUploadsField_Fragment,
  LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment,
  LocalCustomerFieldDataFragment,
  LocalCustomerDataFragment,
  LoanSettingsDetailsFragment,
  ListGiftVoucherDataFragment,
  LeaseSettingsDetailsFragment,
  LeadListEndpointSpecsFragment,
  LeadListEndpointContextDataFragment,
  LeadListData_ConfiguratorLead_Fragment,
  LeadListData_EventLead_Fragment,
  LeadListData_FinderLead_Fragment,
  LeadListData_LaunchpadLead_Fragment,
  LeadListData_MobilityLead_Fragment,
  LeadListData_StandardLead_Fragment,
  LeadListDataFragment,
  LeadInCustomerList_ConfiguratorLead_Fragment,
  LeadInCustomerList_EventLead_Fragment,
  LeadInCustomerList_FinderLead_Fragment,
  LeadInCustomerList_LaunchpadLead_Fragment,
  LeadInCustomerList_MobilityLead_Fragment,
  LeadInCustomerList_StandardLead_Fragment,
  LeadInCustomerListFragment,
  LeadData_ConfiguratorLead_Fragment,
  LeadData_EventLead_Fragment,
  LeadData_FinderLead_Fragment,
  LeadData_LaunchpadLead_Fragment,
  LeadData_MobilityLead_Fragment,
  LeadData_StandardLead_Fragment,
  LeadDataFragment,
  LaunchpadModuleSpecsForApplicationFragment,
  LaunchpadModuleDebugJourneyFragment,
  LaunchpadLeadDataFragment,
  LaunchpadApplicationSpecFragment,
  LaunchpadApplicationModuleDebugJourneyFragment,
  LaunchpadApplicationDataFragment,
  LaunchpadApplicationConfigurationDataFragment,
  LaunchPadModuleWithPermissionsSpecsFragment,
  LaunchPadModuleSpecsFragment,
  LaunchPadModuleInDealerSpecsFragment,
  LaunchPadApplicationEntrypointSpecsFragment,
  LaunchPadApplicationEntrypointContextDataFragment,
  LanguagePackSpecsFragment,
  LanguagePackOptionDataFragment,
  LanguagePackListDataFragment,
  LanguagePackContextDataFragment,
  LabelsPublicDataFragment,
  LabelsModuleWithPermissionsSpecsFragment,
  LabelsModuleSpecsFragment,
  LabelsDataFragment,
  KycPresetsSpecFragment,
  KycPresetsOptionsDataFragment,
  KycFieldSpecsFragment,
  KycExtraSettingsSpecsFragment,
  JourneyEventDataFragment,
  JourneyDraftFlowFragment,
  InventoryVariantOptionSpecsFragment,
  InventorySubModelOptionSpecsFragment,
  InventoryOptionSpecsFragment,
  InventoryModuleOptionSpecsFragment,
  InventoryModelOptionSpecsFragment,
  InventoryListData_ConfiguratorInventory_Fragment,
  InventoryListData_MobilityInventory_Fragment,
  InventoryListDataFragment,
  InventoryDetailsWithPermissionsData_ConfiguratorInventory_Fragment,
  InventoryDetailsWithPermissionsData_MobilityInventory_Fragment,
  InventoryDetailsWithPermissionsDataFragment,
  InventoryDetailsPublicData_ConfiguratorInventory_Fragment,
  InventoryDetailsPublicData_MobilityInventory_Fragment,
  InventoryDetailsPublicDataFragment,
  InventoryDetailsData_ConfiguratorInventory_Fragment,
  InventoryDetailsData_MobilityInventory_Fragment,
  InventoryDetailsDataFragment,
  InterestRateSettingsDetails_InterestRateFixedSettings_Fragment,
  InterestRateSettingsDetails_InterestRateRangeSettings_Fragment,
  InterestRateSettingsDetails_InterestRateTableSettings_Fragment,
  InterestRateSettingsDetailsFragment,
  InsurerListDataFragment,
  InsurerIntegrationData_EazyInsurerIntegration_Fragment,
  InsurerIntegrationData_EmailInsurerIntegration_Fragment,
  InsurerIntegrationDataFragment,
  InsurerEntrypointContextDataFragment,
  InsurerDetailsDataFragment,
  InsuranceSalesOfferSpecsFragment,
  InsuranceProductListData_Eazy_Fragment,
  InsuranceProductListData_ErgoLookupTable_Fragment,
  InsuranceProductListDataFragment,
  InsuranceProductDetailsWithPermissionsData_Eazy_Fragment,
  InsuranceProductDetailsWithPermissionsData_ErgoLookupTable_Fragment,
  InsuranceProductDetailsWithPermissionsDataFragment,
  InsuranceProductDetailsData_Eazy_Fragment,
  InsuranceProductDetailsData_ErgoLookupTable_Fragment,
  InsuranceProductDetailsDataFragment,
  InsuranceProductDetails_Eazy_Fragment,
  InsuranceProductDetails_ErgoLookupTable_Fragment,
  InsuranceProductDetailsFragment,
  InsuranceModuleWithPermissionsSpecsFragment,
  InsuranceModuleSpecsFragment,
  ImportExcelSpecsFragment,
  ImageWebPageBlockDataFragment,
  ImageDescriptionWebPageBlockDataFragment,
  GuarantorDataFragment,
  GiftVoucherModuleWithPermissionsSpecsFragment,
  GiftVoucherModuleSpecsFragment,
  GiftVoucherModuleInDealerSpecsFragment,
  GiftVoucherModuleEmailDataFragment,
  GiftVoucherModuleEmailContentCustomerSpecsFragment,
  GiftVoucherModuleEmailContentSalesPersonSpecsFragment,
  GiftVoucherModuleEmailContentsSpecsFragment,
  GiftVoucherJourneyContextDataFragment,
  GiftVoucherDraftFlowDataFragment,
  GiftVoucherDataFragment,
  GiftVoucherSpecsFragment,
  GiftVoucherCodeDataFragment,
  GiftPromoTypeDataFragment,
  GetModulesList_AdyenPaymentModule_Fragment,
  GetModulesList_AppointmentModule_Fragment,
  GetModulesList_AutoplayModule_Fragment,
  GetModulesList_BankModule_Fragment,
  GetModulesList_BasicSigningModule_Fragment,
  GetModulesList_CapModule_Fragment,
  GetModulesList_ConfiguratorModule_Fragment,
  GetModulesList_ConsentsAndDeclarationsModule_Fragment,
  GetModulesList_CtsModule_Fragment,
  GetModulesList_DocusignModule_Fragment,
  GetModulesList_EventApplicationModule_Fragment,
  GetModulesList_FinderApplicationPrivateModule_Fragment,
  GetModulesList_FinderApplicationPublicModule_Fragment,
  GetModulesList_FinderVehicleManagementModule_Fragment,
  GetModulesList_FiservPaymentModule_Fragment,
  GetModulesList_GiftVoucherModule_Fragment,
  GetModulesList_InsuranceModule_Fragment,
  GetModulesList_LabelsModule_Fragment,
  GetModulesList_LaunchPadModule_Fragment,
  GetModulesList_LocalCustomerManagementModule_Fragment,
  GetModulesList_MaintenanceModule_Fragment,
  GetModulesList_MarketingModule_Fragment,
  GetModulesList_MobilityModule_Fragment,
  GetModulesList_MyInfoModule_Fragment,
  GetModulesList_NamirialSigningModule_Fragment,
  GetModulesList_OidcModule_Fragment,
  GetModulesList_PayGatePaymentModule_Fragment,
  GetModulesList_PorscheIdModule_Fragment,
  GetModulesList_PorscheMasterDataModule_Fragment,
  GetModulesList_PorschePaymentModule_Fragment,
  GetModulesList_PorscheRetainModule_Fragment,
  GetModulesList_PromoCodeModule_Fragment,
  GetModulesList_SalesControlBoardModule_Fragment,
  GetModulesList_SalesOfferModule_Fragment,
  GetModulesList_SimpleVehicleManagementModule_Fragment,
  GetModulesList_StandardApplicationModule_Fragment,
  GetModulesList_TradeInModule_Fragment,
  GetModulesList_TtbPaymentModule_Fragment,
  GetModulesList_UserlikeChatbotModule_Fragment,
  GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  GetModulesList_VisitAppointmentModule_Fragment,
  GetModulesList_WebsiteModule_Fragment,
  GetModulesList_WhatsappLiveChatModule_Fragment,
  GetModulesListFragment,
  FlexibleDiscountDataFragment,
  FiservPaymentSettingsSpecFragment,
  FiservPaymentModuleWithPermissionsSpecsFragment,
  FiservPaymentModuleSpecsFragment,
  FinderVehiclesListDataFragment,
  FinderVehicleWithPermissionsSpecsFragment,
  FinderVehicleSpecsFragment,
  FinderVehicleSelectionDataFragment,
  FinderVehicleManagementModuleWithPermissionsSpecsFragment,
  FinderVehicleManagementModuleSpecsFragment,
  FinderVehicleDataFragment,
  FinderVehicleCalculatorDataFragment,
  FinderLeadDataFragment,
  FinderConfigurationDataFragment,
  FinderApplicationSpecsFragment,
  FinderApplicationPublicModuleWithPermissionsSpecsFragment,
  FinderApplicationPublicModuleSpecsForApplicationFragment,
  FinderApplicationPublicModuleSpecsFragment,
  FinderApplicationPublicModuleInDealerSpecsFragment,
  FinderApplicationPublicModuleDebugJourneyFragment,
  FinderApplicationPublicModuleDataFragment,
  FinderApplicationPublicAccessEntrypointSpecsFragment,
  FinderApplicationPublicAccessEntrypointContextDataFragment,
  FinderApplicationPrivateModuleWithPermissionsSpecsFragment,
  FinderApplicationPrivateModuleSpecsForApplicationFragment,
  FinderApplicationPrivateModuleSpecsFragment,
  FinderApplicationPrivateModuleInDealerSpecsFragment,
  FinderApplicationPrivateModuleDebugJourneyFragment,
  FinderApplicationPrivateModuleDataFragment,
  FinderApplicationModuleEmailContentSpecsFragment,
  FinderApplicationEntrypointSpecsFragment,
  FinderApplicationEntrypointContextDataFragment,
  FinderApplicationDataFragment,
  FinanceSalesOfferSpecsFragment,
  FinanceProductListData_LocalDeferredPrincipal_Fragment,
  FinanceProductListData_LocalHirePurchase_Fragment,
  FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductListData_LocalLease_Fragment,
  FinanceProductListData_LocalLeasePurchase_Fragment,
  FinanceProductListData_LocalUcclLeasing_Fragment,
  FinanceProductListDataFragment,
  FinanceProductDetailsWithPermissionsData_LocalDeferredPrincipal_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchase_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalLease_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalLeasePurchase_Fragment,
  FinanceProductDetailsWithPermissionsData_LocalUcclLeasing_Fragment,
  FinanceProductDetailsWithPermissionsDataFragment,
  FinanceProductDetailsData_LocalDeferredPrincipal_Fragment,
  FinanceProductDetailsData_LocalHirePurchase_Fragment,
  FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetailsData_LocalLease_Fragment,
  FinanceProductDetailsData_LocalLeasePurchase_Fragment,
  FinanceProductDetailsData_LocalUcclLeasing_Fragment,
  FinanceProductDetailsDataFragment,
  FinanceProductDetails_LocalDeferredPrincipal_Fragment,
  FinanceProductDetails_LocalHirePurchase_Fragment,
  FinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment,
  FinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment,
  FinanceProductDetails_LocalLease_Fragment,
  FinanceProductDetails_LocalLeasePurchase_Fragment,
  FinanceProductDetails_LocalUcclLeasing_Fragment,
  FinanceProductDetailsFragment,
  FeatureValueDataFragment,
  EventModuleData_AdyenPaymentModule_Fragment,
  EventModuleData_AppointmentModule_Fragment,
  EventModuleData_AutoplayModule_Fragment,
  EventModuleData_BankModule_Fragment,
  EventModuleData_BasicSigningModule_Fragment,
  EventModuleData_CapModule_Fragment,
  EventModuleData_ConfiguratorModule_Fragment,
  EventModuleData_ConsentsAndDeclarationsModule_Fragment,
  EventModuleData_CtsModule_Fragment,
  EventModuleData_DocusignModule_Fragment,
  EventModuleData_EventApplicationModule_Fragment,
  EventModuleData_FinderApplicationPrivateModule_Fragment,
  EventModuleData_FinderApplicationPublicModule_Fragment,
  EventModuleData_FinderVehicleManagementModule_Fragment,
  EventModuleData_FiservPaymentModule_Fragment,
  EventModuleData_GiftVoucherModule_Fragment,
  EventModuleData_InsuranceModule_Fragment,
  EventModuleData_LabelsModule_Fragment,
  EventModuleData_LaunchPadModule_Fragment,
  EventModuleData_LocalCustomerManagementModule_Fragment,
  EventModuleData_MaintenanceModule_Fragment,
  EventModuleData_MarketingModule_Fragment,
  EventModuleData_MobilityModule_Fragment,
  EventModuleData_MyInfoModule_Fragment,
  EventModuleData_NamirialSigningModule_Fragment,
  EventModuleData_OidcModule_Fragment,
  EventModuleData_PayGatePaymentModule_Fragment,
  EventModuleData_PorscheIdModule_Fragment,
  EventModuleData_PorscheMasterDataModule_Fragment,
  EventModuleData_PorschePaymentModule_Fragment,
  EventModuleData_PorscheRetainModule_Fragment,
  EventModuleData_PromoCodeModule_Fragment,
  EventModuleData_SalesControlBoardModule_Fragment,
  EventModuleData_SalesOfferModule_Fragment,
  EventModuleData_SimpleVehicleManagementModule_Fragment,
  EventModuleData_StandardApplicationModule_Fragment,
  EventModuleData_TradeInModule_Fragment,
  EventModuleData_TtbPaymentModule_Fragment,
  EventModuleData_UserlikeChatbotModule_Fragment,
  EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
  EventModuleData_VisitAppointmentModule_Fragment,
  EventModuleData_WebsiteModule_Fragment,
  EventModuleData_WhatsappLiveChatModule_Fragment,
  EventModuleDataFragment,
  EventListDataFragment,
  EventLeadDataFragment,
  EventDataFragment,
  EventApplicationSpecFragment,
  EventApplicationModuleWithPermissionsSpecsFragment,
  EventApplicationModuleSpecsForApplicationFragment,
  EventApplicationModuleSpecsFragment,
  EventApplicationModuleInDealerSpecsFragment,
  EventApplicationModuleEmailContentSpecsFragment,
  EventEmailContentSpecsFragment,
  EventApplicationModuleDebugJourneyFragment,
  EventApplicationEntrypointSpecsFragment,
  EventApplicationEntrypointContextDataFragment,
  EventApplicationDataFragment,
  EventApplicationConfigurationDataFragment,
  ErgoLookupTableSettingsDetailsFragment,
  ErgoLookupTableDetailsFragment,
  EntrypointFinderApplicationPublicModuleFragment,
  EntrypointFinderApplicationPrivateModuleFragment,
  EndpointListSpecs_ApplicationListEndpoint_Fragment,
  EndpointListSpecs_ConfiguratorApplicationEntrypoint_Fragment,
  EndpointListSpecs_CustomerListEndpoint_Fragment,
  EndpointListSpecs_DummyPrivatePageEndpoint_Fragment,
  EndpointListSpecs_DummyWelcomePageEndpoint_Fragment,
  EndpointListSpecs_EventApplicationEntrypoint_Fragment,
  EndpointListSpecs_FinderApplicationEntrypoint_Fragment,
  EndpointListSpecs_FinderApplicationPublicAccessEntrypoint_Fragment,
  EndpointListSpecs_LaunchPadApplicationEntrypoint_Fragment,
  EndpointListSpecs_LeadListEndpoint_Fragment,
  EndpointListSpecs_MobilityApplicationEntrypoint_Fragment,
  EndpointListSpecs_StandardApplicationEntrypoint_Fragment,
  EndpointListSpecs_StandardApplicationPublicAccessEntrypoint_Fragment,
  EndpointListSpecs_WebPageEndpoint_Fragment,
  EndpointListSpecsFragment,
  EndpointContextData_ApplicationListEndpoint_Fragment,
  EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment,
  EndpointContextData_CustomerListEndpoint_Fragment,
  EndpointContextData_DummyPrivatePageEndpoint_Fragment,
  EndpointContextData_DummyWelcomePageEndpoint_Fragment,
  EndpointContextData_EventApplicationEntrypoint_Fragment,
  EndpointContextData_FinderApplicationEntrypoint_Fragment,
  EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment,
  EndpointContextData_LaunchPadApplicationEntrypoint_Fragment,
  EndpointContextData_LeadListEndpoint_Fragment,
  EndpointContextData_MobilityApplicationEntrypoint_Fragment,
  EndpointContextData_StandardApplicationEntrypoint_Fragment,
  EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment,
  EndpointContextData_WebPageEndpoint_Fragment,
  EndpointContextDataFragment,
  EdmSocialMediaDataFragment,
  EdmEmailFooterPublicDataFragment,
  EazyDetailsFragment,
  DummyWelcomePageEndpointSpecsFragment,
  DummyPrivatePageEndpointSpecsFragment,
  DummyPrivatePageEndpointContextDataFragment,
  DraftFlowConfigurationSpecFragment,
  DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment,
  DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment,
  DownPaymentSettingsDetailsFragment,
  DocusignSettingDataFragment,
  DocusignModuleSpecsWithPermissionSpecsFragment,
  DocusignModuleSpecsFragment,
  DiscountPromoTypeDataFragment,
  DiscountCodeData_GiftVoucher_Fragment,
  DiscountCodeData_PromoCode_Fragment,
  DiscountCodeDataFragment,
  DepositSettingsDetails_DepositRangeSettings_Fragment,
  DepositSettingsDetails_DepositTableSettings_Fragment,
  DepositSettingsDetailsFragment,
  DepositSalesOfferSpecsFragment,
  DepositAmountDataFragment,
  DebugJourneyDataFragment,
  DealershipSettingSpecData_DealershipMyInfoSetting_Fragment,
  DealershipSettingSpecData_DealershipPaymentSetting_Fragment,
  DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment,
  DealershipSettingSpecDataFragment,
  DealershipSettingDetailsData_DealershipMyInfoSetting_Fragment,
  DealershipSettingDetailsData_DealershipPaymentSetting_Fragment,
  DealershipSettingDetailsData_DealershipPublicSalesPerson_Fragment,
  DealershipSettingDetailsDataFragment,
  DealerWithPermissionsFragmentFragment,
  DealerVehiclesSpecsFragment,
  DealerUploadedFileWithPreviewDataFragment,
  DealerTranslatedStringSettingDataFragment,
  DealerSocialMediaFragmentFragment,
  DealerPriceDisclaimerDataFragment,
  DealerDisclaimersConfiguratorDataFragment,
  DealerOptionsDataFragment,
  DealerMarketDataFragment,
  DealerListDataFragment,
  DealerJourneyDataFragment,
  DealerIntegrationDetailsFragmentFragment,
  DealerIntDataFragment,
  DealerObjectIdDataFragment,
  DealerFloatDataFragment,
  DealerInsuranceProductsSpecsFragment,
  DealerFragmentFragment,
  DealerFinanceProductsSpecsFragment,
  DealerDisclaimersFragmentFragment,
  DealerContactFragmentFragment,
  DealerBooleanSettingDataFragment,
  DealerBookingCodeSpecsFragment,
  DealerBookingCodeDataFragment,
  DealerApplicationFragmentFragment,
  DateUnitDataFragment,
  CustomizedFieldDataFragment,
  CustomerSpecs_CorporateCustomer_Fragment,
  CustomerSpecs_Guarantor_Fragment,
  CustomerSpecs_LocalCustomer_Fragment,
  CustomerSpecsFragment,
  CustomerListStandardApplicationFragment,
  CustomerListSpecs_CorporateCustomer_Fragment,
  CustomerListSpecs_Guarantor_Fragment,
  CustomerListSpecs_LocalCustomer_Fragment,
  CustomerListSpecsFragment,
  CustomerListMobilityApplicationFragment,
  CustomerListLaunchpadApplicationFragment,
  CustomerListFinderApplicationFragment,
  CustomerListEventApplicationFragment,
  CustomerListEndpointSpecsFragment,
  CustomerListEndpointContextDataFragment,
  CustomerListConfiguratorApplicationFragment,
  CustomerDetailsStandardApplicationFragment,
  CustomerDetailsSalesOfferApplicationFragment,
  CustomerDetailsMobilityApplicationFragment,
  CustomerDetailsLaunchpadApplicationFragment,
  CustomerDetailsFinderApplicationFragment,
  CustomerDetailsEventApplicationFragment,
  CustomerDetailsConfiguratorApplicationFragment,
  CustomerDetailSpecs_CorporateCustomer_Fragment,
  CustomerDetailSpecs_Guarantor_Fragment,
  CustomerDetailSpecs_LocalCustomer_Fragment,
  CustomerDetailSpecsFragment,
  CustomWebPageBlockDataFragment,
  CustomTestDriveBookingSlotsDataFragment,
  CurrentUserDataFragment,
  CurrentUserCompaniesDataFragment,
  CtsModuleWithPermissionsSpecsFragment,
  CtsModuleSpecsFragment,
  CtsModuleSettingDataFragment,
  CounterSettingsSpecsFragment,
  CorporateCustomerDataFragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecs_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsWithPermissionsSpecsFragment,
  ConsentsAndDeclarationsSpecs_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecs_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsSpecsFragment,
  ConsentsAndDeclarationsModuleWithPermissionsSpecsFragment,
  ConsentsAndDeclarationsModuleSpecsFragment,
  ConsentsAndDeclarationsListData_CheckboxConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_GroupConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_MarketingConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListData_TextConsentsAndDeclarations_Fragment,
  ConsentsAndDeclarationsListDataFragment,
  ConfiguratorModuleWithPermissionsSpecsFragment,
  ConfiguratorModuleSpecsForApplicationFragment,
  ConfiguratorModuleSpecsFragment,
  ConfiguratorModuleInDealerSpecsFragment,
  ConfiguratorModuleEmailContentSpecsFragment,
  ConfiguratorLeadDataFragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment,
  ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment,
  ConfiguratorJourneyBlocksDataFragment,
  ConfiguratorInventorySpecsFragment,
  ConfiguratorInventoryPublicSpecsFragment,
  ConfiguratorDetails_ModelConfigurator_Fragment,
  ConfiguratorDetails_VariantConfigurator_Fragment,
  ConfiguratorDetailsFragment,
  ConfiguratorApplicationSpecFragment,
  ConfiguratorApplicationModuleDebugJourneyFragment,
  ConfiguratorApplicationModuleDataFragment,
  ConfiguratorApplicationEntrypointSpecsFragment,
  ConfiguratorApplicationEntrypointContextDataFragment,
  ConfiguratorApplicationDataFragment,
  ConfiguratorApplicationConfigurationDataFragment,
  ConditionSpecs_ApplicationModuleCondition_Fragment,
  ConditionSpecs_BankCondition_Fragment,
  ConditionSpecs_ContextualCondition_Fragment,
  ConditionSpecs_DealerCondition_Fragment,
  ConditionSpecs_GiftVoucherCondition_Fragment,
  ConditionSpecs_InsurerCondition_Fragment,
  ConditionSpecs_LocationCondition_Fragment,
  ConditionSpecs_LogicCondition_Fragment,
  ConditionSpecs_SalesOfferAgreementsCondition_Fragment,
  ConditionSpecsFragment,
  ConditionDetailSpecsFragment,
  CompanyWithPermissionsSpecsFragment,
  CompanyWithPermissionsContextDataFragment,
  CompanyWebpagePublicSpecFragment,
  CompanySpecsFragment,
  CompanySelectionItemFragment,
  CompanyPublicSpecsFragment,
  CompanyListDataFragment,
  CompanyInModuleOptionDataFragment,
  CompanyDealerDataFragment,
  CompanyContextDataFragment,
  ColumnWebPageBlockDataFragment,
  ColumnBlockDataFragment,
  ColorBlockSpecsFragment,
  ColorAndTrimSettingsSpecsFragment,
  CapVehicleModelMetadataSpecsFragment,
  CapVehicleMakeMetadataSpecsFragment,
  CapSettingSpecFragment,
  CapModuleWithPermissionsSpecsFragment,
  CapModuleSpecsFragment,
  CapLeadFragment,
  CapBusinessPartnerFragment,
  CampaignOptionDataFragment,
  CampaignDataFragment,
  CalculatorResultData_CalculatorDeferredPrincipalResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseWithBalloonGfvResult_Fragment,
  CalculatorResultData_CalculatorHirePurchaseWithBalloonResult_Fragment,
  CalculatorResultData_CalculatorLeasePurchaseResult_Fragment,
  CalculatorResultData_CalculatorLeaseResult_Fragment,
  CalculatorResultData_CalculatorUcclLeasingResult_Fragment,
  CalculatorResultDataFragment,
  BlockDetails_ColorBlock_Fragment,
  BlockDetails_OptionsBlock_Fragment,
  BlockDetails_PackageBlock_Fragment,
  BlockDetails_TrimBlock_Fragment,
  BlockDetailsFragment,
  BasicSigningModuleWithPermissionsSpecsFragment,
  BasicSigningModuleSpecsFragment,
  BasicRouterLayoutSpecsFragment,
  BasicProLayoutSpecsFragment,
  BaseConditionSpecs_ApplicationModuleCondition_Fragment,
  BaseConditionSpecs_BankCondition_Fragment,
  BaseConditionSpecs_ContextualCondition_Fragment,
  BaseConditionSpecs_DealerCondition_Fragment,
  BaseConditionSpecs_GiftVoucherCondition_Fragment,
  BaseConditionSpecs_InsurerCondition_Fragment,
  BaseConditionSpecs_LocationCondition_Fragment,
  BaseConditionSpecs_LogicCondition_Fragment,
  BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment,
  BaseConditionSpecsFragment,
  BannerPublicDataFragment,
  BannerListDataFragment,
  BannerDataFragment,
  BankOptionsDataFragment,
  BankModuleWithPermissionsSpecsFragment,
  BankModuleSpecsFragment,
  BankListDataFragment,
  BankIntegrationData_DbsBankIntegration_Fragment,
  BankIntegrationData_EmailBankIntegration_Fragment,
  BankIntegrationData_EnbdBankIntegration_Fragment,
  BankIntegrationData_HlfBankIntegration_Fragment,
  BankIntegrationData_HlfBankV2Integration_Fragment,
  BankIntegrationData_MaybankIntegration_Fragment,
  BankIntegrationData_UobBankIntegration_Fragment,
  BankIntegrationDataFragment,
  BankDetailsWithPermissionsDataFragment,
  BankDetailsDataFragment,
  BankDealerMarketDataFragment,
  BalloonSettingsDetails_BalloonRangeSettings_Fragment,
  BalloonSettingsDetails_BalloonTableSettings_Fragment,
  BalloonSettingsDetailsFragment,
  BalloonGfvSettingsDetailsFragment,
  AvailableModulesDataFragment,
  AutoplaySettingSpecsFragment,
  AutoplayModuleWithPermissionsSpecsFragment,
  AutoplayModuleSpecsFragment,
  AuthorData_CorporateCustomer_Fragment,
  AuthorData_ExternalBank_Fragment,
  AuthorData_Guarantor_Fragment,
  AuthorData_LocalCustomer_Fragment,
  AuthorData_PorscheRetain_Fragment,
  AuthorData_Salesforce_Fragment,
  AuthorData_SystemBank_Fragment,
  AuthorData_User_Fragment,
  AuthorDataFragment,
  AuditTrailDetailsFragment,
  AppointmentTimeSlotDataFragment,
  AppointmentModuleWithPermissionsSpecsFragment,
  AppointmentModuleSpecsFragment,
  AppointmentModuleOnEventModuleDataFragment,
  AppointmentModuleInDealerSpecsFragment,
  AppointmentModuleEmailContentSpecsFragment,
  AppointmentModuleEmailContentFinderReservationSpecsFragment,
  AppointmentModuleEmailContentCustomerSpecsFragment,
  AppointmentModuleEmailContentSalesPersonSpecsFragment,
  AppointmentModuleEmailContentsSpecsFragment,
  AppointmentModuleApplicationJourneyFragment,
  ApplicationVariantSpecFragment,
  ApplicationTtbDepositDataFragment,
  ApplicationStageUserData_ConfiguratorApplication_Fragment,
  ApplicationStageUserData_EventApplication_Fragment,
  ApplicationStageUserData_FinderApplication_Fragment,
  ApplicationStageUserData_LaunchpadApplication_Fragment,
  ApplicationStageUserData_MobilityApplication_Fragment,
  ApplicationStageUserData_SalesOfferApplication_Fragment,
  ApplicationStageUserData_StandardApplication_Fragment,
  ApplicationStageUserDataFragment,
  ApplicationStageData_ConfiguratorApplication_Fragment,
  ApplicationStageData_EventApplication_Fragment,
  ApplicationStageData_FinderApplication_Fragment,
  ApplicationStageData_LaunchpadApplication_Fragment,
  ApplicationStageData_MobilityApplication_Fragment,
  ApplicationStageData_SalesOfferApplication_Fragment,
  ApplicationStageData_StandardApplication_Fragment,
  ApplicationStageDataFragment,
  ApplicationQuotationOptionDataFragment,
  ApplicationQuotationDataFragment,
  ApplicationPorscheDepositDataFragment,
  ApplicationPayGateDepositDataFragment,
  ApplicationModelSpecsFragment,
  ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment,
  ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment,
  ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment,
  ApplicationMarketTypeFragmentFragment,
  ApplicationListEndpointSpecsFragment,
  ApplicationListEndpointContextDataFragment,
  ApplicationListData_ConfiguratorApplication_Fragment,
  ApplicationListData_EventApplication_Fragment,
  ApplicationListData_FinderApplication_Fragment,
  ApplicationListData_LaunchpadApplication_Fragment,
  ApplicationListData_MobilityApplication_Fragment,
  ApplicationListData_SalesOfferApplication_Fragment,
  ApplicationListData_StandardApplication_Fragment,
  ApplicationListDataFragment,
  ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment,
  ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment,
  ApplicationJourneyDepositFragment,
  ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment,
  ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment,
  ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment,
  ApplicationInsurancingDataFragment,
  ApplicationInCustomerDetails_ConfiguratorApplication_Fragment,
  ApplicationInCustomerDetails_EventApplication_Fragment,
  ApplicationInCustomerDetails_FinderApplication_Fragment,
  ApplicationInCustomerDetails_LaunchpadApplication_Fragment,
  ApplicationInCustomerDetails_MobilityApplication_Fragment,
  ApplicationInCustomerDetails_SalesOfferApplication_Fragment,
  ApplicationInCustomerDetails_StandardApplication_Fragment,
  ApplicationInCustomerDetailsFragment,
  ApplicationFiservDepositDataFragment,
  ApplicationFinderVehicleSpecsFragment,
  ApplicationFinancingListData_DefaultApplicationFinancing_Fragment,
  ApplicationFinancingListData_NewZealandApplicationFinancing_Fragment,
  ApplicationFinancingListData_SingaporeApplicationFinancing_Fragment,
  ApplicationFinancingListDataFragment,
  ApplicationFinancingData_DefaultApplicationFinancing_Fragment,
  ApplicationFinancingData_NewZealandApplicationFinancing_Fragment,
  ApplicationFinancingData_SingaporeApplicationFinancing_Fragment,
  ApplicationFinancingDataFragment,
  ApplicationEventCustomizedFieldDataFragment,
  ApplicationDocumentDataFragment,
  ApplicationData_ConfiguratorApplication_Fragment,
  ApplicationData_EventApplication_Fragment,
  ApplicationData_FinderApplication_Fragment,
  ApplicationData_LaunchpadApplication_Fragment,
  ApplicationData_MobilityApplication_Fragment,
  ApplicationData_SalesOfferApplication_Fragment,
  ApplicationData_StandardApplication_Fragment,
  ApplicationDataFragment,
  ApplicationConfigurationDataFragment,
  ApplicationAgreementData_CheckboxApplicationAgreement_Fragment,
  ApplicationAgreementData_GroupApplicationAgreement_Fragment,
  ApplicationAgreementData_MarketingApplicationAgreement_Fragment,
  ApplicationAgreementData_TextApplicationAgreement_Fragment,
  ApplicationAgreementDataFragment,
  ApplicationAdyenDepositDataFragment,
  AdyenPaymentSettingsSpecFragment,
  AdyenPaymentModuleWithPermissionsSpecsFragment,
  AdyenPaymentModuleSpecsFragment,
  AdvancedVersioningDataFragment,
  AdditionalDetailsSpecsFragment,
} from "./fragments/index";

export {
  StatisticNameCounterFragmentDoc,
  StatisticNameCountResultFragmentDoc,
  StatisticNameAmountResultFragmentDoc,
  StatisticNameAmountNumberFragmentDoc,
  StatisticDateCounterFragmentDoc,
  StatisticDateCountResultFragmentDoc,
  StatisticDateAmountResultFragmentDoc,
  StatisticDateAmountFragmentDoc,
  FormattedDateDataFragmentDoc,
  LocalizedStringDataFragmentDoc,
  LocalizedValueDataFragmentDoc,
  NumberUnitDataFragmentDoc,
  FullListingValueFragmentDoc,
  CurrencyDataFragmentDoc,
  WhatsappLiveChatSettingsSpecFragmentDoc,
  WhatsappLiveChatModuleWithPermissionsSpecsFragmentDoc,
  WhatsappLiveChatModuleSpecsFragmentDoc,
  WebsiteModuleWithPermissionsSpecsFragmentDoc,
  WebsiteModuleSpecsFragmentDoc,
  WebsiteModulePublicSpecsFragmentDoc,
  WebpageEndpointContextDataFragmentDoc,
  WebPagePathDataFragmentDoc,
  WebPageOptionDataFragmentDoc,
  WebPageListDataFragmentDoc,
  WebPageEndpointSpecsFragmentDoc,
  WebPageDataFragmentDoc,
  WebPageButtonDataFragmentDoc,
  WebPageBlockDataFragmentDoc,
  WebCalcSettingDataFragmentDoc,
  VisitAppointmentModuleWithPermissionsSpecsFragmentDoc,
  VisitAppointmentModuleSpecsFragmentDoc,
  VisitAppointmentModuleInDealerSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
  VisitAppointmentModuleEmailContentsSpecsFragmentDoc,
  VisitAppointmentModuleApplicationJourneyFragmentDoc,
  VehiclesListByParametersSpecsFragmentDoc,
  VehicleWithPermissionsSpecsFragmentDoc,
  VehicleSpecsFragmentDoc,
  VehicleSalesOfferSpecsFragmentDoc,
  VehicleReferenceParametersDataFragmentDoc,
  VehicleOptionsDataFragmentDoc,
  VehicleListDataFragmentDoc,
  VehicleDetailsFragmentDoc,
  VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc,
  VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragmentDoc,
  VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc,
  VehicleCalculatorSpecsFragmentDoc,
  VariantConfiguratorWithPermissionsSpecsFragmentDoc,
  VariantConfiguratorSpecsFragmentDoc,
  VariantConfiguratorListItemFragmentDoc,
  VariantConfiguratorJourneyDataFragmentDoc,
  VariantConfiguratorDetailsFragmentDoc,
  VsaSalesOfferSpecsFragmentDoc,
  UsersOptionsDataFragmentDoc,
  UserlikeChatbotSettingsSpecFragmentDoc,
  UserlikeChatbotModuleWithPermissionsSpecsFragmentDoc,
  UserlikeChatbotModuleSpecsFragmentDoc,
  UserSpecsFragmentDoc,
  UserPreviewDataFragmentDoc,
  UserListDataFragmentDoc,
  UserGroupSpecsFragmentDoc,
  UserGroupOptionsDataFragmentDoc,
  UserGroupListDataFragmentDoc,
  UserFullDataFragmentDoc,
  UserCompanyContextDataFragmentDoc,
  UserAvatarSpecsFragmentDoc,
  UploadedVariantAssetDataFragmentDoc,
  UploadFileWithPreviewFormDataFragmentDoc,
  UploadFileFormDataFragmentDoc,
  TtbPaymentSettingsSpecFragmentDoc,
  TtbPaymentModuleWithPermissionsSpecsFragmentDoc,
  TtbPaymentModuleSpecsFragmentDoc,
  TrimBlockSpecsFragmentDoc,
  TranslatedTextDataFragmentDoc,
  TranslatedStringSpecsFragmentDoc,
  TranslatedStringDataFragmentDoc,
  TradeInVehicleDataFragmentDoc,
  TradeInSettingSpecFragmentDoc,
  TradeInSalesOfferSpecsFragmentDoc,
  TradeInModuleWithPermissionsSpecsFragmentDoc,
  TradeInModuleSpecsFragmentDoc,
  TradeInListDataFragmentDoc,
  TradeInDataFragmentDoc,
  TimeSlotDataFragmentDoc,
  ThankYouPageContentSpecsFragmentDoc,
  TextImageWebPageBlockDataFragmentDoc,
  TextCarouselWebPageBlockDataFragmentDoc,
  TestDriveFixedPeriodDataFragmentDoc,
  TestDriveBookingWindowSettingsDataFragmentDoc,
  TermSettingsDetailsFragmentDoc,
  SystemMessageDataFragmentDoc,
  StockPublicDataFragmentDoc,
  StockInventorySpecsFragmentDoc,
  StockDetailsPublicDataFragmentDoc,
  StockBlockingPeriodDataFragmentDoc,
  StandardLeadDataFragmentDoc,
  StandardApplicationSpecFragmentDoc,
  StandardApplicationPublicAccessEntrypointSpecsFragmentDoc,
  StandardApplicationPublicAccessEntrypointContextDataFragmentDoc,
  StandardApplicationModuleWithPermissionsSpecsFragmentDoc,
  StandardApplicationModuleSpecsForApplicationFragmentDoc,
  StandardApplicationModuleSpecsFragmentDoc,
  StandardApplicationModuleInDealerSpecsFragmentDoc,
  StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc,
  StandardApplicationModuleEmailContentSpecsFragmentDoc,
  StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc,
  StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc,
  StandardApplicationModuleEmailContentsSpecsFragmentDoc,
  StandardApplicationModuleDebugJourneyFragmentDoc,
  StandardApplicationEntrypointSpecsFragmentDoc,
  StandardApplicationEntrypointContextDataFragmentDoc,
  StandardApplicationDataFragmentDoc,
  SimpleVersioningDataFragmentDoc,
  SimpleVehicleManagementModuleWithPermissionsSpecsFragmentDoc,
  SimpleVehicleManagementModuleSpecsFragmentDoc,
  SalesOfferSpecsFragmentDoc,
  SalesOfferSigningsSpecsFragmentDoc,
  SalesOfferModuleWithPermissionsSpecsFragmentDoc,
  SalesOfferModuleSpecsFragmentDoc,
  SalesOfferModuleInDealerSpecsFragmentDoc,
  SalesOfferEmailContentsSpecsFragmentDoc,
  SalesOfferModuleEmailContentsSpecsFragmentDoc,
  SalesOfferModuleDebugJourneyFragmentDoc,
  SalesOfferKycPresetSpecsFragmentDoc,
  SalesOfferJourneyDataFragmentDoc,
  SalesOfferDocumentDataFragmentDoc,
  SalesOfferConsentsSpecsFragmentDoc,
  SalesOfferApplicationSpecsFragmentDoc,
  SalesOfferApplicationDataFragmentDoc,
  SalesOfferApplicationConfigurationDataFragmentDoc,
  SalesControlBoardModuleWithPermissionsSpecsFragmentDoc,
  SalesControlBoardModuleSpecsFragmentDoc,
  SalesControlBoardModuleInDealerSpecsFragmentDoc,
  RouterWithPermissionsSpecsFragmentDoc,
  RouterFinderApplicationModuleFragmentDoc,
  RouterFinderApplicationPrivateModuleFragmentDoc,
  RouterSpecsFragmentDoc,
  RouterListWithEndpointsDataFragmentDoc,
  RouterListDataFragmentDoc,
  RouterLayoutContextDataFragmentDoc,
  RouterContextDataFragmentDoc,
  RoleSpecsFragmentDoc,
  RoleListDataFragmentDoc,
  ResidualValueSettingsDetailsFragmentDoc,
  ReferenceDepositDataFragmentDoc,
  ReferenceFinancingDataFragmentDoc,
  ReferenceInsuranceDataFragmentDoc,
  ReferenceApplicationDataFragmentDoc,
  PromoCodeSpecFragmentDoc,
  PromoCodeModuleWithPermissionsSpecsFragmentDoc,
  PromoCodeModuleSpecsFragmentDoc,
  PromoCodeListingDataFragmentDoc,
  PromoCodeDataFragmentDoc,
  ProductionInsuranceProductDetailsFragmentDoc,
  ProductionFinanceProductDetailsFragmentDoc,
  ProductionBankDetailsFragmentDoc,
  PrefetchKycConsentsDataFragmentDoc,
  PorscheVehicleDataFeatureSpecsFragmentDoc,
  PorscheVehicleImagesSpecsFragmentDoc,
  PorscheVehicleDataSpecsFragmentDoc,
  PorscheRetainModuleWithPermissionsSpecsFragmentDoc,
  PorscheRetainModuleSpecsFragmentDoc,
  PorschePaymentSettingsSpecFragmentDoc,
  PorschePaymentModuleWithPermissionsSpecsFragmentDoc,
  PorschePaymentModuleSpecsFragmentDoc,
  PorscheMasterDataModuleWithPermissionsSpecsFragmentDoc,
  PorscheMasterDataModuleSpecsFragmentDoc,
  PorscheIdSettingSpecFragmentDoc,
  PorscheIdModuleWithPermissionsSpecsFragmentDoc,
  PorscheIdModuleSpecsFragmentDoc,
  PermissionSpecsFragmentDoc,
  PeriodDataFragmentDoc,
  PaymentSettingsDetailsFragmentDoc,
  PayGatePaymentSettingsSpecFragmentDoc,
  PayGatePaymentModuleWithPermissionsSpecsFragmentDoc,
  PayGatePaymentModuleSpecsFragmentDoc,
  PathScriptSpecsFragmentDoc,
  PackageTypeSpecsFragmentDoc,
  PackageSettingsSpecsFragmentDoc,
  PackageBlockSpecsFragmentDoc,
  OptionsBlockSpecsFragmentDoc,
  OptionSettingDetailsFragmentDoc,
  OidcModuleSpecsFragmentDoc,
  NzFeesDealerMarketDataFragmentDoc,
  NearbyDealersDataFragmentDoc,
  NamirialSigningModuleWithPermissionsSpecsFragmentDoc,
  NamirialSigningModuleSpecsFragmentDoc,
  NamirialSigningDataFragmentDoc,
  NamirialSettingsSpecFragmentDoc,
  MyInfoSettingsListFragmentDoc,
  MyInfoSettingSpecFragmentDoc,
  MyInfoModuleWithPermissionsSpecsFragmentDoc,
  MyInfoModuleSpecsFragmentDoc,
  ModulesOptionsDataFragmentDoc,
  ModulesCompanyTimezoneDataFragmentDoc,
  ModuleWithScenariosFragmentDoc,
  ModuleWithPermissionsSpecsFragmentDoc,
  ModuleVariantDataFragmentDoc,
  ModuleSpecsFragmentDoc,
  ModuleListDataFragmentDoc,
  ModuleInDealerSpecsFragmentDoc,
  ModuleDisclaimersDataFragmentDoc,
  ModelConfiguratorWithPermissionsSpecsFragmentDoc,
  ModelConfiguratorSpecsFragmentDoc,
  ModelConfiguratorListDataFragmentDoc,
  ModelConfiguratorDetailsFragmentDoc,
  MobilityStockPublicListFragmentDoc,
  MobilityStockGiftVoucherDataFragmentDoc,
  MobilitySnapshotDataFragmentDoc,
  MobilitySigningSettingSpecsFragmentDoc,
  MobilityOperatorEmailContentDataFragmentDoc,
  MobilityModuleWithPermissionsSpecsFragmentDoc,
  MobilityModuleSpecsFragmentDoc,
  MobilityModuleInDealerSpecsFragmentDoc,
  MobilityModuleGiftVoucherDataFragmentDoc,
  MobilityModuleEmailScenarioContentSpecsFragmentDoc,
  MobilityLocationDataFragmentDoc,
  MobilityListDataFragmentDoc,
  MobilityLeadDataFragmentDoc,
  MobilityInventorySpecsFragmentDoc,
  MobilityInventoryPublicSpecsFragmentDoc,
  MobilityInventoryOptionFragmentDoc,
  MobilityHomeDeliveryDataFragmentDoc,
  MobilityEmailContentDataFragmentDoc,
  MobilityDataFragmentDoc,
  MobilityCustomerEmailContentDataFragmentDoc,
  MobilityBookingLocationPickupDataFragmentDoc,
  MobilityBookingLocationHomeDataFragmentDoc,
  MobilityBookingDetailsDataFragmentDoc,
  MobilityApplicationSpecsFragmentDoc,
  MobilityApplicationModuleDataFragmentDoc,
  MobilityApplicationExpiredFragmentDoc,
  MobilityApplicationEntrypointSpecsFragmentDoc,
  MobilityApplicationEntrypointContextDataFragmentDoc,
  MobilityApplicationDataFragmentDoc,
  MobilityAddonDataFragmentDoc,
  MobilityAdditionalInfoDataFragmentDoc,
  MenuItemSpecsFragmentDoc,
  MatrixSpecsFragmentDoc,
  MatrixDataFragmentDoc,
  MarketingPlatformsAgreedSpecsFragmentDoc,
  MarketingPlatformSpecsFragmentDoc,
  MarketingModuleWithPermissionsSpecsFragmentDoc,
  MarketingModuleSpecsFragmentDoc,
  MaintenanceUpdateFragmentDoc,
  MaintenanceModuleWithPermissionsSpecsFragmentDoc,
  MaintenanceModuleSpecsFragmentDoc,
  MainDetailsSalesOfferSpecsFragmentDoc,
  LocalVariantsListDataFragmentDoc,
  LocalVariantWithPermissionsSpecsFragmentDoc,
  LocalVariantSpecsFragmentDoc,
  LocalVariantPublicSpecsFragmentDoc,
  LocalVariantProductionListSpecsFragmentDoc,
  LocalVariantCalculatorSpecsFragmentDoc,
  LocalUcclLeasingOnlyDetailsFragmentDoc,
  LocalUcclLeasingDetailsFragmentDoc,
  LocalModelsListDataFragmentDoc,
  LocalModelWithPermissionsSpecsFragmentDoc,
  LocalModelSpecsFragmentDoc,
  LocalModelPublicSpecsFragmentDoc,
  LocalModelCalculatorSpecsFragmentDoc,
  LocalMakesListDataFragmentDoc,
  LocalMakeWithPermissionsSpecsFragmentDoc,
  LocalMakeSpecsFragmentDoc,
  LocalMakePublicSpecsFragmentDoc,
  LocalMakeCalculatorSpecsFragmentDoc,
  LocalLeasePurchaseDetailsFragmentDoc,
  LocalLeaseDetailsFragmentDoc,
  LocalHirePurchaseWithBalloonGfvDetailsFragmentDoc,
  LocalHirePurchaseWithBalloonDetailsFragmentDoc,
  LocalHirePurchaseDetailsFragmentDoc,
  LocalFittedOptionsSpecsFragmentDoc,
  LocalDeferredPrincipalDetailsFragmentDoc,
  LocalCustomerManagementModuleWithPermissionsSpecsFragmentDoc,
  LocalCustomerManagementModuleSpecsFragmentDoc,
  LocalCustomerManagementModuleKycFieldSpecsFragmentDoc,
  LocalCustomerFieldDataFragmentDoc,
  LocalCustomerDataFragmentDoc,
  LoanSettingsDetailsFragmentDoc,
  ListGiftVoucherDataFragmentDoc,
  LeaseSettingsDetailsFragmentDoc,
  LeadListEndpointSpecsFragmentDoc,
  LeadListEndpointContextDataFragmentDoc,
  LeadListDataFragmentDoc,
  LeadInCustomerListFragmentDoc,
  LeadDataFragmentDoc,
  LaunchpadModuleSpecsForApplicationFragmentDoc,
  LaunchpadModuleDebugJourneyFragmentDoc,
  LaunchpadLeadDataFragmentDoc,
  LaunchpadApplicationSpecFragmentDoc,
  LaunchpadApplicationModuleDebugJourneyFragmentDoc,
  LaunchpadApplicationDataFragmentDoc,
  LaunchpadApplicationConfigurationDataFragmentDoc,
  LaunchPadModuleWithPermissionsSpecsFragmentDoc,
  LaunchPadModuleSpecsFragmentDoc,
  LaunchPadModuleInDealerSpecsFragmentDoc,
  LaunchPadApplicationEntrypointSpecsFragmentDoc,
  LaunchPadApplicationEntrypointContextDataFragmentDoc,
  LanguagePackSpecsFragmentDoc,
  LanguagePackOptionDataFragmentDoc,
  LanguagePackListDataFragmentDoc,
  LanguagePackContextDataFragmentDoc,
  LabelsPublicDataFragmentDoc,
  LabelsModuleWithPermissionsSpecsFragmentDoc,
  LabelsModuleSpecsFragmentDoc,
  LabelsDataFragmentDoc,
  KycPresetsSpecFragmentDoc,
  KycPresetsOptionsDataFragmentDoc,
  KycFieldSpecsFragmentDoc,
  KycExtraSettingsSpecsFragmentDoc,
  JourneyEventDataFragmentDoc,
  JourneyDraftFlowFragmentDoc,
  InventoryVariantOptionSpecsFragmentDoc,
  InventorySubModelOptionSpecsFragmentDoc,
  InventoryOptionSpecsFragmentDoc,
  InventoryModuleOptionSpecsFragmentDoc,
  InventoryModelOptionSpecsFragmentDoc,
  InventoryListDataFragmentDoc,
  InventoryDetailsWithPermissionsDataFragmentDoc,
  InventoryDetailsPublicDataFragmentDoc,
  InventoryDetailsDataFragmentDoc,
  InterestRateSettingsDetailsFragmentDoc,
  InsurerListDataFragmentDoc,
  InsurerIntegrationDataFragmentDoc,
  InsurerEntrypointContextDataFragmentDoc,
  InsurerDetailsDataFragmentDoc,
  InsuranceSalesOfferSpecsFragmentDoc,
  InsuranceProductListDataFragmentDoc,
  InsuranceProductDetailsWithPermissionsDataFragmentDoc,
  InsuranceProductDetailsDataFragmentDoc,
  InsuranceProductDetailsFragmentDoc,
  InsuranceModuleWithPermissionsSpecsFragmentDoc,
  InsuranceModuleSpecsFragmentDoc,
  ImportExcelSpecsFragmentDoc,
  ImageWebPageBlockDataFragmentDoc,
  ImageDescriptionWebPageBlockDataFragmentDoc,
  GuarantorDataFragmentDoc,
  GiftVoucherModuleWithPermissionsSpecsFragmentDoc,
  GiftVoucherModuleSpecsFragmentDoc,
  GiftVoucherModuleInDealerSpecsFragmentDoc,
  GiftVoucherModuleEmailDataFragmentDoc,
  GiftVoucherModuleEmailContentSalesPersonSpecsFragmentDoc,
  GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc,
  GiftVoucherModuleEmailContentsSpecsFragmentDoc,
  GiftVoucherJourneyContextDataFragmentDoc,
  GiftVoucherDraftFlowDataFragmentDoc,
  GiftVoucherDataFragmentDoc,
  GiftVoucherSpecsFragmentDoc,
  GiftVoucherCodeDataFragmentDoc,
  GiftPromoTypeDataFragmentDoc,
  GetModulesListFragmentDoc,
  FlexibleDiscountDataFragmentDoc,
  FiservPaymentSettingsSpecFragmentDoc,
  FiservPaymentModuleWithPermissionsSpecsFragmentDoc,
  FiservPaymentModuleSpecsFragmentDoc,
  FinderVehiclesListDataFragmentDoc,
  FinderVehicleWithPermissionsSpecsFragmentDoc,
  FinderVehicleSpecsFragmentDoc,
  FinderVehicleSelectionDataFragmentDoc,
  FinderVehicleManagementModuleWithPermissionsSpecsFragmentDoc,
  FinderVehicleManagementModuleSpecsFragmentDoc,
  FinderVehicleDataFragmentDoc,
  FinderVehicleCalculatorDataFragmentDoc,
  FinderLeadDataFragmentDoc,
  FinderConfigurationDataFragmentDoc,
  FinderApplicationSpecsFragmentDoc,
  FinderApplicationPublicModuleWithPermissionsSpecsFragmentDoc,
  FinderApplicationPublicModuleSpecsForApplicationFragmentDoc,
  FinderApplicationPublicModuleSpecsFragmentDoc,
  FinderApplicationPublicModuleInDealerSpecsFragmentDoc,
  FinderApplicationPublicModuleDebugJourneyFragmentDoc,
  FinderApplicationPublicModuleDataFragmentDoc,
  FinderApplicationPublicAccessEntrypointSpecsFragmentDoc,
  FinderApplicationPublicAccessEntrypointContextDataFragmentDoc,
  FinderApplicationPrivateModuleWithPermissionsSpecsFragmentDoc,
  FinderApplicationPrivateModuleSpecsForApplicationFragmentDoc,
  FinderApplicationPrivateModuleSpecsFragmentDoc,
  FinderApplicationPrivateModuleInDealerSpecsFragmentDoc,
  FinderApplicationPrivateModuleDebugJourneyFragmentDoc,
  FinderApplicationPrivateModuleDataFragmentDoc,
  FinderApplicationModuleEmailContentSpecsFragmentDoc,
  FinderApplicationEntrypointSpecsFragmentDoc,
  FinderApplicationEntrypointContextDataFragmentDoc,
  FinderApplicationDataFragmentDoc,
  FinanceSalesOfferSpecsFragmentDoc,
  FinanceProductListDataFragmentDoc,
  FinanceProductDetailsWithPermissionsDataFragmentDoc,
  FinanceProductDetailsDataFragmentDoc,
  FinanceProductDetailsFragmentDoc,
  FeatureValueDataFragmentDoc,
  EventModuleDataFragmentDoc,
  EventListDataFragmentDoc,
  EventLeadDataFragmentDoc,
  EventDataFragmentDoc,
  EventApplicationSpecFragmentDoc,
  EventApplicationModuleWithPermissionsSpecsFragmentDoc,
  EventApplicationModuleSpecsForApplicationFragmentDoc,
  EventApplicationModuleSpecsFragmentDoc,
  EventApplicationModuleInDealerSpecsFragmentDoc,
  EventApplicationModuleEmailContentSpecsFragmentDoc,
  EventEmailContentSpecsFragmentDoc,
  EventApplicationModuleDebugJourneyFragmentDoc,
  EventApplicationEntrypointSpecsFragmentDoc,
  EventApplicationEntrypointContextDataFragmentDoc,
  EventApplicationDataFragmentDoc,
  EventApplicationConfigurationDataFragmentDoc,
  ErgoLookupTableSettingsDetailsFragmentDoc,
  ErgoLookupTableDetailsFragmentDoc,
  EntrypointFinderApplicationPublicModuleFragmentDoc,
  EntrypointFinderApplicationPrivateModuleFragmentDoc,
  EndpointListSpecsFragmentDoc,
  EndpointContextDataFragmentDoc,
  EdmSocialMediaDataFragmentDoc,
  EdmEmailFooterPublicDataFragmentDoc,
  EazyDetailsFragmentDoc,
  DummyWelcomePageEndpointSpecsFragmentDoc,
  DummyPrivatePageEndpointSpecsFragmentDoc,
  DummyPrivatePageEndpointContextDataFragmentDoc,
  DraftFlowConfigurationSpecFragmentDoc,
  DownPaymentSettingsDetailsFragmentDoc,
  DocusignSettingDataFragmentDoc,
  DocusignModuleSpecsWithPermissionSpecsFragmentDoc,
  DocusignModuleSpecsFragmentDoc,
  DiscountPromoTypeDataFragmentDoc,
  DiscountCodeDataFragmentDoc,
  DepositSettingsDetailsFragmentDoc,
  DepositSalesOfferSpecsFragmentDoc,
  DepositAmountDataFragmentDoc,
  DebugJourneyDataFragmentDoc,
  DealershipSettingSpecDataFragmentDoc,
  DealershipSettingDetailsDataFragmentDoc,
  DealerWithPermissionsFragmentFragmentDoc,
  DealerVehiclesSpecsFragmentDoc,
  DealerUploadedFileWithPreviewDataFragmentDoc,
  DealerTranslatedStringSettingDataFragmentDoc,
  DealerSocialMediaFragmentFragmentDoc,
  DealerPriceDisclaimerDataFragmentDoc,
  DealerDisclaimersConfiguratorDataFragmentDoc,
  DealerOptionsDataFragmentDoc,
  DealerMarketDataFragmentDoc,
  DealerListDataFragmentDoc,
  DealerJourneyDataFragmentDoc,
  DealerIntegrationDetailsFragmentFragmentDoc,
  DealerIntDataFragmentDoc,
  DealerObjectIdDataFragmentDoc,
  DealerFloatDataFragmentDoc,
  DealerInsuranceProductsSpecsFragmentDoc,
  DealerFragmentFragmentDoc,
  DealerFinanceProductsSpecsFragmentDoc,
  DealerDisclaimersFragmentFragmentDoc,
  DealerContactFragmentFragmentDoc,
  DealerBooleanSettingDataFragmentDoc,
  DealerBookingCodeSpecsFragmentDoc,
  DealerBookingCodeDataFragmentDoc,
  DealerApplicationFragmentFragmentDoc,
  DateUnitDataFragmentDoc,
  CustomizedFieldDataFragmentDoc,
  CustomerSpecsFragmentDoc,
  CustomerListStandardApplicationFragmentDoc,
  CustomerListSpecsFragmentDoc,
  CustomerListMobilityApplicationFragmentDoc,
  CustomerListLaunchpadApplicationFragmentDoc,
  CustomerListFinderApplicationFragmentDoc,
  CustomerListEventApplicationFragmentDoc,
  CustomerListEndpointSpecsFragmentDoc,
  CustomerListEndpointContextDataFragmentDoc,
  CustomerListConfiguratorApplicationFragmentDoc,
  CustomerDetailsStandardApplicationFragmentDoc,
  CustomerDetailsSalesOfferApplicationFragmentDoc,
  CustomerDetailsMobilityApplicationFragmentDoc,
  CustomerDetailsLaunchpadApplicationFragmentDoc,
  CustomerDetailsFinderApplicationFragmentDoc,
  CustomerDetailsEventApplicationFragmentDoc,
  CustomerDetailsConfiguratorApplicationFragmentDoc,
  CustomerDetailSpecsFragmentDoc,
  CustomWebPageBlockDataFragmentDoc,
  CustomTestDriveBookingSlotsDataFragmentDoc,
  CurrentUserDataFragmentDoc,
  CurrentUserCompaniesDataFragmentDoc,
  CtsModuleWithPermissionsSpecsFragmentDoc,
  CtsModuleSpecsFragmentDoc,
  CtsModuleSettingDataFragmentDoc,
  CounterSettingsSpecsFragmentDoc,
  CorporateCustomerDataFragmentDoc,
  ConsentsAndDeclarationsWithPermissionsSpecsFragmentDoc,
  ConsentsAndDeclarationsSpecsFragmentDoc,
  ConsentsAndDeclarationsModuleWithPermissionsSpecsFragmentDoc,
  ConsentsAndDeclarationsModuleSpecsFragmentDoc,
  ConsentsAndDeclarationsListDataFragmentDoc,
  ConfiguratorModuleWithPermissionsSpecsFragmentDoc,
  ConfiguratorModuleSpecsForApplicationFragmentDoc,
  ConfiguratorModuleSpecsFragmentDoc,
  ConfiguratorModuleInDealerSpecsFragmentDoc,
  ConfiguratorModuleEmailContentSpecsFragmentDoc,
  ConfiguratorLeadDataFragmentDoc,
  ConfiguratorJourneyBlocksDataFragmentDoc,
  ConfiguratorInventorySpecsFragmentDoc,
  ConfiguratorInventoryPublicSpecsFragmentDoc,
  ConfiguratorDetailsFragmentDoc,
  ConfiguratorApplicationSpecFragmentDoc,
  ConfiguratorApplicationModuleDebugJourneyFragmentDoc,
  ConfiguratorApplicationModuleDataFragmentDoc,
  ConfiguratorApplicationEntrypointSpecsFragmentDoc,
  ConfiguratorApplicationEntrypointContextDataFragmentDoc,
  ConfiguratorApplicationDataFragmentDoc,
  ConfiguratorApplicationConfigurationDataFragmentDoc,
  ConditionSpecsFragmentDoc,
  ConditionDetailSpecsFragmentDoc,
  CompanyWithPermissionsSpecsFragmentDoc,
  CompanyWithPermissionsContextDataFragmentDoc,
  CompanyWebpagePublicSpecFragmentDoc,
  CompanySpecsFragmentDoc,
  CompanySelectionItemFragmentDoc,
  CompanyPublicSpecsFragmentDoc,
  CompanyListDataFragmentDoc,
  CompanyInModuleOptionDataFragmentDoc,
  CompanyDealerDataFragmentDoc,
  CompanyContextDataFragmentDoc,
  ColumnWebPageBlockDataFragmentDoc,
  ColumnBlockDataFragmentDoc,
  ColorBlockSpecsFragmentDoc,
  ColorAndTrimSettingsSpecsFragmentDoc,
  CapVehicleModelMetadataSpecsFragmentDoc,
  CapVehicleMakeMetadataSpecsFragmentDoc,
  CapSettingSpecFragmentDoc,
  CapModuleWithPermissionsSpecsFragmentDoc,
  CapModuleSpecsFragmentDoc,
  CapLeadFragmentDoc,
  CapBusinessPartnerFragmentDoc,
  CampaignOptionDataFragmentDoc,
  CampaignDataFragmentDoc,
  CalculatorResultDataFragmentDoc,
  BlockDetailsFragmentDoc,
  BasicSigningModuleWithPermissionsSpecsFragmentDoc,
  BasicSigningModuleSpecsFragmentDoc,
  BasicRouterLayoutSpecsFragmentDoc,
  BasicProLayoutSpecsFragmentDoc,
  BaseConditionSpecsFragmentDoc,
  BannerPublicDataFragmentDoc,
  BannerListDataFragmentDoc,
  BannerDataFragmentDoc,
  BankOptionsDataFragmentDoc,
  BankModuleWithPermissionsSpecsFragmentDoc,
  BankModuleSpecsFragmentDoc,
  BankListDataFragmentDoc,
  BankIntegrationDataFragmentDoc,
  BankDetailsWithPermissionsDataFragmentDoc,
  BankDetailsDataFragmentDoc,
  BankDealerMarketDataFragmentDoc,
  BalloonSettingsDetailsFragmentDoc,
  BalloonGfvSettingsDetailsFragmentDoc,
  AvailableModulesDataFragmentDoc,
  AutoplaySettingSpecsFragmentDoc,
  AutoplayModuleWithPermissionsSpecsFragmentDoc,
  AutoplayModuleSpecsFragmentDoc,
  AuthorDataFragmentDoc,
  AuditTrailDetailsFragmentDoc,
  AppointmentTimeSlotDataFragmentDoc,
  AppointmentModuleWithPermissionsSpecsFragmentDoc,
  AppointmentModuleSpecsFragmentDoc,
  AppointmentModuleOnEventModuleDataFragmentDoc,
  AppointmentModuleInDealerSpecsFragmentDoc,
  AppointmentModuleEmailContentSpecsFragmentDoc,
  AppointmentModuleEmailContentCustomerSpecsFragmentDoc,
  AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc,
  AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
  AppointmentModuleEmailContentsSpecsFragmentDoc,
  AppointmentModuleApplicationJourneyFragmentDoc,
  ApplicationVariantSpecFragmentDoc,
  ApplicationTtbDepositDataFragmentDoc,
  ApplicationStageUserDataFragmentDoc,
  ApplicationStageDataFragmentDoc,
  ApplicationQuotationOptionDataFragmentDoc,
  ApplicationQuotationDataFragmentDoc,
  ApplicationPorscheDepositDataFragmentDoc,
  ApplicationPayGateDepositDataFragmentDoc,
  ApplicationModelSpecsFragmentDoc,
  ApplicationMarketTypeFragmentFragmentDoc,
  ApplicationListEndpointSpecsFragmentDoc,
  ApplicationListEndpointContextDataFragmentDoc,
  ApplicationListDataFragmentDoc,
  ApplicationJourneyDepositFragmentDoc,
  ApplicationInsurancingDataFragmentDoc,
  ApplicationInCustomerDetailsFragmentDoc,
  ApplicationFiservDepositDataFragmentDoc,
  ApplicationFinderVehicleSpecsFragmentDoc,
  ApplicationFinancingListDataFragmentDoc,
  ApplicationFinancingDataFragmentDoc,
  ApplicationEventCustomizedFieldDataFragmentDoc,
  ApplicationDocumentDataFragmentDoc,
  ApplicationDataFragmentDoc,
  ApplicationConfigurationDataFragmentDoc,
  ApplicationAgreementDataFragmentDoc,
  ApplicationAdyenDepositDataFragmentDoc,
  AdyenPaymentSettingsSpecFragmentDoc,
  AdyenPaymentModuleWithPermissionsSpecsFragmentDoc,
  AdyenPaymentModuleSpecsFragmentDoc,
  AdvancedVersioningDataFragmentDoc,
  AdditionalDetailsSpecsFragmentDoc,
} from "./fragments/index";

export type {
  VerifyMobileOtpMutationVariables,
  VerifyMobileOtpMutation,
  VerifyMobileOtpMutationFn,
  VerifyMobileOtpMutationHookResult,
  VerifyMobileOtpMutationResult,
  VerifyMobileOtpMutationOptions,
  ValidateSmsOtpForUpdateEmailMutationVariables,
  ValidateSmsOtpForUpdateEmailMutation,
  ValidateSmsOtpForUpdateEmailMutationFn,
  ValidateSmsOtpForUpdateEmailMutationHookResult,
  ValidateSmsOtpForUpdateEmailMutationResult,
  ValidateSmsOtpForUpdateEmailMutationOptions,
  ValidateSmsOtpForResetPasswordMutationVariables,
  ValidateSmsOtpForResetPasswordMutation,
  ValidateSmsOtpForResetPasswordMutationFn,
  ValidateSmsOtpForResetPasswordMutationHookResult,
  ValidateSmsOtpForResetPasswordMutationResult,
  ValidateSmsOtpForResetPasswordMutationOptions,
  ValidateSalesOfferRemoteJourneyPasscodeMutationVariables,
  ValidateSalesOfferRemoteJourneyPasscodeMutation,
  ValidateSalesOfferRemoteJourneyPasscodeMutationFn,
  ValidateSalesOfferRemoteJourneyPasscodeMutationHookResult,
  ValidateSalesOfferRemoteJourneyPasscodeMutationResult,
  ValidateSalesOfferRemoteJourneyPasscodeMutationOptions,
  ValidateRemoteJourneyPasscodeMutationVariables,
  ValidateRemoteJourneyPasscodeMutation,
  ValidateRemoteJourneyPasscodeMutationFn,
  ValidateRemoteJourneyPasscodeMutationHookResult,
  ValidateRemoteJourneyPasscodeMutationResult,
  ValidateRemoteJourneyPasscodeMutationOptions,
  UpsertKycPresetInEventMutationVariables,
  UpsertKycPresetInEventMutation,
  UpsertKycPresetInEventMutationFn,
  UpsertKycPresetInEventMutationHookResult,
  UpsertKycPresetInEventMutationResult,
  UpsertKycPresetInEventMutationOptions,
  UpsertEventUserIdsMutationVariables,
  UpsertEventUserIdsMutation,
  UpsertEventUserIdsMutationFn,
  UpsertEventUserIdsMutationHookResult,
  UpsertEventUserIdsMutationResult,
  UpsertEventUserIdsMutationOptions,
  UploadWebsiteSocialMediaAssetMutationVariables,
  UploadWebsiteSocialMediaAssetMutation,
  UploadWebsiteSocialMediaAssetMutationFn,
  UploadWebsiteSocialMediaAssetMutationHookResult,
  UploadWebsiteSocialMediaAssetMutationResult,
  UploadWebsiteSocialMediaAssetMutationOptions,
  UploadWebPageImageMutationVariables,
  UploadWebPageImageMutation,
  UploadWebPageImageMutationFn,
  UploadWebPageImageMutationHookResult,
  UploadWebPageImageMutationResult,
  UploadWebPageImageMutationOptions,
  UploadVisitAppointmentModuleAssetMutationVariables,
  UploadVisitAppointmentModuleAssetMutation,
  UploadVisitAppointmentModuleAssetMutationFn,
  UploadVisitAppointmentModuleAssetMutationHookResult,
  UploadVisitAppointmentModuleAssetMutationResult,
  UploadVisitAppointmentModuleAssetMutationOptions,
  UploadVehicleSalesOfferSpecificationDocumentMutationVariables,
  UploadVehicleSalesOfferSpecificationDocumentMutation,
  UploadVehicleSalesOfferSpecificationDocumentMutationFn,
  UploadVehicleSalesOfferSpecificationDocumentMutationHookResult,
  UploadVehicleSalesOfferSpecificationDocumentMutationResult,
  UploadVehicleSalesOfferSpecificationDocumentMutationOptions,
  UploadVariantConfiguratorTrimSettingAssetMutationVariables,
  UploadVariantConfiguratorTrimSettingAssetMutation,
  UploadVariantConfiguratorTrimSettingAssetMutationFn,
  UploadVariantConfiguratorTrimSettingAssetMutationHookResult,
  UploadVariantConfiguratorTrimSettingAssetMutationResult,
  UploadVariantConfiguratorTrimSettingAssetMutationOptions,
  UploadVariantConfiguratorPackageSectionImageAssetMutationVariables,
  UploadVariantConfiguratorPackageSectionImageAssetMutation,
  UploadVariantConfiguratorPackageSectionImageAssetMutationFn,
  UploadVariantConfiguratorPackageSectionImageAssetMutationHookResult,
  UploadVariantConfiguratorPackageSectionImageAssetMutationResult,
  UploadVariantConfiguratorPackageSectionImageAssetMutationOptions,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationVariables,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationFn,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationHookResult,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationResult,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetMutationOptions,
  UploadVariantConfiguratorOptionSettingAssetMutationVariables,
  UploadVariantConfiguratorOptionSettingAssetMutation,
  UploadVariantConfiguratorOptionSettingAssetMutationFn,
  UploadVariantConfiguratorOptionSettingAssetMutationHookResult,
  UploadVariantConfiguratorOptionSettingAssetMutationResult,
  UploadVariantConfiguratorOptionSettingAssetMutationOptions,
  UploadVariantConfiguratorOptionAssetMutationVariables,
  UploadVariantConfiguratorOptionAssetMutation,
  UploadVariantConfiguratorOptionAssetMutationFn,
  UploadVariantConfiguratorOptionAssetMutationHookResult,
  UploadVariantConfiguratorOptionAssetMutationResult,
  UploadVariantConfiguratorOptionAssetMutationOptions,
  UploadVariantConfiguratorMatrixAssetMutationVariables,
  UploadVariantConfiguratorMatrixAssetMutation,
  UploadVariantConfiguratorMatrixAssetMutationFn,
  UploadVariantConfiguratorMatrixAssetMutationHookResult,
  UploadVariantConfiguratorMatrixAssetMutationResult,
  UploadVariantConfiguratorMatrixAssetMutationOptions,
  UploadVariantConfiguratorColorSettingAssetMutationVariables,
  UploadVariantConfiguratorColorSettingAssetMutation,
  UploadVariantConfiguratorColorSettingAssetMutationFn,
  UploadVariantConfiguratorColorSettingAssetMutationHookResult,
  UploadVariantConfiguratorColorSettingAssetMutationResult,
  UploadVariantConfiguratorColorSettingAssetMutationOptions,
  UploadVariantAssetMutationVariables,
  UploadVariantAssetMutation,
  UploadVariantAssetMutationFn,
  UploadVariantAssetMutationHookResult,
  UploadVariantAssetMutationResult,
  UploadVariantAssetMutationOptions,
  UploadUserAssetMutationVariables,
  UploadUserAssetMutation,
  UploadUserAssetMutationFn,
  UploadUserAssetMutationHookResult,
  UploadUserAssetMutationResult,
  UploadUserAssetMutationOptions,
  UploadStockAssetMutationVariables,
  UploadStockAssetMutation,
  UploadStockAssetMutationFn,
  UploadStockAssetMutationHookResult,
  UploadStockAssetMutationResult,
  UploadStockAssetMutationOptions,
  UploadStandardApplicationModuleAssetMutationVariables,
  UploadStandardApplicationModuleAssetMutation,
  UploadStandardApplicationModuleAssetMutationFn,
  UploadStandardApplicationModuleAssetMutationHookResult,
  UploadStandardApplicationModuleAssetMutationResult,
  UploadStandardApplicationModuleAssetMutationOptions,
  UploadSalesOfferDocumentMutationVariables,
  UploadSalesOfferDocumentMutation,
  UploadSalesOfferDocumentMutationFn,
  UploadSalesOfferDocumentMutationHookResult,
  UploadSalesOfferDocumentMutationResult,
  UploadSalesOfferDocumentMutationOptions,
  UploadModelConfiguratorAssetMutationVariables,
  UploadModelConfiguratorAssetMutation,
  UploadModelConfiguratorAssetMutationFn,
  UploadModelConfiguratorAssetMutationHookResult,
  UploadModelConfiguratorAssetMutationResult,
  UploadModelConfiguratorAssetMutationOptions,
  UploadMobilityModuleEmailAssetMutationVariables,
  UploadMobilityModuleEmailAssetMutation,
  UploadMobilityModuleEmailAssetMutationFn,
  UploadMobilityModuleEmailAssetMutationHookResult,
  UploadMobilityModuleEmailAssetMutationResult,
  UploadMobilityModuleEmailAssetMutationOptions,
  UploadMaintenanceModuleAssetMutationVariables,
  UploadMaintenanceModuleAssetMutation,
  UploadMaintenanceModuleAssetMutationFn,
  UploadMaintenanceModuleAssetMutationHookResult,
  UploadMaintenanceModuleAssetMutationResult,
  UploadMaintenanceModuleAssetMutationOptions,
  UploadLeadDocumentsMutationVariables,
  UploadLeadDocumentsMutation,
  UploadLeadDocumentsMutationFn,
  UploadLeadDocumentsMutationHookResult,
  UploadLeadDocumentsMutationResult,
  UploadLeadDocumentsMutationOptions,
  UploadLeadDocumentMutationVariables,
  UploadLeadDocumentMutation,
  UploadLeadDocumentMutationFn,
  UploadLeadDocumentMutationHookResult,
  UploadLeadDocumentMutationResult,
  UploadLeadDocumentMutationOptions,
  UploadGiftVoucherModuleAssetMutationVariables,
  UploadGiftVoucherModuleAssetMutation,
  UploadGiftVoucherModuleAssetMutationFn,
  UploadGiftVoucherModuleAssetMutationHookResult,
  UploadGiftVoucherModuleAssetMutationResult,
  UploadGiftVoucherModuleAssetMutationOptions,
  UploadGiftVoucherDocumentMutationVariables,
  UploadGiftVoucherDocumentMutation,
  UploadGiftVoucherDocumentMutationFn,
  UploadGiftVoucherDocumentMutationHookResult,
  UploadGiftVoucherDocumentMutationResult,
  UploadGiftVoucherDocumentMutationOptions,
  UploadFinderApplicationModuleAssetMutationVariables,
  UploadFinderApplicationModuleAssetMutation,
  UploadFinderApplicationModuleAssetMutationFn,
  UploadFinderApplicationModuleAssetMutationHookResult,
  UploadFinderApplicationModuleAssetMutationResult,
  UploadFinderApplicationModuleAssetMutationOptions,
  UploadEventLevelAssetMutationVariables,
  UploadEventLevelAssetMutation,
  UploadEventLevelAssetMutationFn,
  UploadEventLevelAssetMutationHookResult,
  UploadEventLevelAssetMutationResult,
  UploadEventLevelAssetMutationOptions,
  UploadEventApplicationModuleAssetMutationVariables,
  UploadEventApplicationModuleAssetMutation,
  UploadEventApplicationModuleAssetMutationFn,
  UploadEventApplicationModuleAssetMutationHookResult,
  UploadEventApplicationModuleAssetMutationResult,
  UploadEventApplicationModuleAssetMutationOptions,
  UploadEdmSocialMediaAssetMutationVariables,
  UploadEdmSocialMediaAssetMutation,
  UploadEdmSocialMediaAssetMutationFn,
  UploadEdmSocialMediaAssetMutationHookResult,
  UploadEdmSocialMediaAssetMutationResult,
  UploadEdmSocialMediaAssetMutationOptions,
  UploadDealerSocialMediaAssetMutationVariables,
  UploadDealerSocialMediaAssetMutation,
  UploadDealerSocialMediaAssetMutationFn,
  UploadDealerSocialMediaAssetMutationHookResult,
  UploadDealerSocialMediaAssetMutationResult,
  UploadDealerSocialMediaAssetMutationOptions,
  UploadConfiguratorModuleAssetMutationVariables,
  UploadConfiguratorModuleAssetMutation,
  UploadConfiguratorModuleAssetMutationFn,
  UploadConfiguratorModuleAssetMutationHookResult,
  UploadConfiguratorModuleAssetMutationResult,
  UploadConfiguratorModuleAssetMutationOptions,
  UploadConfiguratorDescriptionImageMutationVariables,
  UploadConfiguratorDescriptionImageMutation,
  UploadConfiguratorDescriptionImageMutationFn,
  UploadConfiguratorDescriptionImageMutationHookResult,
  UploadConfiguratorDescriptionImageMutationResult,
  UploadConfiguratorDescriptionImageMutationOptions,
  UploadCompanyAssetMutationVariables,
  UploadCompanyAssetMutation,
  UploadCompanyAssetMutationFn,
  UploadCompanyAssetMutationHookResult,
  UploadCompanyAssetMutationResult,
  UploadCompanyAssetMutationOptions,
  UploadBannerImageMutationVariables,
  UploadBannerImageMutation,
  UploadBannerImageMutationFn,
  UploadBannerImageMutationHookResult,
  UploadBannerImageMutationResult,
  UploadBannerImageMutationOptions,
  UploadBankAssetMutationVariables,
  UploadBankAssetMutation,
  UploadBankAssetMutationFn,
  UploadBankAssetMutationHookResult,
  UploadBankAssetMutationResult,
  UploadBankAssetMutationOptions,
  UploadAppointmentModuleAssetMutationVariables,
  UploadAppointmentModuleAssetMutation,
  UploadAppointmentModuleAssetMutationFn,
  UploadAppointmentModuleAssetMutationHookResult,
  UploadAppointmentModuleAssetMutationResult,
  UploadAppointmentModuleAssetMutationOptions,
  UploadApplicationDocumentsMutationVariables,
  UploadApplicationDocumentsMutation,
  UploadApplicationDocumentsMutationFn,
  UploadApplicationDocumentsMutationHookResult,
  UploadApplicationDocumentsMutationResult,
  UploadApplicationDocumentsMutationOptions,
  UploadApplicationDocumentMutationVariables,
  UploadApplicationDocumentMutation,
  UploadApplicationDocumentMutationFn,
  UploadApplicationDocumentMutationHookResult,
  UploadApplicationDocumentMutationResult,
  UploadApplicationDocumentMutationOptions,
  UpdateWhatsappLiveChatSettingsMutationVariables,
  UpdateWhatsappLiveChatSettingsMutation,
  UpdateWhatsappLiveChatSettingsMutationFn,
  UpdateWhatsappLiveChatSettingsMutationHookResult,
  UpdateWhatsappLiveChatSettingsMutationResult,
  UpdateWhatsappLiveChatSettingsMutationOptions,
  UpdateWhatsappLiveChatModuleMutationVariables,
  UpdateWhatsappLiveChatModuleMutation,
  UpdateWhatsappLiveChatModuleMutationFn,
  UpdateWhatsappLiveChatModuleMutationHookResult,
  UpdateWhatsappLiveChatModuleMutationResult,
  UpdateWhatsappLiveChatModuleMutationOptions,
  UpdateWebsiteSocialMediaMutationVariables,
  UpdateWebsiteSocialMediaMutation,
  UpdateWebsiteSocialMediaMutationFn,
  UpdateWebsiteSocialMediaMutationHookResult,
  UpdateWebsiteSocialMediaMutationResult,
  UpdateWebsiteSocialMediaMutationOptions,
  UpdateWebsiteModuleMutationVariables,
  UpdateWebsiteModuleMutation,
  UpdateWebsiteModuleMutationFn,
  UpdateWebsiteModuleMutationHookResult,
  UpdateWebsiteModuleMutationResult,
  UpdateWebsiteModuleMutationOptions,
  UpdateWebPageEndpointMutationVariables,
  UpdateWebPageEndpointMutation,
  UpdateWebPageEndpointMutationFn,
  UpdateWebPageEndpointMutationHookResult,
  UpdateWebPageEndpointMutationResult,
  UpdateWebPageEndpointMutationOptions,
  UpdateWebPageMutationVariables,
  UpdateWebPageMutation,
  UpdateWebPageMutationFn,
  UpdateWebPageMutationHookResult,
  UpdateWebPageMutationResult,
  UpdateWebPageMutationOptions,
  UpdateWebCalcSettingMutationVariables,
  UpdateWebCalcSettingMutation,
  UpdateWebCalcSettingMutationFn,
  UpdateWebCalcSettingMutationHookResult,
  UpdateWebCalcSettingMutationResult,
  UpdateWebCalcSettingMutationOptions,
  UpdateVisitAppointmentModuleEmailContentMutationVariables,
  UpdateVisitAppointmentModuleEmailContentMutation,
  UpdateVisitAppointmentModuleEmailContentMutationFn,
  UpdateVisitAppointmentModuleEmailContentMutationHookResult,
  UpdateVisitAppointmentModuleEmailContentMutationResult,
  UpdateVisitAppointmentModuleEmailContentMutationOptions,
  UpdateVisitAppointmentModuleMutationVariables,
  UpdateVisitAppointmentModuleMutation,
  UpdateVisitAppointmentModuleMutationFn,
  UpdateVisitAppointmentModuleMutationHookResult,
  UpdateVisitAppointmentModuleMutationResult,
  UpdateVisitAppointmentModuleMutationOptions,
  UpdateVehicleSalesOfferMutationVariables,
  UpdateVehicleSalesOfferMutation,
  UpdateVehicleSalesOfferMutationFn,
  UpdateVehicleSalesOfferMutationHookResult,
  UpdateVehicleSalesOfferMutationResult,
  UpdateVehicleSalesOfferMutationOptions,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationFn,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationHookResult,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationResult,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleMutationOptions,
  UpdateVariantLabelsMutationVariables,
  UpdateVariantLabelsMutation,
  UpdateVariantLabelsMutationFn,
  UpdateVariantLabelsMutationHookResult,
  UpdateVariantLabelsMutationResult,
  UpdateVariantLabelsMutationOptions,
  UpdateVariantImageOrderingMutationVariables,
  UpdateVariantImageOrderingMutation,
  UpdateVariantImageOrderingMutationFn,
  UpdateVariantImageOrderingMutationHookResult,
  UpdateVariantImageOrderingMutationResult,
  UpdateVariantImageOrderingMutationOptions,
  UpdateVariantConfiguratorMutationVariables,
  UpdateVariantConfiguratorMutation,
  UpdateVariantConfiguratorMutationFn,
  UpdateVariantConfiguratorMutationHookResult,
  UpdateVariantConfiguratorMutationResult,
  UpdateVariantConfiguratorMutationOptions,
  UpdateVariantMutationVariables,
  UpdateVariantMutation,
  UpdateVariantMutationFn,
  UpdateVariantMutationHookResult,
  UpdateVariantMutationResult,
  UpdateVariantMutationOptions,
  UpdateUserlikeChatbotSettingsMutationVariables,
  UpdateUserlikeChatbotSettingsMutation,
  UpdateUserlikeChatbotSettingsMutationFn,
  UpdateUserlikeChatbotSettingsMutationHookResult,
  UpdateUserlikeChatbotSettingsMutationResult,
  UpdateUserlikeChatbotSettingsMutationOptions,
  UpdateUserlikeChatbotModuleMutationVariables,
  UpdateUserlikeChatbotModuleMutation,
  UpdateUserlikeChatbotModuleMutationFn,
  UpdateUserlikeChatbotModuleMutationHookResult,
  UpdateUserlikeChatbotModuleMutationResult,
  UpdateUserlikeChatbotModuleMutationOptions,
  UpdateUserGroupMutationVariables,
  UpdateUserGroupMutation,
  UpdateUserGroupMutationFn,
  UpdateUserGroupMutationHookResult,
  UpdateUserGroupMutationResult,
  UpdateUserGroupMutationOptions,
  UpdateUserMutationVariables,
  UpdateUserMutation,
  UpdateUserMutationFn,
  UpdateUserMutationHookResult,
  UpdateUserMutationResult,
  UpdateUserMutationOptions,
  UpdateUobBankIntegrationSettingMutationVariables,
  UpdateUobBankIntegrationSettingMutation,
  UpdateUobBankIntegrationSettingMutationFn,
  UpdateUobBankIntegrationSettingMutationHookResult,
  UpdateUobBankIntegrationSettingMutationResult,
  UpdateUobBankIntegrationSettingMutationOptions,
  UpdateTtbPaymentSettingsMutationVariables,
  UpdateTtbPaymentSettingsMutation,
  UpdateTtbPaymentSettingsMutationFn,
  UpdateTtbPaymentSettingsMutationHookResult,
  UpdateTtbPaymentSettingsMutationResult,
  UpdateTtbPaymentSettingsMutationOptions,
  UpdateTtbPaymentModuleMutationVariables,
  UpdateTtbPaymentModuleMutation,
  UpdateTtbPaymentModuleMutationFn,
  UpdateTtbPaymentModuleMutationHookResult,
  UpdateTtbPaymentModuleMutationResult,
  UpdateTtbPaymentModuleMutationOptions,
  UpdateTrimBlockMutationVariables,
  UpdateTrimBlockMutation,
  UpdateTrimBlockMutationFn,
  UpdateTrimBlockMutationHookResult,
  UpdateTrimBlockMutationResult,
  UpdateTrimBlockMutationOptions,
  UpdateTradeInSalesOfferMutationVariables,
  UpdateTradeInSalesOfferMutation,
  UpdateTradeInSalesOfferMutationFn,
  UpdateTradeInSalesOfferMutationHookResult,
  UpdateTradeInSalesOfferMutationResult,
  UpdateTradeInSalesOfferMutationOptions,
  UpdateTradeInModuleMutationVariables,
  UpdateTradeInModuleMutation,
  UpdateTradeInModuleMutationFn,
  UpdateTradeInModuleMutationHookResult,
  UpdateTradeInModuleMutationResult,
  UpdateTradeInModuleMutationOptions,
  UpdateTextConsentsAndDeclarationsMutationVariables,
  UpdateTextConsentsAndDeclarationsMutation,
  UpdateTextConsentsAndDeclarationsMutationFn,
  UpdateTextConsentsAndDeclarationsMutationHookResult,
  UpdateTextConsentsAndDeclarationsMutationResult,
  UpdateTextConsentsAndDeclarationsMutationOptions,
  UpdateTestDriveDataMutationVariables,
  UpdateTestDriveDataMutation,
  UpdateTestDriveDataMutationFn,
  UpdateTestDriveDataMutationHookResult,
  UpdateTestDriveDataMutationResult,
  UpdateTestDriveDataMutationOptions,
  UpdateStockInventoryMutationVariables,
  UpdateStockInventoryMutation,
  UpdateStockInventoryMutationFn,
  UpdateStockInventoryMutationHookResult,
  UpdateStockInventoryMutationResult,
  UpdateStockInventoryMutationOptions,
  UpdateStandardApplicationVehicleAssignmentsMutationVariables,
  UpdateStandardApplicationVehicleAssignmentsMutation,
  UpdateStandardApplicationVehicleAssignmentsMutationFn,
  UpdateStandardApplicationVehicleAssignmentsMutationHookResult,
  UpdateStandardApplicationVehicleAssignmentsMutationResult,
  UpdateStandardApplicationVehicleAssignmentsMutationOptions,
  UpdateStandardApplicationPublicAccessEntrypointMutationVariables,
  UpdateStandardApplicationPublicAccessEntrypointMutation,
  UpdateStandardApplicationPublicAccessEntrypointMutationFn,
  UpdateStandardApplicationPublicAccessEntrypointMutationHookResult,
  UpdateStandardApplicationPublicAccessEntrypointMutationResult,
  UpdateStandardApplicationPublicAccessEntrypointMutationOptions,
  UpdateStandardApplicationModuleMainDetailsMutationVariables,
  UpdateStandardApplicationModuleMainDetailsMutation,
  UpdateStandardApplicationModuleMainDetailsMutationFn,
  UpdateStandardApplicationModuleMainDetailsMutationHookResult,
  UpdateStandardApplicationModuleMainDetailsMutationResult,
  UpdateStandardApplicationModuleMainDetailsMutationOptions,
  UpdateStandardApplicationModuleEmailContentMutationVariables,
  UpdateStandardApplicationModuleEmailContentMutation,
  UpdateStandardApplicationModuleEmailContentMutationFn,
  UpdateStandardApplicationModuleEmailContentMutationHookResult,
  UpdateStandardApplicationModuleEmailContentMutationResult,
  UpdateStandardApplicationModuleEmailContentMutationOptions,
  UpdateStandardApplicationJourneyMutationVariables,
  UpdateStandardApplicationJourneyMutation,
  UpdateStandardApplicationJourneyMutationFn,
  UpdateStandardApplicationJourneyMutationHookResult,
  UpdateStandardApplicationJourneyMutationResult,
  UpdateStandardApplicationJourneyMutationOptions,
  UpdateStandardApplicationFinanceProductAssignmentsMutationVariables,
  UpdateStandardApplicationFinanceProductAssignmentsMutation,
  UpdateStandardApplicationFinanceProductAssignmentsMutationFn,
  UpdateStandardApplicationFinanceProductAssignmentsMutationHookResult,
  UpdateStandardApplicationFinanceProductAssignmentsMutationResult,
  UpdateStandardApplicationFinanceProductAssignmentsMutationOptions,
  UpdateStandardApplicationEntrypointMutationVariables,
  UpdateStandardApplicationEntrypointMutation,
  UpdateStandardApplicationEntrypointMutationFn,
  UpdateStandardApplicationEntrypointMutationHookResult,
  UpdateStandardApplicationEntrypointMutationResult,
  UpdateStandardApplicationEntrypointMutationOptions,
  UpdateStandardApplicationDraftMutationVariables,
  UpdateStandardApplicationDraftMutation,
  UpdateStandardApplicationDraftMutationFn,
  UpdateStandardApplicationDraftMutationHookResult,
  UpdateStandardApplicationDraftMutationResult,
  UpdateStandardApplicationDraftMutationOptions,
  UpdateStandardApplicationConfigurationMutationVariables,
  UpdateStandardApplicationConfigurationMutation,
  UpdateStandardApplicationConfigurationMutationFn,
  UpdateStandardApplicationConfigurationMutationHookResult,
  UpdateStandardApplicationConfigurationMutationResult,
  UpdateStandardApplicationConfigurationMutationOptions,
  UpdateSimpleVehicleManagementModuleMutationVariables,
  UpdateSimpleVehicleManagementModuleMutation,
  UpdateSimpleVehicleManagementModuleMutationFn,
  UpdateSimpleVehicleManagementModuleMutationHookResult,
  UpdateSimpleVehicleManagementModuleMutationResult,
  UpdateSimpleVehicleManagementModuleMutationOptions,
  UpdateSalesOfferModuleEmailContentMutationVariables,
  UpdateSalesOfferModuleEmailContentMutation,
  UpdateSalesOfferModuleEmailContentMutationFn,
  UpdateSalesOfferModuleEmailContentMutationHookResult,
  UpdateSalesOfferModuleEmailContentMutationResult,
  UpdateSalesOfferModuleEmailContentMutationOptions,
  UpdateSalesOfferModuleMutationVariables,
  UpdateSalesOfferModuleMutation,
  UpdateSalesOfferModuleMutationFn,
  UpdateSalesOfferModuleMutationHookResult,
  UpdateSalesOfferModuleMutationResult,
  UpdateSalesOfferModuleMutationOptions,
  UpdateSalesControlBoardModuleByDealerMutationVariables,
  UpdateSalesControlBoardModuleByDealerMutation,
  UpdateSalesControlBoardModuleByDealerMutationFn,
  UpdateSalesControlBoardModuleByDealerMutationHookResult,
  UpdateSalesControlBoardModuleByDealerMutationResult,
  UpdateSalesControlBoardModuleByDealerMutationOptions,
  UpdateSalesControlBoardModuleMutationVariables,
  UpdateSalesControlBoardModuleMutation,
  UpdateSalesControlBoardModuleMutationFn,
  UpdateSalesControlBoardModuleMutationHookResult,
  UpdateSalesControlBoardModuleMutationResult,
  UpdateSalesControlBoardModuleMutationOptions,
  UpdateRouterPathScriptsMutationVariables,
  UpdateRouterPathScriptsMutation,
  UpdateRouterPathScriptsMutationFn,
  UpdateRouterPathScriptsMutationHookResult,
  UpdateRouterPathScriptsMutationResult,
  UpdateRouterPathScriptsMutationOptions,
  UpdateRouterMenuMutationVariables,
  UpdateRouterMenuMutation,
  UpdateRouterMenuMutationFn,
  UpdateRouterMenuMutationHookResult,
  UpdateRouterMenuMutationResult,
  UpdateRouterMenuMutationOptions,
  UpdateRouterMutationVariables,
  UpdateRouterMutation,
  UpdateRouterMutationFn,
  UpdateRouterMutationHookResult,
  UpdateRouterMutationResult,
  UpdateRouterMutationOptions,
  UpdateRoleMutationVariables,
  UpdateRoleMutation,
  UpdateRoleMutationFn,
  UpdateRoleMutationHookResult,
  UpdateRoleMutationResult,
  UpdateRoleMutationOptions,
  UpdatePromoCodeModuleMutationVariables,
  UpdatePromoCodeModuleMutation,
  UpdatePromoCodeModuleMutationFn,
  UpdatePromoCodeModuleMutationHookResult,
  UpdatePromoCodeModuleMutationResult,
  UpdatePromoCodeModuleMutationOptions,
  UpdatePromoCodeMutationVariables,
  UpdatePromoCodeMutation,
  UpdatePromoCodeMutationFn,
  UpdatePromoCodeMutationHookResult,
  UpdatePromoCodeMutationResult,
  UpdatePromoCodeMutationOptions,
  UpdatePorscheRetainModuleMutationVariables,
  UpdatePorscheRetainModuleMutation,
  UpdatePorscheRetainModuleMutationFn,
  UpdatePorscheRetainModuleMutationHookResult,
  UpdatePorscheRetainModuleMutationResult,
  UpdatePorscheRetainModuleMutationOptions,
  UpdatePorschePaymentSettingsMutationVariables,
  UpdatePorschePaymentSettingsMutation,
  UpdatePorschePaymentSettingsMutationFn,
  UpdatePorschePaymentSettingsMutationHookResult,
  UpdatePorschePaymentSettingsMutationResult,
  UpdatePorschePaymentSettingsMutationOptions,
  UpdatePorschePaymentModuleMutationVariables,
  UpdatePorschePaymentModuleMutation,
  UpdatePorschePaymentModuleMutationFn,
  UpdatePorschePaymentModuleMutationHookResult,
  UpdatePorschePaymentModuleMutationResult,
  UpdatePorschePaymentModuleMutationOptions,
  UpdatePorscheMasterDataModuleMutationVariables,
  UpdatePorscheMasterDataModuleMutation,
  UpdatePorscheMasterDataModuleMutationFn,
  UpdatePorscheMasterDataModuleMutationHookResult,
  UpdatePorscheMasterDataModuleMutationResult,
  UpdatePorscheMasterDataModuleMutationOptions,
  UpdatePorscheIdModuleMutationVariables,
  UpdatePorscheIdModuleMutation,
  UpdatePorscheIdModuleMutationFn,
  UpdatePorscheIdModuleMutationHookResult,
  UpdatePorscheIdModuleMutationResult,
  UpdatePorscheIdModuleMutationOptions,
  UpdatePayGatePaymentSettingsMutationVariables,
  UpdatePayGatePaymentSettingsMutation,
  UpdatePayGatePaymentSettingsMutationFn,
  UpdatePayGatePaymentSettingsMutationHookResult,
  UpdatePayGatePaymentSettingsMutationResult,
  UpdatePayGatePaymentSettingsMutationOptions,
  UpdatePayGatePaymentModuleMutationVariables,
  UpdatePayGatePaymentModuleMutation,
  UpdatePayGatePaymentModuleMutationFn,
  UpdatePayGatePaymentModuleMutationHookResult,
  UpdatePayGatePaymentModuleMutationResult,
  UpdatePayGatePaymentModuleMutationOptions,
  UpdatePackageSettingMutationVariables,
  UpdatePackageSettingMutation,
  UpdatePackageSettingMutationFn,
  UpdatePackageSettingMutationHookResult,
  UpdatePackageSettingMutationResult,
  UpdatePackageSettingMutationOptions,
  UpdatePackageBlockMutationVariables,
  UpdatePackageBlockMutation,
  UpdatePackageBlockMutationFn,
  UpdatePackageBlockMutationHookResult,
  UpdatePackageBlockMutationResult,
  UpdatePackageBlockMutationOptions,
  UpdateOptionsBlockMutationVariables,
  UpdateOptionsBlockMutation,
  UpdateOptionsBlockMutationFn,
  UpdateOptionsBlockMutationHookResult,
  UpdateOptionsBlockMutationResult,
  UpdateOptionsBlockMutationOptions,
  UpdateOidcModuleMutationVariables,
  UpdateOidcModuleMutation,
  UpdateOidcModuleMutationFn,
  UpdateOidcModuleMutationHookResult,
  UpdateOidcModuleMutationResult,
  UpdateOidcModuleMutationOptions,
  UpdateOidcClientMutationVariables,
  UpdateOidcClientMutation,
  UpdateOidcClientMutationFn,
  UpdateOidcClientMutationHookResult,
  UpdateOidcClientMutationResult,
  UpdateOidcClientMutationOptions,
  UpdateNamirialSigningModuleMutationVariables,
  UpdateNamirialSigningModuleMutation,
  UpdateNamirialSigningModuleMutationFn,
  UpdateNamirialSigningModuleMutationHookResult,
  UpdateNamirialSigningModuleMutationResult,
  UpdateNamirialSigningModuleMutationOptions,
  UpdateNamirialSettingsMutationVariables,
  UpdateNamirialSettingsMutation,
  UpdateNamirialSettingsMutationFn,
  UpdateNamirialSettingsMutationHookResult,
  UpdateNamirialSettingsMutationResult,
  UpdateNamirialSettingsMutationOptions,
  UpdateMyInfoSettingMutationVariables,
  UpdateMyInfoSettingMutation,
  UpdateMyInfoSettingMutationFn,
  UpdateMyInfoSettingMutationHookResult,
  UpdateMyInfoSettingMutationResult,
  UpdateMyInfoSettingMutationOptions,
  UpdateMyInfoModuleMutationVariables,
  UpdateMyInfoModuleMutation,
  UpdateMyInfoModuleMutationFn,
  UpdateMyInfoModuleMutationHookResult,
  UpdateMyInfoModuleMutationResult,
  UpdateMyInfoModuleMutationOptions,
  UpdateModelConfiguratorMutationVariables,
  UpdateModelConfiguratorMutation,
  UpdateModelConfiguratorMutationFn,
  UpdateModelConfiguratorMutationHookResult,
  UpdateModelConfiguratorMutationResult,
  UpdateModelConfiguratorMutationOptions,
  UpdateModelMutationVariables,
  UpdateModelMutation,
  UpdateModelMutationFn,
  UpdateModelMutationHookResult,
  UpdateModelMutationResult,
  UpdateModelMutationOptions,
  UpdateMobilityModuleMutationVariables,
  UpdateMobilityModuleMutation,
  UpdateMobilityModuleMutationFn,
  UpdateMobilityModuleMutationHookResult,
  UpdateMobilityModuleMutationResult,
  UpdateMobilityModuleMutationOptions,
  UpdateMobilityEmailContentMutationVariables,
  UpdateMobilityEmailContentMutation,
  UpdateMobilityEmailContentMutationFn,
  UpdateMobilityEmailContentMutationHookResult,
  UpdateMobilityEmailContentMutationResult,
  UpdateMobilityEmailContentMutationOptions,
  UpdateMobilityDepositAmountMutationVariables,
  UpdateMobilityDepositAmountMutation,
  UpdateMobilityDepositAmountMutationFn,
  UpdateMobilityDepositAmountMutationHookResult,
  UpdateMobilityDepositAmountMutationResult,
  UpdateMobilityDepositAmountMutationOptions,
  UpdateMobilityApplicationEntrypointMutationVariables,
  UpdateMobilityApplicationEntrypointMutation,
  UpdateMobilityApplicationEntrypointMutationFn,
  UpdateMobilityApplicationEntrypointMutationHookResult,
  UpdateMobilityApplicationEntrypointMutationResult,
  UpdateMobilityApplicationEntrypointMutationOptions,
  UpdateMobilityApplicationDraftMutationVariables,
  UpdateMobilityApplicationDraftMutation,
  UpdateMobilityApplicationDraftMutationFn,
  UpdateMobilityApplicationDraftMutationHookResult,
  UpdateMobilityApplicationDraftMutationResult,
  UpdateMobilityApplicationDraftMutationOptions,
  UpdateMobilityApplicationMutationVariables,
  UpdateMobilityApplicationMutation,
  UpdateMobilityApplicationMutationFn,
  UpdateMobilityApplicationMutationHookResult,
  UpdateMobilityApplicationMutationResult,
  UpdateMobilityApplicationMutationOptions,
  UpdateMobilityAddonMutationVariables,
  UpdateMobilityAddonMutation,
  UpdateMobilityAddonMutationFn,
  UpdateMobilityAddonMutationHookResult,
  UpdateMobilityAddonMutationResult,
  UpdateMobilityAddonMutationOptions,
  UpdateMobilityAdditionalInfoMutationVariables,
  UpdateMobilityAdditionalInfoMutation,
  UpdateMobilityAdditionalInfoMutationFn,
  UpdateMobilityAdditionalInfoMutationHookResult,
  UpdateMobilityAdditionalInfoMutationResult,
  UpdateMobilityAdditionalInfoMutationOptions,
  UpdateMaybankIntegrationSettingMutationVariables,
  UpdateMaybankIntegrationSettingMutation,
  UpdateMaybankIntegrationSettingMutationFn,
  UpdateMaybankIntegrationSettingMutationHookResult,
  UpdateMaybankIntegrationSettingMutationResult,
  UpdateMaybankIntegrationSettingMutationOptions,
  UpdateMarketingModuleMutationVariables,
  UpdateMarketingModuleMutation,
  UpdateMarketingModuleMutationFn,
  UpdateMarketingModuleMutationHookResult,
  UpdateMarketingModuleMutationResult,
  UpdateMarketingModuleMutationOptions,
  UpdateMarketingConsentsAndDeclarationsMutationVariables,
  UpdateMarketingConsentsAndDeclarationsMutation,
  UpdateMarketingConsentsAndDeclarationsMutationFn,
  UpdateMarketingConsentsAndDeclarationsMutationHookResult,
  UpdateMarketingConsentsAndDeclarationsMutationResult,
  UpdateMarketingConsentsAndDeclarationsMutationOptions,
  UpdateMakeMutationVariables,
  UpdateMakeMutation,
  UpdateMakeMutationFn,
  UpdateMakeMutationHookResult,
  UpdateMakeMutationResult,
  UpdateMakeMutationOptions,
  UpdateMaintenanceModuleDetailsMutationVariables,
  UpdateMaintenanceModuleDetailsMutation,
  UpdateMaintenanceModuleDetailsMutationFn,
  UpdateMaintenanceModuleDetailsMutationHookResult,
  UpdateMaintenanceModuleDetailsMutationResult,
  UpdateMaintenanceModuleDetailsMutationOptions,
  UpdateMaintenanceModuleMutationVariables,
  UpdateMaintenanceModuleMutation,
  UpdateMaintenanceModuleMutationFn,
  UpdateMaintenanceModuleMutationHookResult,
  UpdateMaintenanceModuleMutationResult,
  UpdateMaintenanceModuleMutationOptions,
  UpdateMainDetailsSalesOfferMutationVariables,
  UpdateMainDetailsSalesOfferMutation,
  UpdateMainDetailsSalesOfferMutationFn,
  UpdateMainDetailsSalesOfferMutationHookResult,
  UpdateMainDetailsSalesOfferMutationResult,
  UpdateMainDetailsSalesOfferMutationOptions,
  UpdateLocalCustomerManagementKycFieldsMutationVariables,
  UpdateLocalCustomerManagementKycFieldsMutation,
  UpdateLocalCustomerManagementKycFieldsMutationFn,
  UpdateLocalCustomerManagementKycFieldsMutationHookResult,
  UpdateLocalCustomerManagementKycFieldsMutationResult,
  UpdateLocalCustomerManagementKycFieldsMutationOptions,
  UpdateLocalCustomerManagementMutationVariables,
  UpdateLocalCustomerManagementMutation,
  UpdateLocalCustomerManagementMutationFn,
  UpdateLocalCustomerManagementMutationHookResult,
  UpdateLocalCustomerManagementMutationResult,
  UpdateLocalCustomerManagementMutationOptions,
  UpdateLeadListEndpointMutationVariables,
  UpdateLeadListEndpointMutation,
  UpdateLeadListEndpointMutationFn,
  UpdateLeadListEndpointMutationHookResult,
  UpdateLeadListEndpointMutationResult,
  UpdateLeadListEndpointMutationOptions,
  UpdateLeadFollowUpMutationVariables,
  UpdateLeadFollowUpMutation,
  UpdateLeadFollowUpMutationFn,
  UpdateLeadFollowUpMutationHookResult,
  UpdateLeadFollowUpMutationResult,
  UpdateLeadFollowUpMutationOptions,
  UpdateLeadMutationVariables,
  UpdateLeadMutation,
  UpdateLeadMutationFn,
  UpdateLeadMutationHookResult,
  UpdateLeadMutationResult,
  UpdateLeadMutationOptions,
  UpdateLaunchpadApplicationTradeInMutationVariables,
  UpdateLaunchpadApplicationTradeInMutation,
  UpdateLaunchpadApplicationTradeInMutationFn,
  UpdateLaunchpadApplicationTradeInMutationHookResult,
  UpdateLaunchpadApplicationTradeInMutationResult,
  UpdateLaunchpadApplicationTradeInMutationOptions,
  UpdateLaunchPadModuleVehicleAssignmentsMutationVariables,
  UpdateLaunchPadModuleVehicleAssignmentsMutation,
  UpdateLaunchPadModuleVehicleAssignmentsMutationFn,
  UpdateLaunchPadModuleVehicleAssignmentsMutationHookResult,
  UpdateLaunchPadModuleVehicleAssignmentsMutationResult,
  UpdateLaunchPadModuleVehicleAssignmentsMutationOptions,
  UpdateLaunchPadModuleMutationVariables,
  UpdateLaunchPadModuleMutation,
  UpdateLaunchPadModuleMutationFn,
  UpdateLaunchPadModuleMutationHookResult,
  UpdateLaunchPadModuleMutationResult,
  UpdateLaunchPadModuleMutationOptions,
  UpdateLaunchPadApplicationEntrypointMutationVariables,
  UpdateLaunchPadApplicationEntrypointMutation,
  UpdateLaunchPadApplicationEntrypointMutationFn,
  UpdateLaunchPadApplicationEntrypointMutationHookResult,
  UpdateLaunchPadApplicationEntrypointMutationResult,
  UpdateLaunchPadApplicationEntrypointMutationOptions,
  UpdateLanguagePackMutationVariables,
  UpdateLanguagePackMutation,
  UpdateLanguagePackMutationFn,
  UpdateLanguagePackMutationHookResult,
  UpdateLanguagePackMutationResult,
  UpdateLanguagePackMutationOptions,
  UpdateLabelsModuleMutationVariables,
  UpdateLabelsModuleMutation,
  UpdateLabelsModuleMutationFn,
  UpdateLabelsModuleMutationHookResult,
  UpdateLabelsModuleMutationResult,
  UpdateLabelsModuleMutationOptions,
  UpdateLabelsMutationVariables,
  UpdateLabelsMutation,
  UpdateLabelsMutationFn,
  UpdateLabelsMutationHookResult,
  UpdateLabelsMutationResult,
  UpdateLabelsMutationOptions,
  UpdateKycPresetMutationVariables,
  UpdateKycPresetMutation,
  UpdateKycPresetMutationFn,
  UpdateKycPresetMutationHookResult,
  UpdateKycPresetMutationResult,
  UpdateKycPresetMutationOptions,
  UpdateInventoryMutationVariables,
  UpdateInventoryMutation,
  UpdateInventoryMutationFn,
  UpdateInventoryMutationHookResult,
  UpdateInventoryMutationResult,
  UpdateInventoryMutationOptions,
  UpdateInsurerMutationVariables,
  UpdateInsurerMutation,
  UpdateInsurerMutationFn,
  UpdateInsurerMutationHookResult,
  UpdateInsurerMutationResult,
  UpdateInsurerMutationOptions,
  UpdateInsuranceSalesOfferMutationVariables,
  UpdateInsuranceSalesOfferMutation,
  UpdateInsuranceSalesOfferMutationFn,
  UpdateInsuranceSalesOfferMutationHookResult,
  UpdateInsuranceSalesOfferMutationResult,
  UpdateInsuranceSalesOfferMutationOptions,
  UpdateInsuranceProductMutationVariables,
  UpdateInsuranceProductMutation,
  UpdateInsuranceProductMutationFn,
  UpdateInsuranceProductMutationHookResult,
  UpdateInsuranceProductMutationResult,
  UpdateInsuranceProductMutationOptions,
  UpdateInsuranceModuleMutationVariables,
  UpdateInsuranceModuleMutation,
  UpdateInsuranceModuleMutationFn,
  UpdateInsuranceModuleMutationHookResult,
  UpdateInsuranceModuleMutationResult,
  UpdateInsuranceModuleMutationOptions,
  UpdateHlfBankV2IntegrationSettingMutationVariables,
  UpdateHlfBankV2IntegrationSettingMutation,
  UpdateHlfBankV2IntegrationSettingMutationFn,
  UpdateHlfBankV2IntegrationSettingMutationHookResult,
  UpdateHlfBankV2IntegrationSettingMutationResult,
  UpdateHlfBankV2IntegrationSettingMutationOptions,
  UpdateHlfBankIntegrationSettingMutationVariables,
  UpdateHlfBankIntegrationSettingMutation,
  UpdateHlfBankIntegrationSettingMutationFn,
  UpdateHlfBankIntegrationSettingMutationHookResult,
  UpdateHlfBankIntegrationSettingMutationResult,
  UpdateHlfBankIntegrationSettingMutationOptions,
  UpdateGroupConsentsAndDeclarationsMutationVariables,
  UpdateGroupConsentsAndDeclarationsMutation,
  UpdateGroupConsentsAndDeclarationsMutationFn,
  UpdateGroupConsentsAndDeclarationsMutationHookResult,
  UpdateGroupConsentsAndDeclarationsMutationResult,
  UpdateGroupConsentsAndDeclarationsMutationOptions,
  UpdateGiftVoucherModuleEmailContentMutationVariables,
  UpdateGiftVoucherModuleEmailContentMutation,
  UpdateGiftVoucherModuleEmailContentMutationFn,
  UpdateGiftVoucherModuleEmailContentMutationHookResult,
  UpdateGiftVoucherModuleEmailContentMutationResult,
  UpdateGiftVoucherModuleEmailContentMutationOptions,
  UpdateGiftVoucherModuleMutationVariables,
  UpdateGiftVoucherModuleMutation,
  UpdateGiftVoucherModuleMutationFn,
  UpdateGiftVoucherModuleMutationHookResult,
  UpdateGiftVoucherModuleMutationResult,
  UpdateGiftVoucherModuleMutationOptions,
  UpdateGiftVoucherMutationVariables,
  UpdateGiftVoucherMutation,
  UpdateGiftVoucherMutationFn,
  UpdateGiftVoucherMutationHookResult,
  UpdateGiftVoucherMutationResult,
  UpdateGiftVoucherMutationOptions,
  UpdateFiservPaymentSettingsMutationVariables,
  UpdateFiservPaymentSettingsMutation,
  UpdateFiservPaymentSettingsMutationFn,
  UpdateFiservPaymentSettingsMutationHookResult,
  UpdateFiservPaymentSettingsMutationResult,
  UpdateFiservPaymentSettingsMutationOptions,
  UpdateFiservPaymentModuleMutationVariables,
  UpdateFiservPaymentModuleMutation,
  UpdateFiservPaymentModuleMutationFn,
  UpdateFiservPaymentModuleMutationHookResult,
  UpdateFiservPaymentModuleMutationResult,
  UpdateFiservPaymentModuleMutationOptions,
  UpdateFinderVehicleManagementModuleMutationVariables,
  UpdateFinderVehicleManagementModuleMutation,
  UpdateFinderVehicleManagementModuleMutationFn,
  UpdateFinderVehicleManagementModuleMutationHookResult,
  UpdateFinderVehicleManagementModuleMutationResult,
  UpdateFinderVehicleManagementModuleMutationOptions,
  UpdateFinderVehicleMutationVariables,
  UpdateFinderVehicleMutation,
  UpdateFinderVehicleMutationFn,
  UpdateFinderVehicleMutationHookResult,
  UpdateFinderVehicleMutationResult,
  UpdateFinderVehicleMutationOptions,
  UpdateFinderApplicationPublicModuleEmailContentsMutationVariables,
  UpdateFinderApplicationPublicModuleEmailContentsMutation,
  UpdateFinderApplicationPublicModuleEmailContentsMutationFn,
  UpdateFinderApplicationPublicModuleEmailContentsMutationHookResult,
  UpdateFinderApplicationPublicModuleEmailContentsMutationResult,
  UpdateFinderApplicationPublicModuleEmailContentsMutationOptions,
  UpdateFinderApplicationPublicAccessEntrypointMutationVariables,
  UpdateFinderApplicationPublicAccessEntrypointMutation,
  UpdateFinderApplicationPublicAccessEntrypointMutationFn,
  UpdateFinderApplicationPublicAccessEntrypointMutationHookResult,
  UpdateFinderApplicationPublicAccessEntrypointMutationResult,
  UpdateFinderApplicationPublicAccessEntrypointMutationOptions,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationVariables,
  UpdateFinderApplicationPrivateModuleEmailContentsMutation,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationFn,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationHookResult,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationResult,
  UpdateFinderApplicationPrivateModuleEmailContentsMutationOptions,
  UpdateFinderApplicationPrivateModuleMutationVariables,
  UpdateFinderApplicationPrivateModuleMutation,
  UpdateFinderApplicationPrivateModuleMutationFn,
  UpdateFinderApplicationPrivateModuleMutationHookResult,
  UpdateFinderApplicationPrivateModuleMutationResult,
  UpdateFinderApplicationPrivateModuleMutationOptions,
  UpdateFinderApplicationPublicModuleMutationVariables,
  UpdateFinderApplicationPublicModuleMutation,
  UpdateFinderApplicationPublicModuleMutationFn,
  UpdateFinderApplicationPublicModuleMutationHookResult,
  UpdateFinderApplicationPublicModuleMutationResult,
  UpdateFinderApplicationPublicModuleMutationOptions,
  UpdateFinderApplicationJourneyMutationVariables,
  UpdateFinderApplicationJourneyMutation,
  UpdateFinderApplicationJourneyMutationFn,
  UpdateFinderApplicationJourneyMutationHookResult,
  UpdateFinderApplicationJourneyMutationResult,
  UpdateFinderApplicationJourneyMutationOptions,
  UpdateFinderApplicationEntrypointMutationVariables,
  UpdateFinderApplicationEntrypointMutation,
  UpdateFinderApplicationEntrypointMutationFn,
  UpdateFinderApplicationEntrypointMutationHookResult,
  UpdateFinderApplicationEntrypointMutationResult,
  UpdateFinderApplicationEntrypointMutationOptions,
  UpdateFinderApplicationDraftMutationVariables,
  UpdateFinderApplicationDraftMutation,
  UpdateFinderApplicationDraftMutationFn,
  UpdateFinderApplicationDraftMutationHookResult,
  UpdateFinderApplicationDraftMutationResult,
  UpdateFinderApplicationDraftMutationOptions,
  UpdateFinderApplicationMutationVariables,
  UpdateFinderApplicationMutation,
  UpdateFinderApplicationMutationFn,
  UpdateFinderApplicationMutationHookResult,
  UpdateFinderApplicationMutationResult,
  UpdateFinderApplicationMutationOptions,
  UpdateFinanceSalesOfferMutationVariables,
  UpdateFinanceSalesOfferMutation,
  UpdateFinanceSalesOfferMutationFn,
  UpdateFinanceSalesOfferMutationHookResult,
  UpdateFinanceSalesOfferMutationResult,
  UpdateFinanceSalesOfferMutationOptions,
  UpdateFinanceProductMutationVariables,
  UpdateFinanceProductMutation,
  UpdateFinanceProductMutationFn,
  UpdateFinanceProductMutationHookResult,
  UpdateFinanceProductMutationResult,
  UpdateFinanceProductMutationOptions,
  UpdateEventApplicationVehicleAssignmentsMutationVariables,
  UpdateEventApplicationVehicleAssignmentsMutation,
  UpdateEventApplicationVehicleAssignmentsMutationFn,
  UpdateEventApplicationVehicleAssignmentsMutationHookResult,
  UpdateEventApplicationVehicleAssignmentsMutationResult,
  UpdateEventApplicationVehicleAssignmentsMutationOptions,
  UpdateEventApplicationModuleMainDetailsMutationVariables,
  UpdateEventApplicationModuleMainDetailsMutation,
  UpdateEventApplicationModuleMainDetailsMutationFn,
  UpdateEventApplicationModuleMainDetailsMutationHookResult,
  UpdateEventApplicationModuleMainDetailsMutationResult,
  UpdateEventApplicationModuleMainDetailsMutationOptions,
  UpdateEventApplicationModuleEmailContentsMutationVariables,
  UpdateEventApplicationModuleEmailContentsMutation,
  UpdateEventApplicationModuleEmailContentsMutationFn,
  UpdateEventApplicationModuleEmailContentsMutationHookResult,
  UpdateEventApplicationModuleEmailContentsMutationResult,
  UpdateEventApplicationModuleEmailContentsMutationOptions,
  UpdateEventApplicationFinanceProductAssignmentsMutationVariables,
  UpdateEventApplicationFinanceProductAssignmentsMutation,
  UpdateEventApplicationFinanceProductAssignmentsMutationFn,
  UpdateEventApplicationFinanceProductAssignmentsMutationHookResult,
  UpdateEventApplicationFinanceProductAssignmentsMutationResult,
  UpdateEventApplicationFinanceProductAssignmentsMutationOptions,
  UpdateEventApplicationEntrypointMutationVariables,
  UpdateEventApplicationEntrypointMutation,
  UpdateEventApplicationEntrypointMutationFn,
  UpdateEventApplicationEntrypointMutationHookResult,
  UpdateEventApplicationEntrypointMutationResult,
  UpdateEventApplicationEntrypointMutationOptions,
  UpdateEventApplicationMutationVariables,
  UpdateEventApplicationMutation,
  UpdateEventApplicationMutationFn,
  UpdateEventApplicationMutationHookResult,
  UpdateEventApplicationMutationResult,
  UpdateEventApplicationMutationOptions,
  UpdateEventMutationVariables,
  UpdateEventMutation,
  UpdateEventMutationFn,
  UpdateEventMutationHookResult,
  UpdateEventMutationResult,
  UpdateEventMutationOptions,
  UpdateEnbdBankIntegrationSettingMutationVariables,
  UpdateEnbdBankIntegrationSettingMutation,
  UpdateEnbdBankIntegrationSettingMutationFn,
  UpdateEnbdBankIntegrationSettingMutationHookResult,
  UpdateEnbdBankIntegrationSettingMutationResult,
  UpdateEnbdBankIntegrationSettingMutationOptions,
  UpdateEmailInsurerIntegrationSettingMutationVariables,
  UpdateEmailInsurerIntegrationSettingMutation,
  UpdateEmailInsurerIntegrationSettingMutationFn,
  UpdateEmailInsurerIntegrationSettingMutationHookResult,
  UpdateEmailInsurerIntegrationSettingMutationResult,
  UpdateEmailInsurerIntegrationSettingMutationOptions,
  UpdateEmailFromTokenMutationVariables,
  UpdateEmailFromTokenMutation,
  UpdateEmailFromTokenMutationFn,
  UpdateEmailFromTokenMutationHookResult,
  UpdateEmailFromTokenMutationResult,
  UpdateEmailFromTokenMutationOptions,
  UpdateEmailBankIntegrationSettingMutationVariables,
  UpdateEmailBankIntegrationSettingMutation,
  UpdateEmailBankIntegrationSettingMutationFn,
  UpdateEmailBankIntegrationSettingMutationHookResult,
  UpdateEmailBankIntegrationSettingMutationResult,
  UpdateEmailBankIntegrationSettingMutationOptions,
  UpdateEdmEmailSocialMediaMutationVariables,
  UpdateEdmEmailSocialMediaMutation,
  UpdateEdmEmailSocialMediaMutationFn,
  UpdateEdmEmailSocialMediaMutationHookResult,
  UpdateEdmEmailSocialMediaMutationResult,
  UpdateEdmEmailSocialMediaMutationOptions,
  UpdateEazyInsurerIntegrationSettingMutationVariables,
  UpdateEazyInsurerIntegrationSettingMutation,
  UpdateEazyInsurerIntegrationSettingMutationFn,
  UpdateEazyInsurerIntegrationSettingMutationHookResult,
  UpdateEazyInsurerIntegrationSettingMutationResult,
  UpdateEazyInsurerIntegrationSettingMutationOptions,
  UpdateDummyWelcomePageEndpointMutationVariables,
  UpdateDummyWelcomePageEndpointMutation,
  UpdateDummyWelcomePageEndpointMutationFn,
  UpdateDummyWelcomePageEndpointMutationHookResult,
  UpdateDummyWelcomePageEndpointMutationResult,
  UpdateDummyWelcomePageEndpointMutationOptions,
  UpdateDummyPrivatePageEndpointMutationVariables,
  UpdateDummyPrivatePageEndpointMutation,
  UpdateDummyPrivatePageEndpointMutationFn,
  UpdateDummyPrivatePageEndpointMutationHookResult,
  UpdateDummyPrivatePageEndpointMutationResult,
  UpdateDummyPrivatePageEndpointMutationOptions,
  UpdateDocusignSettingMutationVariables,
  UpdateDocusignSettingMutation,
  UpdateDocusignSettingMutationFn,
  UpdateDocusignSettingMutationHookResult,
  UpdateDocusignSettingMutationResult,
  UpdateDocusignSettingMutationOptions,
  UpdateDocusignModuleMutationVariables,
  UpdateDocusignModuleMutation,
  UpdateDocusignModuleMutationFn,
  UpdateDocusignModuleMutationHookResult,
  UpdateDocusignModuleMutationResult,
  UpdateDocusignModuleMutationOptions,
  UpdateDepositSalesOfferMutationVariables,
  UpdateDepositSalesOfferMutation,
  UpdateDepositSalesOfferMutationFn,
  UpdateDepositSalesOfferMutationHookResult,
  UpdateDepositSalesOfferMutationResult,
  UpdateDepositSalesOfferMutationOptions,
  UpdateDealerSocialMediaMutationVariables,
  UpdateDealerSocialMediaMutation,
  UpdateDealerSocialMediaMutationFn,
  UpdateDealerSocialMediaMutationHookResult,
  UpdateDealerSocialMediaMutationResult,
  UpdateDealerSocialMediaMutationOptions,
  UpdateDealerMutationVariables,
  UpdateDealerMutation,
  UpdateDealerMutationFn,
  UpdateDealerMutationHookResult,
  UpdateDealerMutationResult,
  UpdateDealerMutationOptions,
  UpdateDbsBankIntegrationSettingMutationVariables,
  UpdateDbsBankIntegrationSettingMutation,
  UpdateDbsBankIntegrationSettingMutationFn,
  UpdateDbsBankIntegrationSettingMutationHookResult,
  UpdateDbsBankIntegrationSettingMutationResult,
  UpdateDbsBankIntegrationSettingMutationOptions,
  UpdateCustomerListEndpointMutationVariables,
  UpdateCustomerListEndpointMutation,
  UpdateCustomerListEndpointMutationFn,
  UpdateCustomerListEndpointMutationHookResult,
  UpdateCustomerListEndpointMutationResult,
  UpdateCustomerListEndpointMutationOptions,
  UpdateCustomerMutationVariables,
  UpdateCustomerMutation,
  UpdateCustomerMutationFn,
  UpdateCustomerMutationHookResult,
  UpdateCustomerMutationResult,
  UpdateCustomerMutationOptions,
  UpdateCtsModuleSettingMutationVariables,
  UpdateCtsModuleSettingMutation,
  UpdateCtsModuleSettingMutationFn,
  UpdateCtsModuleSettingMutationHookResult,
  UpdateCtsModuleSettingMutationResult,
  UpdateCtsModuleSettingMutationOptions,
  UpdateCtsModuleMutationVariables,
  UpdateCtsModuleMutation,
  UpdateCtsModuleMutationFn,
  UpdateCtsModuleMutationHookResult,
  UpdateCtsModuleMutationResult,
  UpdateCtsModuleMutationOptions,
  UpdateCtsInsuranceProductsMutationVariables,
  UpdateCtsInsuranceProductsMutation,
  UpdateCtsInsuranceProductsMutationFn,
  UpdateCtsInsuranceProductsMutationHookResult,
  UpdateCtsInsuranceProductsMutationResult,
  UpdateCtsInsuranceProductsMutationOptions,
  UpdateCtsFinanceProductsMutationVariables,
  UpdateCtsFinanceProductsMutation,
  UpdateCtsFinanceProductsMutationFn,
  UpdateCtsFinanceProductsMutationHookResult,
  UpdateCtsFinanceProductsMutationResult,
  UpdateCtsFinanceProductsMutationOptions,
  UpdateConsentsAndDeclarationsModuleMutationVariables,
  UpdateConsentsAndDeclarationsModuleMutation,
  UpdateConsentsAndDeclarationsModuleMutationFn,
  UpdateConsentsAndDeclarationsModuleMutationHookResult,
  UpdateConsentsAndDeclarationsModuleMutationResult,
  UpdateConsentsAndDeclarationsModuleMutationOptions,
  UpdateConsentOrderListMutationVariables,
  UpdateConsentOrderListMutation,
  UpdateConsentOrderListMutationFn,
  UpdateConsentOrderListMutationHookResult,
  UpdateConsentOrderListMutationResult,
  UpdateConsentOrderListMutationOptions,
  UpdateConfiguratorModuleEmailContentsMutationVariables,
  UpdateConfiguratorModuleEmailContentsMutation,
  UpdateConfiguratorModuleEmailContentsMutationFn,
  UpdateConfiguratorModuleEmailContentsMutationHookResult,
  UpdateConfiguratorModuleEmailContentsMutationResult,
  UpdateConfiguratorModuleEmailContentsMutationOptions,
  UpdateConfiguratorModuleMutationVariables,
  UpdateConfiguratorModuleMutation,
  UpdateConfiguratorModuleMutationFn,
  UpdateConfiguratorModuleMutationHookResult,
  UpdateConfiguratorModuleMutationResult,
  UpdateConfiguratorModuleMutationOptions,
  UpdateConfiguratorApplicationJourneyMutationVariables,
  UpdateConfiguratorApplicationJourneyMutation,
  UpdateConfiguratorApplicationJourneyMutationFn,
  UpdateConfiguratorApplicationJourneyMutationHookResult,
  UpdateConfiguratorApplicationJourneyMutationResult,
  UpdateConfiguratorApplicationJourneyMutationOptions,
  UpdateConfiguratorApplicationEntrypointMutationVariables,
  UpdateConfiguratorApplicationEntrypointMutation,
  UpdateConfiguratorApplicationEntrypointMutationFn,
  UpdateConfiguratorApplicationEntrypointMutationHookResult,
  UpdateConfiguratorApplicationEntrypointMutationResult,
  UpdateConfiguratorApplicationEntrypointMutationOptions,
  UpdateConfiguratorApplicationConfigurationMutationVariables,
  UpdateConfiguratorApplicationConfigurationMutation,
  UpdateConfiguratorApplicationConfigurationMutationFn,
  UpdateConfiguratorApplicationConfigurationMutationHookResult,
  UpdateConfiguratorApplicationConfigurationMutationResult,
  UpdateConfiguratorApplicationConfigurationMutationOptions,
  UpdateConfiguratorApplicationMutationVariables,
  UpdateConfiguratorApplicationMutation,
  UpdateConfiguratorApplicationMutationFn,
  UpdateConfiguratorApplicationMutationHookResult,
  UpdateConfiguratorApplicationMutationResult,
  UpdateConfiguratorApplicationMutationOptions,
  UpdateCompanyMutationVariables,
  UpdateCompanyMutation,
  UpdateCompanyMutationFn,
  UpdateCompanyMutationHookResult,
  UpdateCompanyMutationResult,
  UpdateCompanyMutationOptions,
  UpdateColorBlockMutationVariables,
  UpdateColorBlockMutation,
  UpdateColorBlockMutationFn,
  UpdateColorBlockMutationHookResult,
  UpdateColorBlockMutationResult,
  UpdateColorBlockMutationOptions,
  UpdateCheckboxConsentsAndDeclarationsMutationVariables,
  UpdateCheckboxConsentsAndDeclarationsMutation,
  UpdateCheckboxConsentsAndDeclarationsMutationFn,
  UpdateCheckboxConsentsAndDeclarationsMutationHookResult,
  UpdateCheckboxConsentsAndDeclarationsMutationResult,
  UpdateCheckboxConsentsAndDeclarationsMutationOptions,
  UpdateCapModuleMutationVariables,
  UpdateCapModuleMutation,
  UpdateCapModuleMutationFn,
  UpdateCapModuleMutationHookResult,
  UpdateCapModuleMutationResult,
  UpdateCapModuleMutationOptions,
  UpdateCampaignMutationVariables,
  UpdateCampaignMutation,
  UpdateCampaignMutationFn,
  UpdateCampaignMutationHookResult,
  UpdateCampaignMutationResult,
  UpdateCampaignMutationOptions,
  UpdateBasicSigningModuleMutationVariables,
  UpdateBasicSigningModuleMutation,
  UpdateBasicSigningModuleMutationFn,
  UpdateBasicSigningModuleMutationHookResult,
  UpdateBasicSigningModuleMutationResult,
  UpdateBasicSigningModuleMutationOptions,
  UpdateBannerMutationVariables,
  UpdateBannerMutation,
  UpdateBannerMutationFn,
  UpdateBannerMutationHookResult,
  UpdateBannerMutationResult,
  UpdateBannerMutationOptions,
  UpdateBankModuleMutationVariables,
  UpdateBankModuleMutation,
  UpdateBankModuleMutationFn,
  UpdateBankModuleMutationHookResult,
  UpdateBankModuleMutationResult,
  UpdateBankModuleMutationOptions,
  UpdateBankAvailableFinanceProductTypesMutationVariables,
  UpdateBankAvailableFinanceProductTypesMutation,
  UpdateBankAvailableFinanceProductTypesMutationFn,
  UpdateBankAvailableFinanceProductTypesMutationHookResult,
  UpdateBankAvailableFinanceProductTypesMutationResult,
  UpdateBankAvailableFinanceProductTypesMutationOptions,
  UpdateBankMutationVariables,
  UpdateBankMutation,
  UpdateBankMutationFn,
  UpdateBankMutationHookResult,
  UpdateBankMutationResult,
  UpdateBankMutationOptions,
  UpdateAutoplayModuleSettingMutationVariables,
  UpdateAutoplayModuleSettingMutation,
  UpdateAutoplayModuleSettingMutationFn,
  UpdateAutoplayModuleSettingMutationHookResult,
  UpdateAutoplayModuleSettingMutationResult,
  UpdateAutoplayModuleSettingMutationOptions,
  UpdateAutoplayModuleMutationVariables,
  UpdateAutoplayModuleMutation,
  UpdateAutoplayModuleMutationFn,
  UpdateAutoplayModuleMutationHookResult,
  UpdateAutoplayModuleMutationResult,
  UpdateAutoplayModuleMutationOptions,
  UpdateAssigneeOnLeadMutationVariables,
  UpdateAssigneeOnLeadMutation,
  UpdateAssigneeOnLeadMutationFn,
  UpdateAssigneeOnLeadMutationHookResult,
  UpdateAssigneeOnLeadMutationResult,
  UpdateAssigneeOnLeadMutationOptions,
  UpdateAssigneeOnApplicationMutationVariables,
  UpdateAssigneeOnApplicationMutation,
  UpdateAssigneeOnApplicationMutationFn,
  UpdateAssigneeOnApplicationMutationHookResult,
  UpdateAssigneeOnApplicationMutationResult,
  UpdateAssigneeOnApplicationMutationOptions,
  UpdateAppointmentModuleEmailContentMutationVariables,
  UpdateAppointmentModuleEmailContentMutation,
  UpdateAppointmentModuleEmailContentMutationFn,
  UpdateAppointmentModuleEmailContentMutationHookResult,
  UpdateAppointmentModuleEmailContentMutationResult,
  UpdateAppointmentModuleEmailContentMutationOptions,
  UpdateAppointmentModuleMutationVariables,
  UpdateAppointmentModuleMutation,
  UpdateAppointmentModuleMutationFn,
  UpdateAppointmentModuleMutationHookResult,
  UpdateAppointmentModuleMutationResult,
  UpdateAppointmentModuleMutationOptions,
  UpdateAppointmentDataMutationVariables,
  UpdateAppointmentDataMutation,
  UpdateAppointmentDataMutationFn,
  UpdateAppointmentDataMutationHookResult,
  UpdateAppointmentDataMutationResult,
  UpdateAppointmentDataMutationOptions,
  UpdateApplicationListEndpointMutationVariables,
  UpdateApplicationListEndpointMutation,
  UpdateApplicationListEndpointMutationFn,
  UpdateApplicationListEndpointMutationHookResult,
  UpdateApplicationListEndpointMutationResult,
  UpdateApplicationListEndpointMutationOptions,
  UpdateApplicationFieldsMutationVariables,
  UpdateApplicationFieldsMutation,
  UpdateApplicationFieldsMutationFn,
  UpdateApplicationFieldsMutationHookResult,
  UpdateApplicationFieldsMutationResult,
  UpdateApplicationFieldsMutationOptions,
  UpdateApplicationDealershipAssignmentsByDealerMutationVariables,
  UpdateApplicationDealershipAssignmentsByDealerMutation,
  UpdateApplicationDealershipAssignmentsByDealerMutationFn,
  UpdateApplicationDealershipAssignmentsByDealerMutationHookResult,
  UpdateApplicationDealershipAssignmentsByDealerMutationResult,
  UpdateApplicationDealershipAssignmentsByDealerMutationOptions,
  UpdateApplicationMutationVariables,
  UpdateApplicationMutation,
  UpdateApplicationMutationFn,
  UpdateApplicationMutationHookResult,
  UpdateApplicationMutationResult,
  UpdateApplicationMutationOptions,
  UpdateApplicantVisitAppointmentMutationVariables,
  UpdateApplicantVisitAppointmentMutation,
  UpdateApplicantVisitAppointmentMutationFn,
  UpdateApplicantVisitAppointmentMutationHookResult,
  UpdateApplicantVisitAppointmentMutationResult,
  UpdateApplicantVisitAppointmentMutationOptions,
  UpdateApplicantAppointmentMutationVariables,
  UpdateApplicantAppointmentMutation,
  UpdateApplicantAppointmentMutationFn,
  UpdateApplicantAppointmentMutationHookResult,
  UpdateApplicantAppointmentMutationResult,
  UpdateApplicantAppointmentMutationOptions,
  UpdateAdyenPaymentSettingsMutationVariables,
  UpdateAdyenPaymentSettingsMutation,
  UpdateAdyenPaymentSettingsMutationFn,
  UpdateAdyenPaymentSettingsMutationHookResult,
  UpdateAdyenPaymentSettingsMutationResult,
  UpdateAdyenPaymentSettingsMutationOptions,
  UpdateAdyenPaymentModuleMutationVariables,
  UpdateAdyenPaymentModuleMutation,
  UpdateAdyenPaymentModuleMutationFn,
  UpdateAdyenPaymentModuleMutationHookResult,
  UpdateAdyenPaymentModuleMutationResult,
  UpdateAdyenPaymentModuleMutationOptions,
  UpdateAdditionalDetailMutationVariables,
  UpdateAdditionalDetailMutation,
  UpdateAdditionalDetailMutationFn,
  UpdateAdditionalDetailMutationHookResult,
  UpdateAdditionalDetailMutationResult,
  UpdateAdditionalDetailMutationOptions,
  UpdateAccountMutationVariables,
  UpdateAccountMutation,
  UpdateAccountMutationFn,
  UpdateAccountMutationHookResult,
  UpdateAccountMutationResult,
  UpdateAccountMutationOptions,
  UnqualifyLeadMutationVariables,
  UnqualifyLeadMutation,
  UnqualifyLeadMutationFn,
  UnqualifyLeadMutationHookResult,
  UnqualifyLeadMutationResult,
  UnqualifyLeadMutationOptions,
  TakeOutStockInventoryMutationVariables,
  TakeOutStockInventoryMutation,
  TakeOutStockInventoryMutationFn,
  TakeOutStockInventoryMutationHookResult,
  TakeOutStockInventoryMutationResult,
  TakeOutStockInventoryMutationOptions,
  SynchronizePorscheMasterDataMutationVariables,
  SynchronizePorscheMasterDataMutation,
  SynchronizePorscheMasterDataMutationFn,
  SynchronizePorscheMasterDataMutationHookResult,
  SynchronizePorscheMasterDataMutationResult,
  SynchronizePorscheMasterDataMutationOptions,
  SynchronizeFinderVehicleMutationVariables,
  SynchronizeFinderVehicleMutation,
  SynchronizeFinderVehicleMutationFn,
  SynchronizeFinderVehicleMutationHookResult,
  SynchronizeFinderVehicleMutationResult,
  SynchronizeFinderVehicleMutationOptions,
  SubmitTtbPaymentMutationVariables,
  SubmitTtbPaymentMutation,
  SubmitTtbPaymentMutationFn,
  SubmitTtbPaymentMutationHookResult,
  SubmitTtbPaymentMutationResult,
  SubmitTtbPaymentMutationOptions,
  SubmitTestDriveKycMutationVariables,
  SubmitTestDriveKycMutation,
  SubmitTestDriveKycMutationFn,
  SubmitTestDriveKycMutationHookResult,
  SubmitTestDriveKycMutationResult,
  SubmitTestDriveKycMutationOptions,
  SubmitTestDriveAgreementsMutationVariables,
  SubmitTestDriveAgreementsMutation,
  SubmitTestDriveAgreementsMutationFn,
  SubmitTestDriveAgreementsMutationHookResult,
  SubmitTestDriveAgreementsMutationResult,
  SubmitTestDriveAgreementsMutationOptions,
  SubmitSigningOtpMutationVariables,
  SubmitSigningOtpMutation,
  SubmitSigningOtpMutationFn,
  SubmitSigningOtpMutationHookResult,
  SubmitSigningOtpMutationResult,
  SubmitSigningOtpMutationOptions,
  SubmitSalesOfferPorschePaymentMutationVariables,
  SubmitSalesOfferPorschePaymentMutation,
  SubmitSalesOfferPorschePaymentMutationFn,
  SubmitSalesOfferPorschePaymentMutationHookResult,
  SubmitSalesOfferPorschePaymentMutationResult,
  SubmitSalesOfferPorschePaymentMutationOptions,
  SubmitPorschePaymentMutationVariables,
  SubmitPorschePaymentMutation,
  SubmitPorschePaymentMutationFn,
  SubmitPorschePaymentMutationHookResult,
  SubmitPorschePaymentMutationResult,
  SubmitPorschePaymentMutationOptions,
  SubmitPayGatePaymentMutationVariables,
  SubmitPayGatePaymentMutation,
  SubmitPayGatePaymentMutationFn,
  SubmitPayGatePaymentMutationHookResult,
  SubmitPayGatePaymentMutationResult,
  SubmitPayGatePaymentMutationOptions,
  SubmitIntentAndAssignMutationVariables,
  SubmitIntentAndAssignMutation,
  SubmitIntentAndAssignMutationFn,
  SubmitIntentAndAssignMutationHookResult,
  SubmitIntentAndAssignMutationResult,
  SubmitIntentAndAssignMutationOptions,
  SubmitGuarantorKycMutationVariables,
  SubmitGuarantorKycMutation,
  SubmitGuarantorKycMutationFn,
  SubmitGuarantorKycMutationHookResult,
  SubmitGuarantorKycMutationResult,
  SubmitGuarantorKycMutationOptions,
  SubmitGuarantorAgreementsMutationVariables,
  SubmitGuarantorAgreementsMutation,
  SubmitGuarantorAgreementsMutationFn,
  SubmitGuarantorAgreementsMutationHookResult,
  SubmitGuarantorAgreementsMutationResult,
  SubmitGuarantorAgreementsMutationOptions,
  SubmitGiftVoucherTtbPaymentMutationVariables,
  SubmitGiftVoucherTtbPaymentMutation,
  SubmitGiftVoucherTtbPaymentMutationFn,
  SubmitGiftVoucherTtbPaymentMutationHookResult,
  SubmitGiftVoucherTtbPaymentMutationResult,
  SubmitGiftVoucherTtbPaymentMutationOptions,
  SubmitGiftVoucherPorschePaymentMutationVariables,
  SubmitGiftVoucherPorschePaymentMutation,
  SubmitGiftVoucherPorschePaymentMutationFn,
  SubmitGiftVoucherPorschePaymentMutationHookResult,
  SubmitGiftVoucherPorschePaymentMutationResult,
  SubmitGiftVoucherPorschePaymentMutationOptions,
  SubmitGiftVoucherPayGatePaymentMutationVariables,
  SubmitGiftVoucherPayGatePaymentMutation,
  SubmitGiftVoucherPayGatePaymentMutationFn,
  SubmitGiftVoucherPayGatePaymentMutationHookResult,
  SubmitGiftVoucherPayGatePaymentMutationResult,
  SubmitGiftVoucherPayGatePaymentMutationOptions,
  SubmitGiftVoucherFiservPaymentMutationVariables,
  SubmitGiftVoucherFiservPaymentMutation,
  SubmitGiftVoucherFiservPaymentMutationFn,
  SubmitGiftVoucherFiservPaymentMutationHookResult,
  SubmitGiftVoucherFiservPaymentMutationResult,
  SubmitGiftVoucherFiservPaymentMutationOptions,
  SubmitGiftVoucherApplicantKycMutationVariables,
  SubmitGiftVoucherApplicantKycMutation,
  SubmitGiftVoucherApplicantKycMutationFn,
  SubmitGiftVoucherApplicantKycMutationHookResult,
  SubmitGiftVoucherApplicantKycMutationResult,
  SubmitGiftVoucherApplicantKycMutationOptions,
  SubmitGiftVoucherApplicantAgreementsMutationVariables,
  SubmitGiftVoucherApplicantAgreementsMutation,
  SubmitGiftVoucherApplicantAgreementsMutationFn,
  SubmitGiftVoucherApplicantAgreementsMutationHookResult,
  SubmitGiftVoucherApplicantAgreementsMutationResult,
  SubmitGiftVoucherApplicantAgreementsMutationOptions,
  SubmitGiftVoucherAdyenPaymentMutationVariables,
  SubmitGiftVoucherAdyenPaymentMutation,
  SubmitGiftVoucherAdyenPaymentMutationFn,
  SubmitGiftVoucherAdyenPaymentMutationHookResult,
  SubmitGiftVoucherAdyenPaymentMutationResult,
  SubmitGiftVoucherAdyenPaymentMutationOptions,
  SubmitFiservPaymentMutationVariables,
  SubmitFiservPaymentMutation,
  SubmitFiservPaymentMutationFn,
  SubmitFiservPaymentMutationHookResult,
  SubmitFiservPaymentMutationResult,
  SubmitFiservPaymentMutationOptions,
  SubmitChangesMutationVariables,
  SubmitChangesMutation,
  SubmitChangesMutationFn,
  SubmitChangesMutationHookResult,
  SubmitChangesMutationResult,
  SubmitChangesMutationOptions,
  SubmitApplicationQuotationMutationVariables,
  SubmitApplicationQuotationMutation,
  SubmitApplicationQuotationMutationFn,
  SubmitApplicationQuotationMutationHookResult,
  SubmitApplicationQuotationMutationResult,
  SubmitApplicationQuotationMutationOptions,
  SubmitApplicantVisitAppointmentMutationVariables,
  SubmitApplicantVisitAppointmentMutation,
  SubmitApplicantVisitAppointmentMutationFn,
  SubmitApplicantVisitAppointmentMutationHookResult,
  SubmitApplicantVisitAppointmentMutationResult,
  SubmitApplicantVisitAppointmentMutationOptions,
  SubmitApplicantKycMutationVariables,
  SubmitApplicantKycMutation,
  SubmitApplicantKycMutationFn,
  SubmitApplicantKycMutationHookResult,
  SubmitApplicantKycMutationResult,
  SubmitApplicantKycMutationOptions,
  SubmitApplicantAppointmentMutationVariables,
  SubmitApplicantAppointmentMutation,
  SubmitApplicantAppointmentMutationFn,
  SubmitApplicantAppointmentMutationHookResult,
  SubmitApplicantAppointmentMutationResult,
  SubmitApplicantAppointmentMutationOptions,
  SubmitApplicantAgreementsMutationVariables,
  SubmitApplicantAgreementsMutation,
  SubmitApplicantAgreementsMutationFn,
  SubmitApplicantAgreementsMutationHookResult,
  SubmitApplicantAgreementsMutationResult,
  SubmitApplicantAgreementsMutationOptions,
  SubmitAdyenPaymentMutationVariables,
  SubmitAdyenPaymentMutation,
  SubmitAdyenPaymentMutationFn,
  SubmitAdyenPaymentMutationHookResult,
  SubmitAdyenPaymentMutationResult,
  SubmitAdyenPaymentMutationOptions,
  StartTestDriveMutationVariables,
  StartTestDriveMutation,
  StartTestDriveMutationFn,
  StartTestDriveMutationHookResult,
  StartTestDriveMutationResult,
  StartTestDriveMutationOptions,
  ShareStandardApplicationMutationVariables,
  ShareStandardApplicationMutation,
  ShareStandardApplicationMutationFn,
  ShareStandardApplicationMutationHookResult,
  ShareStandardApplicationMutationResult,
  ShareStandardApplicationMutationOptions,
  ShareSalesOfferDocumentMutationVariables,
  ShareSalesOfferDocumentMutation,
  ShareSalesOfferDocumentMutationFn,
  ShareSalesOfferDocumentMutationHookResult,
  ShareSalesOfferDocumentMutationResult,
  ShareSalesOfferDocumentMutationOptions,
  SendSalesOfferMutationVariables,
  SendSalesOfferMutation,
  SendSalesOfferMutationFn,
  SendSalesOfferMutationHookResult,
  SendSalesOfferMutationResult,
  SendSalesOfferMutationOptions,
  SendMobileVerificationOtpMutationVariables,
  SendMobileVerificationOtpMutation,
  SendMobileVerificationOtpMutationFn,
  SendMobileVerificationOtpMutationHookResult,
  SendMobileVerificationOtpMutationResult,
  SendMobileVerificationOtpMutationOptions,
  RevokeWebPublicKeyCredentialMutationVariables,
  RevokeWebPublicKeyCredentialMutation,
  RevokeWebPublicKeyCredentialMutationFn,
  RevokeWebPublicKeyCredentialMutationHookResult,
  RevokeWebPublicKeyCredentialMutationResult,
  RevokeWebPublicKeyCredentialMutationOptions,
  RevokeSessionMutationVariables,
  RevokeSessionMutation,
  RevokeSessionMutationFn,
  RevokeSessionMutationHookResult,
  RevokeSessionMutationResult,
  RevokeSessionMutationOptions,
  ResubmitLeadToCapMutationVariables,
  ResubmitLeadToCapMutation,
  ResubmitLeadToCapMutationFn,
  ResubmitLeadToCapMutationHookResult,
  ResubmitLeadToCapMutationResult,
  ResubmitLeadToCapMutationOptions,
  ResetFinderVehicleMutationVariables,
  ResetFinderVehicleMutation,
  ResetFinderVehicleMutationFn,
  ResetFinderVehicleMutationHookResult,
  ResetFinderVehicleMutationResult,
  ResetFinderVehicleMutationOptions,
  ResendSmsOtpForUpdateEmailMutationVariables,
  ResendSmsOtpForUpdateEmailMutation,
  ResendSmsOtpForUpdateEmailMutationFn,
  ResendSmsOtpForUpdateEmailMutationHookResult,
  ResendSmsOtpForUpdateEmailMutationResult,
  ResendSmsOtpForUpdateEmailMutationOptions,
  ResendSmsOtpForResetPasswordMutationVariables,
  ResendSmsOtpForResetPasswordMutation,
  ResendSmsOtpForResetPasswordMutationFn,
  ResendSmsOtpForResetPasswordMutationHookResult,
  ResendSmsOtpForResetPasswordMutationResult,
  ResendSmsOtpForResetPasswordMutationOptions,
  ResendActivationLinkMutationVariables,
  ResendActivationLinkMutation,
  ResendActivationLinkMutationFn,
  ResendActivationLinkMutationHookResult,
  ResendActivationLinkMutationResult,
  ResendActivationLinkMutationOptions,
  RequestWebPublicKeyCredentialRegistrationMutationVariables,
  RequestWebPublicKeyCredentialRegistrationMutation,
  RequestWebPublicKeyCredentialRegistrationMutationFn,
  RequestWebPublicKeyCredentialRegistrationMutationHookResult,
  RequestWebPublicKeyCredentialRegistrationMutationResult,
  RequestWebPublicKeyCredentialRegistrationMutationOptions,
  RequestReleaseLetterMutationVariables,
  RequestReleaseLetterMutation,
  RequestReleaseLetterMutationFn,
  RequestReleaseLetterMutationHookResult,
  RequestReleaseLetterMutationResult,
  RequestReleaseLetterMutationOptions,
  RequestDisbursementMutationVariables,
  RequestDisbursementMutation,
  RequestDisbursementMutationFn,
  RequestDisbursementMutationHookResult,
  RequestDisbursementMutationResult,
  RequestDisbursementMutationOptions,
  RemoveUserOnUserGroupMutationVariables,
  RemoveUserOnUserGroupMutation,
  RemoveUserOnUserGroupMutationFn,
  RemoveUserOnUserGroupMutationHookResult,
  RemoveUserOnUserGroupMutationResult,
  RemoveUserOnUserGroupMutationOptions,
  RemoveUserOnRoleMutationVariables,
  RemoveUserOnRoleMutation,
  RemoveUserOnRoleMutationFn,
  RemoveUserOnRoleMutationHookResult,
  RemoveUserOnRoleMutationResult,
  RemoveUserOnRoleMutationOptions,
  RemovePermissionOnRoleMutationVariables,
  RemovePermissionOnRoleMutation,
  RemovePermissionOnRoleMutationFn,
  RemovePermissionOnRoleMutationHookResult,
  RemovePermissionOnRoleMutationResult,
  RemovePermissionOnRoleMutationOptions,
  RemoveDealerOnUserGroupMutationVariables,
  RemoveDealerOnUserGroupMutation,
  RemoveDealerOnUserGroupMutationFn,
  RemoveDealerOnUserGroupMutationHookResult,
  RemoveDealerOnUserGroupMutationResult,
  RemoveDealerOnUserGroupMutationOptions,
  ReleaseReservedMobilityStockMutationVariables,
  ReleaseReservedMobilityStockMutation,
  ReleaseReservedMobilityStockMutationFn,
  ReleaseReservedMobilityStockMutationHookResult,
  ReleaseReservedMobilityStockMutationResult,
  ReleaseReservedMobilityStockMutationOptions,
  ReleaseReservedConfiguratorStockMutationVariables,
  ReleaseReservedConfiguratorStockMutation,
  ReleaseReservedConfiguratorStockMutationFn,
  ReleaseReservedConfiguratorStockMutationHookResult,
  ReleaseReservedConfiguratorStockMutationResult,
  ReleaseReservedConfiguratorStockMutationOptions,
  ReleaseFinderVehicleExpiryMutationVariables,
  ReleaseFinderVehicleExpiryMutation,
  ReleaseFinderVehicleExpiryMutationFn,
  ReleaseFinderVehicleExpiryMutationHookResult,
  ReleaseFinderVehicleExpiryMutationResult,
  ReleaseFinderVehicleExpiryMutationOptions,
  RefreshCredentialsMutationVariables,
  RefreshCredentialsMutation,
  RefreshCredentialsMutationFn,
  RefreshCredentialsMutationHookResult,
  RefreshCredentialsMutationResult,
  RefreshCredentialsMutationOptions,
  RefreshApplicationStatusMutationVariables,
  RefreshApplicationStatusMutation,
  RefreshApplicationStatusMutationFn,
  RefreshApplicationStatusMutationHookResult,
  RefreshApplicationStatusMutationResult,
  RefreshApplicationStatusMutationOptions,
  QualifyLeadWithCapValuesMutationVariables,
  QualifyLeadWithCapValuesMutation,
  QualifyLeadWithCapValuesMutationFn,
  QualifyLeadWithCapValuesMutationHookResult,
  QualifyLeadWithCapValuesMutationResult,
  QualifyLeadWithCapValuesMutationOptions,
  QualifyLeadMutationVariables,
  QualifyLeadMutation,
  QualifyLeadMutationFn,
  QualifyLeadMutationHookResult,
  QualifyLeadMutationResult,
  QualifyLeadMutationOptions,
  ProceedWithCustomerDeviceMutationVariables,
  ProceedWithCustomerDeviceMutation,
  ProceedWithCustomerDeviceMutationFn,
  ProceedWithCustomerDeviceMutationHookResult,
  ProceedWithCustomerDeviceMutationResult,
  ProceedWithCustomerDeviceMutationOptions,
  MarkLeadAsLostMutationVariables,
  MarkLeadAsLostMutation,
  MarkLeadAsLostMutationFn,
  MarkLeadAsLostMutationHookResult,
  MarkLeadAsLostMutationResult,
  MarkLeadAsLostMutationOptions,
  MarkAsContactedMutationVariables,
  MarkAsContactedMutation,
  MarkAsContactedMutationFn,
  MarkAsContactedMutationHookResult,
  MarkAsContactedMutationResult,
  MarkAsContactedMutationOptions,
  InitialSalesOfferSigningMutationVariables,
  InitialSalesOfferSigningMutation,
  InitialSalesOfferSigningMutationFn,
  InitialSalesOfferSigningMutationHookResult,
  InitialSalesOfferSigningMutationResult,
  InitialSalesOfferSigningMutationOptions,
  InitialSalesOfferPaymentMutationVariables,
  InitialSalesOfferPaymentMutation,
  InitialSalesOfferPaymentMutationFn,
  InitialSalesOfferPaymentMutationHookResult,
  InitialSalesOfferPaymentMutationResult,
  InitialSalesOfferPaymentMutationOptions,
  ImportVariantImagesMutationVariables,
  ImportVariantImagesMutation,
  ImportVariantImagesMutationFn,
  ImportVariantImagesMutationHookResult,
  ImportVariantImagesMutationResult,
  ImportVariantImagesMutationOptions,
  ImportVariantMutationVariables,
  ImportVariantMutation,
  ImportVariantMutationFn,
  ImportVariantMutationHookResult,
  ImportVariantMutationResult,
  ImportVariantMutationOptions,
  ImportSubModelMutationVariables,
  ImportSubModelMutation,
  ImportSubModelMutationFn,
  ImportSubModelMutationHookResult,
  ImportSubModelMutationResult,
  ImportSubModelMutationOptions,
  ImportSalesControlBoardDataMutationVariables,
  ImportSalesControlBoardDataMutation,
  ImportSalesControlBoardDataMutationFn,
  ImportSalesControlBoardDataMutationHookResult,
  ImportSalesControlBoardDataMutationResult,
  ImportSalesControlBoardDataMutationOptions,
  ImportModelMutationVariables,
  ImportModelMutation,
  ImportModelMutationFn,
  ImportModelMutationHookResult,
  ImportModelMutationResult,
  ImportModelMutationOptions,
  ImportMakeMutationVariables,
  ImportMakeMutation,
  ImportMakeMutationFn,
  ImportMakeMutationHookResult,
  ImportMakeMutationResult,
  ImportMakeMutationOptions,
  ImportLanguagePackMutationVariables,
  ImportLanguagePackMutation,
  ImportLanguagePackMutationFn,
  ImportLanguagePackMutationHookResult,
  ImportLanguagePackMutationResult,
  ImportLanguagePackMutationOptions,
  ImportInventoriesMutationVariables,
  ImportInventoriesMutation,
  ImportInventoriesMutationFn,
  ImportInventoriesMutationHookResult,
  ImportInventoriesMutationResult,
  ImportInventoriesMutationOptions,
  ImportFinderVehiclesLtaMutationVariables,
  ImportFinderVehiclesLtaMutation,
  ImportFinderVehiclesLtaMutationFn,
  ImportFinderVehiclesLtaMutationHookResult,
  ImportFinderVehiclesLtaMutationResult,
  ImportFinderVehiclesLtaMutationOptions,
  ImportDealersMutationVariables,
  ImportDealersMutation,
  ImportDealersMutationFn,
  ImportDealersMutationHookResult,
  ImportDealersMutationResult,
  ImportDealersMutationOptions,
  ImportConfiguratorVariantPricesMutationVariables,
  ImportConfiguratorVariantPricesMutation,
  ImportConfiguratorVariantPricesMutationFn,
  ImportConfiguratorVariantPricesMutationHookResult,
  ImportConfiguratorVariantPricesMutationResult,
  ImportConfiguratorVariantPricesMutationOptions,
  ImportCompanyConfigurationMutationVariables,
  ImportCompanyConfigurationMutation,
  ImportCompanyConfigurationMutationFn,
  ImportCompanyConfigurationMutationHookResult,
  ImportCompanyConfigurationMutationResult,
  ImportCompanyConfigurationMutationOptions,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationVariables,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutation,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationFn,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationHookResult,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationResult,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationMutationOptions,
  GenerateSigningOtpMutationVariables,
  GenerateSigningOtpMutation,
  GenerateSigningOtpMutationFn,
  GenerateSigningOtpMutationHookResult,
  GenerateSigningOtpMutationResult,
  GenerateSigningOtpMutationOptions,
  GenerateSalesOfferJourneyTokenMutationVariables,
  GenerateSalesOfferJourneyTokenMutation,
  GenerateSalesOfferJourneyTokenMutationFn,
  GenerateSalesOfferJourneyTokenMutationHookResult,
  GenerateSalesOfferJourneyTokenMutationResult,
  GenerateSalesOfferJourneyTokenMutationOptions,
  GenerateRemoteJourneyPasscodeMutationVariables,
  GenerateRemoteJourneyPasscodeMutation,
  GenerateRemoteJourneyPasscodeMutationFn,
  GenerateRemoteJourneyPasscodeMutationHookResult,
  GenerateRemoteJourneyPasscodeMutationResult,
  GenerateRemoteJourneyPasscodeMutationOptions,
  GenerateCancelMobilityApplicationAccessMutationVariables,
  GenerateCancelMobilityApplicationAccessMutation,
  GenerateCancelMobilityApplicationAccessMutationFn,
  GenerateCancelMobilityApplicationAccessMutationHookResult,
  GenerateCancelMobilityApplicationAccessMutationResult,
  GenerateCancelMobilityApplicationAccessMutationOptions,
  GenerateAmendMobilityApplicationAccessMutationVariables,
  GenerateAmendMobilityApplicationAccessMutation,
  GenerateAmendMobilityApplicationAccessMutationFn,
  GenerateAmendMobilityApplicationAccessMutationHookResult,
  GenerateAmendMobilityApplicationAccessMutationResult,
  GenerateAmendMobilityApplicationAccessMutationOptions,
  ExtendMobilityStockExpiryMutationVariables,
  ExtendMobilityStockExpiryMutation,
  ExtendMobilityStockExpiryMutationFn,
  ExtendMobilityStockExpiryMutationHookResult,
  ExtendMobilityStockExpiryMutationResult,
  ExtendMobilityStockExpiryMutationOptions,
  ExtendGiftVoucherJourneyExpiryMutationVariables,
  ExtendGiftVoucherJourneyExpiryMutation,
  ExtendGiftVoucherJourneyExpiryMutationFn,
  ExtendGiftVoucherJourneyExpiryMutationHookResult,
  ExtendGiftVoucherJourneyExpiryMutationResult,
  ExtendGiftVoucherJourneyExpiryMutationOptions,
  ExtendFinderVehicleExpiryMutationVariables,
  ExtendFinderVehicleExpiryMutation,
  ExtendFinderVehicleExpiryMutationFn,
  ExtendFinderVehicleExpiryMutationHookResult,
  ExtendFinderVehicleExpiryMutationResult,
  ExtendFinderVehicleExpiryMutationOptions,
  ExtendEventJourneyExpiryMutationVariables,
  ExtendEventJourneyExpiryMutation,
  ExtendEventJourneyExpiryMutationFn,
  ExtendEventJourneyExpiryMutationHookResult,
  ExtendEventJourneyExpiryMutationResult,
  ExtendEventJourneyExpiryMutationOptions,
  ExtendConfiguratorStockExpiryMutationVariables,
  ExtendConfiguratorStockExpiryMutation,
  ExtendConfiguratorStockExpiryMutationFn,
  ExtendConfiguratorStockExpiryMutationHookResult,
  ExtendConfiguratorStockExpiryMutationResult,
  ExtendConfiguratorStockExpiryMutationOptions,
  ExportCompanyConfigurationMutationVariables,
  ExportCompanyConfigurationMutation,
  ExportCompanyConfigurationMutationFn,
  ExportCompanyConfigurationMutationHookResult,
  ExportCompanyConfigurationMutationResult,
  ExportCompanyConfigurationMutationOptions,
  EndTestDriveMutationVariables,
  EndTestDriveMutation,
  EndTestDriveMutationFn,
  EndTestDriveMutationHookResult,
  EndTestDriveMutationResult,
  EndTestDriveMutationOptions,
  EnableAuthenticatorMutationVariables,
  EnableAuthenticatorMutation,
  EnableAuthenticatorMutationFn,
  EnableAuthenticatorMutationHookResult,
  EnableAuthenticatorMutationResult,
  EnableAuthenticatorMutationOptions,
  DraftStandardApplicationFromLeadMutationVariables,
  DraftStandardApplicationFromLeadMutation,
  DraftStandardApplicationFromLeadMutationFn,
  DraftStandardApplicationFromLeadMutationHookResult,
  DraftStandardApplicationFromLeadMutationResult,
  DraftStandardApplicationFromLeadMutationOptions,
  DraftMobilityApplicationMutationVariables,
  DraftMobilityApplicationMutation,
  DraftMobilityApplicationMutationFn,
  DraftMobilityApplicationMutationHookResult,
  DraftMobilityApplicationMutationResult,
  DraftMobilityApplicationMutationOptions,
  DraftLaunchpadApplicationMutationVariables,
  DraftLaunchpadApplicationMutation,
  DraftLaunchpadApplicationMutationFn,
  DraftLaunchpadApplicationMutationHookResult,
  DraftLaunchpadApplicationMutationResult,
  DraftLaunchpadApplicationMutationOptions,
  DraftGiftVoucherMutationVariables,
  DraftGiftVoucherMutation,
  DraftGiftVoucherMutationFn,
  DraftGiftVoucherMutationHookResult,
  DraftGiftVoucherMutationResult,
  DraftGiftVoucherMutationOptions,
  DraftFinderApplicationFromLeadMutationVariables,
  DraftFinderApplicationFromLeadMutation,
  DraftFinderApplicationFromLeadMutationFn,
  DraftFinderApplicationFromLeadMutationHookResult,
  DraftFinderApplicationFromLeadMutationResult,
  DraftFinderApplicationFromLeadMutationOptions,
  DraftFinderApplicationMutationVariables,
  DraftFinderApplicationMutation,
  DraftFinderApplicationMutationFn,
  DraftFinderApplicationMutationHookResult,
  DraftFinderApplicationMutationResult,
  DraftFinderApplicationMutationOptions,
  DraftEventApplicationMutationVariables,
  DraftEventApplicationMutation,
  DraftEventApplicationMutationFn,
  DraftEventApplicationMutationHookResult,
  DraftEventApplicationMutationResult,
  DraftEventApplicationMutationOptions,
  DraftConfiguratorApplicationMutationVariables,
  DraftConfiguratorApplicationMutation,
  DraftConfiguratorApplicationMutationFn,
  DraftConfiguratorApplicationMutationHookResult,
  DraftConfiguratorApplicationMutationResult,
  DraftConfiguratorApplicationMutationOptions,
  DraftApplicationMutationVariables,
  DraftApplicationMutation,
  DraftApplicationMutationFn,
  DraftApplicationMutationHookResult,
  DraftApplicationMutationResult,
  DraftApplicationMutationOptions,
  DownloadSpecificationDocumentMutationVariables,
  DownloadSpecificationDocumentMutation,
  DownloadSpecificationDocumentMutationFn,
  DownloadSpecificationDocumentMutationHookResult,
  DownloadSpecificationDocumentMutationResult,
  DownloadSpecificationDocumentMutationOptions,
  DownloadSalesOfferDocumentMutationVariables,
  DownloadSalesOfferDocumentMutation,
  DownloadSalesOfferDocumentMutationFn,
  DownloadSalesOfferDocumentMutationHookResult,
  DownloadSalesOfferDocumentMutationResult,
  DownloadSalesOfferDocumentMutationOptions,
  DisableAuthenticatorMutationVariables,
  DisableAuthenticatorMutation,
  DisableAuthenticatorMutationFn,
  DisableAuthenticatorMutationHookResult,
  DisableAuthenticatorMutationResult,
  DisableAuthenticatorMutationOptions,
  DeleteWhatsappLiveChatSettingsMutationVariables,
  DeleteWhatsappLiveChatSettingsMutation,
  DeleteWhatsappLiveChatSettingsMutationFn,
  DeleteWhatsappLiveChatSettingsMutationHookResult,
  DeleteWhatsappLiveChatSettingsMutationResult,
  DeleteWhatsappLiveChatSettingsMutationOptions,
  DeleteWebsiteSocialMediaAssetMutationVariables,
  DeleteWebsiteSocialMediaAssetMutation,
  DeleteWebsiteSocialMediaAssetMutationFn,
  DeleteWebsiteSocialMediaAssetMutationHookResult,
  DeleteWebsiteSocialMediaAssetMutationResult,
  DeleteWebsiteSocialMediaAssetMutationOptions,
  DeleteWebsiteSocialMediaMutationVariables,
  DeleteWebsiteSocialMediaMutation,
  DeleteWebsiteSocialMediaMutationFn,
  DeleteWebsiteSocialMediaMutationHookResult,
  DeleteWebsiteSocialMediaMutationResult,
  DeleteWebsiteSocialMediaMutationOptions,
  DeleteWebPageImageMutationVariables,
  DeleteWebPageImageMutation,
  DeleteWebPageImageMutationFn,
  DeleteWebPageImageMutationHookResult,
  DeleteWebPageImageMutationResult,
  DeleteWebPageImageMutationOptions,
  DeleteWebPageBlockMutationVariables,
  DeleteWebPageBlockMutation,
  DeleteWebPageBlockMutationFn,
  DeleteWebPageBlockMutationHookResult,
  DeleteWebPageBlockMutationResult,
  DeleteWebPageBlockMutationOptions,
  DeleteWebPageMutationVariables,
  DeleteWebPageMutation,
  DeleteWebPageMutationFn,
  DeleteWebPageMutationHookResult,
  DeleteWebPageMutationResult,
  DeleteWebPageMutationOptions,
  DeleteVisitAppointmentModuleAssetMutationVariables,
  DeleteVisitAppointmentModuleAssetMutation,
  DeleteVisitAppointmentModuleAssetMutationFn,
  DeleteVisitAppointmentModuleAssetMutationHookResult,
  DeleteVisitAppointmentModuleAssetMutationResult,
  DeleteVisitAppointmentModuleAssetMutationOptions,
  DeleteVehicleMutationVariables,
  DeleteVehicleMutation,
  DeleteVehicleMutationFn,
  DeleteVehicleMutationHookResult,
  DeleteVehicleMutationResult,
  DeleteVehicleMutationOptions,
  DeleteVariantConfiguratorTrimSettingAssetMutationVariables,
  DeleteVariantConfiguratorTrimSettingAssetMutation,
  DeleteVariantConfiguratorTrimSettingAssetMutationFn,
  DeleteVariantConfiguratorTrimSettingAssetMutationHookResult,
  DeleteVariantConfiguratorTrimSettingAssetMutationResult,
  DeleteVariantConfiguratorTrimSettingAssetMutationOptions,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationVariables,
  DeleteVariantConfiguratorPackageSectionImageAssetMutation,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationFn,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationHookResult,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationResult,
  DeleteVariantConfiguratorPackageSectionImageAssetMutationOptions,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationVariables,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationFn,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationHookResult,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationResult,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetMutationOptions,
  DeleteVariantConfiguratorOptionSettingAssetMutationVariables,
  DeleteVariantConfiguratorOptionSettingAssetMutation,
  DeleteVariantConfiguratorOptionSettingAssetMutationFn,
  DeleteVariantConfiguratorOptionSettingAssetMutationHookResult,
  DeleteVariantConfiguratorOptionSettingAssetMutationResult,
  DeleteVariantConfiguratorOptionSettingAssetMutationOptions,
  DeleteVariantConfiguratorOptionAssetMutationVariables,
  DeleteVariantConfiguratorOptionAssetMutation,
  DeleteVariantConfiguratorOptionAssetMutationFn,
  DeleteVariantConfiguratorOptionAssetMutationHookResult,
  DeleteVariantConfiguratorOptionAssetMutationResult,
  DeleteVariantConfiguratorOptionAssetMutationOptions,
  DeleteVariantConfiguratorMatrixAssetMutationVariables,
  DeleteVariantConfiguratorMatrixAssetMutation,
  DeleteVariantConfiguratorMatrixAssetMutationFn,
  DeleteVariantConfiguratorMatrixAssetMutationHookResult,
  DeleteVariantConfiguratorMatrixAssetMutationResult,
  DeleteVariantConfiguratorMatrixAssetMutationOptions,
  DeleteVariantConfiguratorColorSettingAssetMutationVariables,
  DeleteVariantConfiguratorColorSettingAssetMutation,
  DeleteVariantConfiguratorColorSettingAssetMutationFn,
  DeleteVariantConfiguratorColorSettingAssetMutationHookResult,
  DeleteVariantConfiguratorColorSettingAssetMutationResult,
  DeleteVariantConfiguratorColorSettingAssetMutationOptions,
  DeleteVariantConfiguratorMutationVariables,
  DeleteVariantConfiguratorMutation,
  DeleteVariantConfiguratorMutationFn,
  DeleteVariantConfiguratorMutationHookResult,
  DeleteVariantConfiguratorMutationResult,
  DeleteVariantConfiguratorMutationOptions,
  DeleteVariantAssetMutationVariables,
  DeleteVariantAssetMutation,
  DeleteVariantAssetMutationFn,
  DeleteVariantAssetMutationHookResult,
  DeleteVariantAssetMutationResult,
  DeleteVariantAssetMutationOptions,
  DeleteUserlikeChatbotSettingsMutationVariables,
  DeleteUserlikeChatbotSettingsMutation,
  DeleteUserlikeChatbotSettingsMutationFn,
  DeleteUserlikeChatbotSettingsMutationHookResult,
  DeleteUserlikeChatbotSettingsMutationResult,
  DeleteUserlikeChatbotSettingsMutationOptions,
  DeleteUserGroupMutationVariables,
  DeleteUserGroupMutation,
  DeleteUserGroupMutationFn,
  DeleteUserGroupMutationHookResult,
  DeleteUserGroupMutationResult,
  DeleteUserGroupMutationOptions,
  DeleteUserAssetMutationVariables,
  DeleteUserAssetMutation,
  DeleteUserAssetMutationFn,
  DeleteUserAssetMutationHookResult,
  DeleteUserAssetMutationResult,
  DeleteUserAssetMutationOptions,
  DeleteUserMutationVariables,
  DeleteUserMutation,
  DeleteUserMutationFn,
  DeleteUserMutationHookResult,
  DeleteUserMutationResult,
  DeleteUserMutationOptions,
  DeleteTtbPaymentSettingsMutationVariables,
  DeleteTtbPaymentSettingsMutation,
  DeleteTtbPaymentSettingsMutationFn,
  DeleteTtbPaymentSettingsMutationHookResult,
  DeleteTtbPaymentSettingsMutationResult,
  DeleteTtbPaymentSettingsMutationOptions,
  DeleteStockInventoryMutationVariables,
  DeleteStockInventoryMutation,
  DeleteStockInventoryMutationFn,
  DeleteStockInventoryMutationHookResult,
  DeleteStockInventoryMutationResult,
  DeleteStockInventoryMutationOptions,
  DeleteStockAssetMutationVariables,
  DeleteStockAssetMutation,
  DeleteStockAssetMutationFn,
  DeleteStockAssetMutationHookResult,
  DeleteStockAssetMutationResult,
  DeleteStockAssetMutationOptions,
  DeleteStandardApplicationModuleAssetMutationVariables,
  DeleteStandardApplicationModuleAssetMutation,
  DeleteStandardApplicationModuleAssetMutationFn,
  DeleteStandardApplicationModuleAssetMutationHookResult,
  DeleteStandardApplicationModuleAssetMutationResult,
  DeleteStandardApplicationModuleAssetMutationOptions,
  DeleteSalesOfferDocumentMutationVariables,
  DeleteSalesOfferDocumentMutation,
  DeleteSalesOfferDocumentMutationFn,
  DeleteSalesOfferDocumentMutationHookResult,
  DeleteSalesOfferDocumentMutationResult,
  DeleteSalesOfferDocumentMutationOptions,
  DeleteRouterMutationVariables,
  DeleteRouterMutation,
  DeleteRouterMutationFn,
  DeleteRouterMutationHookResult,
  DeleteRouterMutationResult,
  DeleteRouterMutationOptions,
  DeleteRoleMutationVariables,
  DeleteRoleMutation,
  DeleteRoleMutationFn,
  DeleteRoleMutationHookResult,
  DeleteRoleMutationResult,
  DeleteRoleMutationOptions,
  DeletePromoCodeMutationVariables,
  DeletePromoCodeMutation,
  DeletePromoCodeMutationFn,
  DeletePromoCodeMutationHookResult,
  DeletePromoCodeMutationResult,
  DeletePromoCodeMutationOptions,
  DeletePorschePaymentSettingsMutationVariables,
  DeletePorschePaymentSettingsMutation,
  DeletePorschePaymentSettingsMutationFn,
  DeletePorschePaymentSettingsMutationHookResult,
  DeletePorschePaymentSettingsMutationResult,
  DeletePorschePaymentSettingsMutationOptions,
  DeletePayGatePaymentSettingsMutationVariables,
  DeletePayGatePaymentSettingsMutation,
  DeletePayGatePaymentSettingsMutationFn,
  DeletePayGatePaymentSettingsMutationHookResult,
  DeletePayGatePaymentSettingsMutationResult,
  DeletePayGatePaymentSettingsMutationOptions,
  DeletePackageSettingMutationVariables,
  DeletePackageSettingMutation,
  DeletePackageSettingMutationFn,
  DeletePackageSettingMutationHookResult,
  DeletePackageSettingMutationResult,
  DeletePackageSettingMutationOptions,
  DeleteOptionsBlockMutationVariables,
  DeleteOptionsBlockMutation,
  DeleteOptionsBlockMutationFn,
  DeleteOptionsBlockMutationHookResult,
  DeleteOptionsBlockMutationResult,
  DeleteOptionsBlockMutationOptions,
  DeleteMyInfoSettingMutationVariables,
  DeleteMyInfoSettingMutation,
  DeleteMyInfoSettingMutationFn,
  DeleteMyInfoSettingMutationHookResult,
  DeleteMyInfoSettingMutationResult,
  DeleteMyInfoSettingMutationOptions,
  DeleteModuleMutationVariables,
  DeleteModuleMutation,
  DeleteModuleMutationFn,
  DeleteModuleMutationHookResult,
  DeleteModuleMutationResult,
  DeleteModuleMutationOptions,
  DeleteModelConfiguratorAssetMutationVariables,
  DeleteModelConfiguratorAssetMutation,
  DeleteModelConfiguratorAssetMutationFn,
  DeleteModelConfiguratorAssetMutationHookResult,
  DeleteModelConfiguratorAssetMutationResult,
  DeleteModelConfiguratorAssetMutationOptions,
  DeleteModelConfiguratorMutationVariables,
  DeleteModelConfiguratorMutation,
  DeleteModelConfiguratorMutationFn,
  DeleteModelConfiguratorMutationHookResult,
  DeleteModelConfiguratorMutationResult,
  DeleteModelConfiguratorMutationOptions,
  DeleteMobilityModuleEmailAssetMutationVariables,
  DeleteMobilityModuleEmailAssetMutation,
  DeleteMobilityModuleEmailAssetMutationFn,
  DeleteMobilityModuleEmailAssetMutationHookResult,
  DeleteMobilityModuleEmailAssetMutationResult,
  DeleteMobilityModuleEmailAssetMutationOptions,
  DeleteMobilityMutationVariables,
  DeleteMobilityMutation,
  DeleteMobilityMutationFn,
  DeleteMobilityMutationHookResult,
  DeleteMobilityMutationResult,
  DeleteMobilityMutationOptions,
  DeleteMaintenanceModuleAssetMutationVariables,
  DeleteMaintenanceModuleAssetMutation,
  DeleteMaintenanceModuleAssetMutationFn,
  DeleteMaintenanceModuleAssetMutationHookResult,
  DeleteMaintenanceModuleAssetMutationResult,
  DeleteMaintenanceModuleAssetMutationOptions,
  DeleteLeadDocumentMutationVariables,
  DeleteLeadDocumentMutation,
  DeleteLeadDocumentMutationFn,
  DeleteLeadDocumentMutationHookResult,
  DeleteLeadDocumentMutationResult,
  DeleteLeadDocumentMutationOptions,
  DeleteLanguagePackMutationVariables,
  DeleteLanguagePackMutation,
  DeleteLanguagePackMutationFn,
  DeleteLanguagePackMutationHookResult,
  DeleteLanguagePackMutationResult,
  DeleteLanguagePackMutationOptions,
  DeleteLabelsMutationVariables,
  DeleteLabelsMutation,
  DeleteLabelsMutationFn,
  DeleteLabelsMutationHookResult,
  DeleteLabelsMutationResult,
  DeleteLabelsMutationOptions,
  DeleteKycPresetsMutationVariables,
  DeleteKycPresetsMutation,
  DeleteKycPresetsMutationFn,
  DeleteKycPresetsMutationHookResult,
  DeleteKycPresetsMutationResult,
  DeleteKycPresetsMutationOptions,
  DeleteInventoriesMutationVariables,
  DeleteInventoriesMutation,
  DeleteInventoriesMutationFn,
  DeleteInventoriesMutationHookResult,
  DeleteInventoriesMutationResult,
  DeleteInventoriesMutationOptions,
  DeleteInsurerMutationVariables,
  DeleteInsurerMutation,
  DeleteInsurerMutationFn,
  DeleteInsurerMutationHookResult,
  DeleteInsurerMutationResult,
  DeleteInsurerMutationOptions,
  DeleteInsuranceProductMutationVariables,
  DeleteInsuranceProductMutation,
  DeleteInsuranceProductMutationFn,
  DeleteInsuranceProductMutationHookResult,
  DeleteInsuranceProductMutationResult,
  DeleteInsuranceProductMutationOptions,
  DeleteGiftVoucherModuleAssetMutationVariables,
  DeleteGiftVoucherModuleAssetMutation,
  DeleteGiftVoucherModuleAssetMutationFn,
  DeleteGiftVoucherModuleAssetMutationHookResult,
  DeleteGiftVoucherModuleAssetMutationResult,
  DeleteGiftVoucherModuleAssetMutationOptions,
  DeleteGiftVoucherDocumentMutationVariables,
  DeleteGiftVoucherDocumentMutation,
  DeleteGiftVoucherDocumentMutationFn,
  DeleteGiftVoucherDocumentMutationHookResult,
  DeleteGiftVoucherDocumentMutationResult,
  DeleteGiftVoucherDocumentMutationOptions,
  DeleteFiservPaymentSettingsMutationVariables,
  DeleteFiservPaymentSettingsMutation,
  DeleteFiservPaymentSettingsMutationFn,
  DeleteFiservPaymentSettingsMutationHookResult,
  DeleteFiservPaymentSettingsMutationResult,
  DeleteFiservPaymentSettingsMutationOptions,
  DeleteFinderApplicationModuleAssetMutationVariables,
  DeleteFinderApplicationModuleAssetMutation,
  DeleteFinderApplicationModuleAssetMutationFn,
  DeleteFinderApplicationModuleAssetMutationHookResult,
  DeleteFinderApplicationModuleAssetMutationResult,
  DeleteFinderApplicationModuleAssetMutationOptions,
  DeleteFinanceProductMutationVariables,
  DeleteFinanceProductMutation,
  DeleteFinanceProductMutationFn,
  DeleteFinanceProductMutationHookResult,
  DeleteFinanceProductMutationResult,
  DeleteFinanceProductMutationOptions,
  DeleteEventLevelAssetMutationVariables,
  DeleteEventLevelAssetMutation,
  DeleteEventLevelAssetMutationFn,
  DeleteEventLevelAssetMutationHookResult,
  DeleteEventLevelAssetMutationResult,
  DeleteEventLevelAssetMutationOptions,
  DeleteEventApplicationModuleAssetMutationVariables,
  DeleteEventApplicationModuleAssetMutation,
  DeleteEventApplicationModuleAssetMutationFn,
  DeleteEventApplicationModuleAssetMutationHookResult,
  DeleteEventApplicationModuleAssetMutationResult,
  DeleteEventApplicationModuleAssetMutationOptions,
  DeleteEventMutationVariables,
  DeleteEventMutation,
  DeleteEventMutationFn,
  DeleteEventMutationHookResult,
  DeleteEventMutationResult,
  DeleteEventMutationOptions,
  DeleteEndpointMutationVariables,
  DeleteEndpointMutation,
  DeleteEndpointMutationFn,
  DeleteEndpointMutationHookResult,
  DeleteEndpointMutationResult,
  DeleteEndpointMutationOptions,
  DeleteEdmSocialMediaAssetMutationVariables,
  DeleteEdmSocialMediaAssetMutation,
  DeleteEdmSocialMediaAssetMutationFn,
  DeleteEdmSocialMediaAssetMutationHookResult,
  DeleteEdmSocialMediaAssetMutationResult,
  DeleteEdmSocialMediaAssetMutationOptions,
  DeleteEdmEmailSocialMediaMutationVariables,
  DeleteEdmEmailSocialMediaMutation,
  DeleteEdmEmailSocialMediaMutationFn,
  DeleteEdmEmailSocialMediaMutationHookResult,
  DeleteEdmEmailSocialMediaMutationResult,
  DeleteEdmEmailSocialMediaMutationOptions,
  DeleteDocusignSettingMutationVariables,
  DeleteDocusignSettingMutation,
  DeleteDocusignSettingMutationFn,
  DeleteDocusignSettingMutationHookResult,
  DeleteDocusignSettingMutationResult,
  DeleteDocusignSettingMutationOptions,
  DeleteDealerSocialMediaAssetMutationVariables,
  DeleteDealerSocialMediaAssetMutation,
  DeleteDealerSocialMediaAssetMutationFn,
  DeleteDealerSocialMediaAssetMutationHookResult,
  DeleteDealerSocialMediaAssetMutationResult,
  DeleteDealerSocialMediaAssetMutationOptions,
  DeleteDealerSocialMediaMutationVariables,
  DeleteDealerSocialMediaMutation,
  DeleteDealerSocialMediaMutationFn,
  DeleteDealerSocialMediaMutationHookResult,
  DeleteDealerSocialMediaMutationResult,
  DeleteDealerSocialMediaMutationOptions,
  DeleteDealerMutationVariables,
  DeleteDealerMutation,
  DeleteDealerMutationFn,
  DeleteDealerMutationHookResult,
  DeleteDealerMutationResult,
  DeleteDealerMutationOptions,
  DeleteCtsModuleSettingMutationVariables,
  DeleteCtsModuleSettingMutation,
  DeleteCtsModuleSettingMutationFn,
  DeleteCtsModuleSettingMutationHookResult,
  DeleteCtsModuleSettingMutationResult,
  DeleteCtsModuleSettingMutationOptions,
  DeleteConsentsAndDeclarationsMutationVariables,
  DeleteConsentsAndDeclarationsMutation,
  DeleteConsentsAndDeclarationsMutationFn,
  DeleteConsentsAndDeclarationsMutationHookResult,
  DeleteConsentsAndDeclarationsMutationResult,
  DeleteConsentsAndDeclarationsMutationOptions,
  DeleteConfiguratorModuleAssetMutationVariables,
  DeleteConfiguratorModuleAssetMutation,
  DeleteConfiguratorModuleAssetMutationFn,
  DeleteConfiguratorModuleAssetMutationHookResult,
  DeleteConfiguratorModuleAssetMutationResult,
  DeleteConfiguratorModuleAssetMutationOptions,
  DeleteConfiguratorDescriptionImageMutationVariables,
  DeleteConfiguratorDescriptionImageMutation,
  DeleteConfiguratorDescriptionImageMutationFn,
  DeleteConfiguratorDescriptionImageMutationHookResult,
  DeleteConfiguratorDescriptionImageMutationResult,
  DeleteConfiguratorDescriptionImageMutationOptions,
  DeleteCompanyAssetMutationVariables,
  DeleteCompanyAssetMutation,
  DeleteCompanyAssetMutationFn,
  DeleteCompanyAssetMutationHookResult,
  DeleteCompanyAssetMutationResult,
  DeleteCompanyAssetMutationOptions,
  DeleteCompanyMutationVariables,
  DeleteCompanyMutation,
  DeleteCompanyMutationFn,
  DeleteCompanyMutationHookResult,
  DeleteCompanyMutationResult,
  DeleteCompanyMutationOptions,
  DeleteCampaignMutationVariables,
  DeleteCampaignMutation,
  DeleteCampaignMutationFn,
  DeleteCampaignMutationHookResult,
  DeleteCampaignMutationResult,
  DeleteCampaignMutationOptions,
  DeleteBannerImageMutationVariables,
  DeleteBannerImageMutation,
  DeleteBannerImageMutationFn,
  DeleteBannerImageMutationHookResult,
  DeleteBannerImageMutationResult,
  DeleteBannerImageMutationOptions,
  DeleteBannerMutationVariables,
  DeleteBannerMutation,
  DeleteBannerMutationFn,
  DeleteBannerMutationHookResult,
  DeleteBannerMutationResult,
  DeleteBannerMutationOptions,
  DeleteBankAssetMutationVariables,
  DeleteBankAssetMutation,
  DeleteBankAssetMutationFn,
  DeleteBankAssetMutationHookResult,
  DeleteBankAssetMutationResult,
  DeleteBankAssetMutationOptions,
  DeleteBankMutationVariables,
  DeleteBankMutation,
  DeleteBankMutationFn,
  DeleteBankMutationHookResult,
  DeleteBankMutationResult,
  DeleteBankMutationOptions,
  DeleteAutoplayModuleSettingMutationVariables,
  DeleteAutoplayModuleSettingMutation,
  DeleteAutoplayModuleSettingMutationFn,
  DeleteAutoplayModuleSettingMutationHookResult,
  DeleteAutoplayModuleSettingMutationResult,
  DeleteAutoplayModuleSettingMutationOptions,
  DeleteAppointmentModuleAssetMutationVariables,
  DeleteAppointmentModuleAssetMutation,
  DeleteAppointmentModuleAssetMutationFn,
  DeleteAppointmentModuleAssetMutationHookResult,
  DeleteAppointmentModuleAssetMutationResult,
  DeleteAppointmentModuleAssetMutationOptions,
  DeleteApplicationDocumentMutationVariables,
  DeleteApplicationDocumentMutation,
  DeleteApplicationDocumentMutationFn,
  DeleteApplicationDocumentMutationHookResult,
  DeleteApplicationDocumentMutationResult,
  DeleteApplicationDocumentMutationOptions,
  DeleteAdyenPaymentSettingsMutationVariables,
  DeleteAdyenPaymentSettingsMutation,
  DeleteAdyenPaymentSettingsMutationFn,
  DeleteAdyenPaymentSettingsMutationHookResult,
  DeleteAdyenPaymentSettingsMutationResult,
  DeleteAdyenPaymentSettingsMutationOptions,
  DeleteAdditionalDetailMutationVariables,
  DeleteAdditionalDetailMutation,
  DeleteAdditionalDetailMutationFn,
  DeleteAdditionalDetailMutationHookResult,
  DeleteAdditionalDetailMutationResult,
  DeleteAdditionalDetailMutationOptions,
  DeclineApplicationMutationVariables,
  DeclineApplicationMutation,
  DeclineApplicationMutationFn,
  DeclineApplicationMutationHookResult,
  DeclineApplicationMutationResult,
  DeclineApplicationMutationOptions,
  CreateWhatsappLiveChatSettingsMutationVariables,
  CreateWhatsappLiveChatSettingsMutation,
  CreateWhatsappLiveChatSettingsMutationFn,
  CreateWhatsappLiveChatSettingsMutationHookResult,
  CreateWhatsappLiveChatSettingsMutationResult,
  CreateWhatsappLiveChatSettingsMutationOptions,
  CreateWhatsappLiveChatModuleMutationVariables,
  CreateWhatsappLiveChatModuleMutation,
  CreateWhatsappLiveChatModuleMutationFn,
  CreateWhatsappLiveChatModuleMutationHookResult,
  CreateWhatsappLiveChatModuleMutationResult,
  CreateWhatsappLiveChatModuleMutationOptions,
  CreateWebsiteSocialMediaMutationVariables,
  CreateWebsiteSocialMediaMutation,
  CreateWebsiteSocialMediaMutationFn,
  CreateWebsiteSocialMediaMutationHookResult,
  CreateWebsiteSocialMediaMutationResult,
  CreateWebsiteSocialMediaMutationOptions,
  CreateWebsiteModuleMutationVariables,
  CreateWebsiteModuleMutation,
  CreateWebsiteModuleMutationFn,
  CreateWebsiteModuleMutationHookResult,
  CreateWebsiteModuleMutationResult,
  CreateWebsiteModuleMutationOptions,
  CreateWebPageEndpointMutationVariables,
  CreateWebPageEndpointMutation,
  CreateWebPageEndpointMutationFn,
  CreateWebPageEndpointMutationHookResult,
  CreateWebPageEndpointMutationResult,
  CreateWebPageEndpointMutationOptions,
  CreateWebPageMutationVariables,
  CreateWebPageMutation,
  CreateWebPageMutationFn,
  CreateWebPageMutationHookResult,
  CreateWebPageMutationResult,
  CreateWebPageMutationOptions,
  CreateWebCalcSettingMutationVariables,
  CreateWebCalcSettingMutation,
  CreateWebCalcSettingMutationFn,
  CreateWebCalcSettingMutationHookResult,
  CreateWebCalcSettingMutationResult,
  CreateWebCalcSettingMutationOptions,
  CreateVisitAppointmentModuleMutationVariables,
  CreateVisitAppointmentModuleMutation,
  CreateVisitAppointmentModuleMutationFn,
  CreateVisitAppointmentModuleMutationHookResult,
  CreateVisitAppointmentModuleMutationResult,
  CreateVisitAppointmentModuleMutationOptions,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationFn,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationHookResult,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationResult,
  CreateVehicleDataWithPorscheCodeIntegrationModuleMutationOptions,
  CreateVariantConfiguratorMutationVariables,
  CreateVariantConfiguratorMutation,
  CreateVariantConfiguratorMutationFn,
  CreateVariantConfiguratorMutationHookResult,
  CreateVariantConfiguratorMutationResult,
  CreateVariantConfiguratorMutationOptions,
  CreateVariantMutationVariables,
  CreateVariantMutation,
  CreateVariantMutationFn,
  CreateVariantMutationHookResult,
  CreateVariantMutationResult,
  CreateVariantMutationOptions,
  CreateUserlikeChatbotSettingsMutationVariables,
  CreateUserlikeChatbotSettingsMutation,
  CreateUserlikeChatbotSettingsMutationFn,
  CreateUserlikeChatbotSettingsMutationHookResult,
  CreateUserlikeChatbotSettingsMutationResult,
  CreateUserlikeChatbotSettingsMutationOptions,
  CreateUserlikeChatbotModuleMutationVariables,
  CreateUserlikeChatbotModuleMutation,
  CreateUserlikeChatbotModuleMutationFn,
  CreateUserlikeChatbotModuleMutationHookResult,
  CreateUserlikeChatbotModuleMutationResult,
  CreateUserlikeChatbotModuleMutationOptions,
  CreateUserGroupMutationVariables,
  CreateUserGroupMutation,
  CreateUserGroupMutationFn,
  CreateUserGroupMutationHookResult,
  CreateUserGroupMutationResult,
  CreateUserGroupMutationOptions,
  CreateUserMutationVariables,
  CreateUserMutation,
  CreateUserMutationFn,
  CreateUserMutationHookResult,
  CreateUserMutationResult,
  CreateUserMutationOptions,
  CreateTtbPaymentSettingsMutationVariables,
  CreateTtbPaymentSettingsMutation,
  CreateTtbPaymentSettingsMutationFn,
  CreateTtbPaymentSettingsMutationHookResult,
  CreateTtbPaymentSettingsMutationResult,
  CreateTtbPaymentSettingsMutationOptions,
  CreateTtbPaymentModuleMutationVariables,
  CreateTtbPaymentModuleMutation,
  CreateTtbPaymentModuleMutationFn,
  CreateTtbPaymentModuleMutationHookResult,
  CreateTtbPaymentModuleMutationResult,
  CreateTtbPaymentModuleMutationOptions,
  CreateTrimBlockMutationVariables,
  CreateTrimBlockMutation,
  CreateTrimBlockMutationFn,
  CreateTrimBlockMutationHookResult,
  CreateTrimBlockMutationResult,
  CreateTrimBlockMutationOptions,
  CreateTradeInModuleMutationVariables,
  CreateTradeInModuleMutation,
  CreateTradeInModuleMutationFn,
  CreateTradeInModuleMutationHookResult,
  CreateTradeInModuleMutationResult,
  CreateTradeInModuleMutationOptions,
  CreateTextConsentsAndDeclarationsMutationVariables,
  CreateTextConsentsAndDeclarationsMutation,
  CreateTextConsentsAndDeclarationsMutationFn,
  CreateTextConsentsAndDeclarationsMutationHookResult,
  CreateTextConsentsAndDeclarationsMutationResult,
  CreateTextConsentsAndDeclarationsMutationOptions,
  CreateSubmodelMutationVariables,
  CreateSubmodelMutation,
  CreateSubmodelMutationFn,
  CreateSubmodelMutationHookResult,
  CreateSubmodelMutationResult,
  CreateSubmodelMutationOptions,
  CreateStandardApplicationPublicAccessEntrypointMutationVariables,
  CreateStandardApplicationPublicAccessEntrypointMutation,
  CreateStandardApplicationPublicAccessEntrypointMutationFn,
  CreateStandardApplicationPublicAccessEntrypointMutationHookResult,
  CreateStandardApplicationPublicAccessEntrypointMutationResult,
  CreateStandardApplicationPublicAccessEntrypointMutationOptions,
  CreateStandardApplicationModuleMutationVariables,
  CreateStandardApplicationModuleMutation,
  CreateStandardApplicationModuleMutationFn,
  CreateStandardApplicationModuleMutationHookResult,
  CreateStandardApplicationModuleMutationResult,
  CreateStandardApplicationModuleMutationOptions,
  CreateStandardApplicationEntrypointMutationVariables,
  CreateStandardApplicationEntrypointMutation,
  CreateStandardApplicationEntrypointMutationFn,
  CreateStandardApplicationEntrypointMutationHookResult,
  CreateStandardApplicationEntrypointMutationResult,
  CreateStandardApplicationEntrypointMutationOptions,
  CreateSimpleVehicleManagementModuleMutationVariables,
  CreateSimpleVehicleManagementModuleMutation,
  CreateSimpleVehicleManagementModuleMutationFn,
  CreateSimpleVehicleManagementModuleMutationHookResult,
  CreateSimpleVehicleManagementModuleMutationResult,
  CreateSimpleVehicleManagementModuleMutationOptions,
  CreateShowroomVisitAppointmentFromLeadMutationVariables,
  CreateShowroomVisitAppointmentFromLeadMutation,
  CreateShowroomVisitAppointmentFromLeadMutationFn,
  CreateShowroomVisitAppointmentFromLeadMutationHookResult,
  CreateShowroomVisitAppointmentFromLeadMutationResult,
  CreateShowroomVisitAppointmentFromLeadMutationOptions,
  CreateSalesOfferModuleMutationVariables,
  CreateSalesOfferModuleMutation,
  CreateSalesOfferModuleMutationFn,
  CreateSalesOfferModuleMutationHookResult,
  CreateSalesOfferModuleMutationResult,
  CreateSalesOfferModuleMutationOptions,
  CreateSalesOfferMutationVariables,
  CreateSalesOfferMutation,
  CreateSalesOfferMutationFn,
  CreateSalesOfferMutationHookResult,
  CreateSalesOfferMutationResult,
  CreateSalesOfferMutationOptions,
  CreateSalesControlBoardModuleMutationVariables,
  CreateSalesControlBoardModuleMutation,
  CreateSalesControlBoardModuleMutationFn,
  CreateSalesControlBoardModuleMutationHookResult,
  CreateSalesControlBoardModuleMutationResult,
  CreateSalesControlBoardModuleMutationOptions,
  CreateRouterMenuMutationVariables,
  CreateRouterMenuMutation,
  CreateRouterMenuMutationFn,
  CreateRouterMenuMutationHookResult,
  CreateRouterMenuMutationResult,
  CreateRouterMenuMutationOptions,
  CreateRouterMutationVariables,
  CreateRouterMutation,
  CreateRouterMutationFn,
  CreateRouterMutationHookResult,
  CreateRouterMutationResult,
  CreateRouterMutationOptions,
  CreateRoleMutationVariables,
  CreateRoleMutation,
  CreateRoleMutationFn,
  CreateRoleMutationHookResult,
  CreateRoleMutationResult,
  CreateRoleMutationOptions,
  CreatePromoCodeModuleMutationVariables,
  CreatePromoCodeModuleMutation,
  CreatePromoCodeModuleMutationFn,
  CreatePromoCodeModuleMutationHookResult,
  CreatePromoCodeModuleMutationResult,
  CreatePromoCodeModuleMutationOptions,
  CreatePromoCodeMutationVariables,
  CreatePromoCodeMutation,
  CreatePromoCodeMutationFn,
  CreatePromoCodeMutationHookResult,
  CreatePromoCodeMutationResult,
  CreatePromoCodeMutationOptions,
  CreatePorscheRetainModuleMutationVariables,
  CreatePorscheRetainModuleMutation,
  CreatePorscheRetainModuleMutationFn,
  CreatePorscheRetainModuleMutationHookResult,
  CreatePorscheRetainModuleMutationResult,
  CreatePorscheRetainModuleMutationOptions,
  CreatePorschePaymentSettingsMutationVariables,
  CreatePorschePaymentSettingsMutation,
  CreatePorschePaymentSettingsMutationFn,
  CreatePorschePaymentSettingsMutationHookResult,
  CreatePorschePaymentSettingsMutationResult,
  CreatePorschePaymentSettingsMutationOptions,
  CreatePorschePaymentModuleMutationVariables,
  CreatePorschePaymentModuleMutation,
  CreatePorschePaymentModuleMutationFn,
  CreatePorschePaymentModuleMutationHookResult,
  CreatePorschePaymentModuleMutationResult,
  CreatePorschePaymentModuleMutationOptions,
  CreatePorscheMasterDataModuleMutationVariables,
  CreatePorscheMasterDataModuleMutation,
  CreatePorscheMasterDataModuleMutationFn,
  CreatePorscheMasterDataModuleMutationHookResult,
  CreatePorscheMasterDataModuleMutationResult,
  CreatePorscheMasterDataModuleMutationOptions,
  CreatePorscheIdModuleMutationVariables,
  CreatePorscheIdModuleMutation,
  CreatePorscheIdModuleMutationFn,
  CreatePorscheIdModuleMutationHookResult,
  CreatePorscheIdModuleMutationResult,
  CreatePorscheIdModuleMutationOptions,
  CreatePayGatePaymentSettingsMutationVariables,
  CreatePayGatePaymentSettingsMutation,
  CreatePayGatePaymentSettingsMutationFn,
  CreatePayGatePaymentSettingsMutationHookResult,
  CreatePayGatePaymentSettingsMutationResult,
  CreatePayGatePaymentSettingsMutationOptions,
  CreatePayGatePaymentModuleMutationVariables,
  CreatePayGatePaymentModuleMutation,
  CreatePayGatePaymentModuleMutationFn,
  CreatePayGatePaymentModuleMutationHookResult,
  CreatePayGatePaymentModuleMutationResult,
  CreatePayGatePaymentModuleMutationOptions,
  CreatePackageSettingMutationVariables,
  CreatePackageSettingMutation,
  CreatePackageSettingMutationFn,
  CreatePackageSettingMutationHookResult,
  CreatePackageSettingMutationResult,
  CreatePackageSettingMutationOptions,
  CreatePackageBlockMutationVariables,
  CreatePackageBlockMutation,
  CreatePackageBlockMutationFn,
  CreatePackageBlockMutationHookResult,
  CreatePackageBlockMutationResult,
  CreatePackageBlockMutationOptions,
  CreateOptionsBlockMutationVariables,
  CreateOptionsBlockMutation,
  CreateOptionsBlockMutationFn,
  CreateOptionsBlockMutationHookResult,
  CreateOptionsBlockMutationResult,
  CreateOptionsBlockMutationOptions,
  CreateOidcModuleMutationVariables,
  CreateOidcModuleMutation,
  CreateOidcModuleMutationFn,
  CreateOidcModuleMutationHookResult,
  CreateOidcModuleMutationResult,
  CreateOidcModuleMutationOptions,
  CreateNewContactFromBpMutationVariables,
  CreateNewContactFromBpMutation,
  CreateNewContactFromBpMutationFn,
  CreateNewContactFromBpMutationHookResult,
  CreateNewContactFromBpMutationResult,
  CreateNewContactFromBpMutationOptions,
  CreateNamirialSigningModuleMutationVariables,
  CreateNamirialSigningModuleMutation,
  CreateNamirialSigningModuleMutationFn,
  CreateNamirialSigningModuleMutationHookResult,
  CreateNamirialSigningModuleMutationResult,
  CreateNamirialSigningModuleMutationOptions,
  CreateMyInfoSettingMutationVariables,
  CreateMyInfoSettingMutation,
  CreateMyInfoSettingMutationFn,
  CreateMyInfoSettingMutationHookResult,
  CreateMyInfoSettingMutationResult,
  CreateMyInfoSettingMutationOptions,
  CreateMyInfoModuleMutationVariables,
  CreateMyInfoModuleMutation,
  CreateMyInfoModuleMutationFn,
  CreateMyInfoModuleMutationHookResult,
  CreateMyInfoModuleMutationResult,
  CreateMyInfoModuleMutationOptions,
  CreateModelConfiguratorMutationVariables,
  CreateModelConfiguratorMutation,
  CreateModelConfiguratorMutationFn,
  CreateModelConfiguratorMutationHookResult,
  CreateModelConfiguratorMutationResult,
  CreateModelConfiguratorMutationOptions,
  CreateModelMutationVariables,
  CreateModelMutation,
  CreateModelMutationFn,
  CreateModelMutationHookResult,
  CreateModelMutationResult,
  CreateModelMutationOptions,
  CreateMobilityModuleMutationVariables,
  CreateMobilityModuleMutation,
  CreateMobilityModuleMutationFn,
  CreateMobilityModuleMutationHookResult,
  CreateMobilityModuleMutationResult,
  CreateMobilityModuleMutationOptions,
  CreateMobilityApplicationEntrypointMutationVariables,
  CreateMobilityApplicationEntrypointMutation,
  CreateMobilityApplicationEntrypointMutationFn,
  CreateMobilityApplicationEntrypointMutationHookResult,
  CreateMobilityApplicationEntrypointMutationResult,
  CreateMobilityApplicationEntrypointMutationOptions,
  CreateMobilityAddonMutationVariables,
  CreateMobilityAddonMutation,
  CreateMobilityAddonMutationFn,
  CreateMobilityAddonMutationHookResult,
  CreateMobilityAddonMutationResult,
  CreateMobilityAddonMutationOptions,
  CreateMobilityAdditionalInfoMutationVariables,
  CreateMobilityAdditionalInfoMutation,
  CreateMobilityAdditionalInfoMutationFn,
  CreateMobilityAdditionalInfoMutationHookResult,
  CreateMobilityAdditionalInfoMutationResult,
  CreateMobilityAdditionalInfoMutationOptions,
  CreateMarketingModuleMutationVariables,
  CreateMarketingModuleMutation,
  CreateMarketingModuleMutationFn,
  CreateMarketingModuleMutationHookResult,
  CreateMarketingModuleMutationResult,
  CreateMarketingModuleMutationOptions,
  CreateMarketingConsentsAndDeclarationsMutationVariables,
  CreateMarketingConsentsAndDeclarationsMutation,
  CreateMarketingConsentsAndDeclarationsMutationFn,
  CreateMarketingConsentsAndDeclarationsMutationHookResult,
  CreateMarketingConsentsAndDeclarationsMutationResult,
  CreateMarketingConsentsAndDeclarationsMutationOptions,
  CreateMakeMutationVariables,
  CreateMakeMutation,
  CreateMakeMutationFn,
  CreateMakeMutationHookResult,
  CreateMakeMutationResult,
  CreateMakeMutationOptions,
  CreateMaintenanceModuleMutationVariables,
  CreateMaintenanceModuleMutation,
  CreateMaintenanceModuleMutationFn,
  CreateMaintenanceModuleMutationHookResult,
  CreateMaintenanceModuleMutationResult,
  CreateMaintenanceModuleMutationOptions,
  CreateLocalCustomerManagementMutationVariables,
  CreateLocalCustomerManagementMutation,
  CreateLocalCustomerManagementMutationFn,
  CreateLocalCustomerManagementMutationHookResult,
  CreateLocalCustomerManagementMutationResult,
  CreateLocalCustomerManagementMutationOptions,
  CreateLeadListEndpointMutationVariables,
  CreateLeadListEndpointMutation,
  CreateLeadListEndpointMutationFn,
  CreateLeadListEndpointMutationHookResult,
  CreateLeadListEndpointMutationResult,
  CreateLeadListEndpointMutationOptions,
  CreateLeadFollowUpMutationVariables,
  CreateLeadFollowUpMutation,
  CreateLeadFollowUpMutationFn,
  CreateLeadFollowUpMutationHookResult,
  CreateLeadFollowUpMutationResult,
  CreateLeadFollowUpMutationOptions,
  CreateLaunchPadModuleMutationVariables,
  CreateLaunchPadModuleMutation,
  CreateLaunchPadModuleMutationFn,
  CreateLaunchPadModuleMutationHookResult,
  CreateLaunchPadModuleMutationResult,
  CreateLaunchPadModuleMutationOptions,
  CreateLaunchPadApplicationEntrypointMutationVariables,
  CreateLaunchPadApplicationEntrypointMutation,
  CreateLaunchPadApplicationEntrypointMutationFn,
  CreateLaunchPadApplicationEntrypointMutationHookResult,
  CreateLaunchPadApplicationEntrypointMutationResult,
  CreateLaunchPadApplicationEntrypointMutationOptions,
  CreateLanguagePackMutationVariables,
  CreateLanguagePackMutation,
  CreateLanguagePackMutationFn,
  CreateLanguagePackMutationHookResult,
  CreateLanguagePackMutationResult,
  CreateLanguagePackMutationOptions,
  CreateLabelsModuleMutationVariables,
  CreateLabelsModuleMutation,
  CreateLabelsModuleMutationFn,
  CreateLabelsModuleMutationHookResult,
  CreateLabelsModuleMutationResult,
  CreateLabelsModuleMutationOptions,
  CreateLabelsMutationVariables,
  CreateLabelsMutation,
  CreateLabelsMutationFn,
  CreateLabelsMutationHookResult,
  CreateLabelsMutationResult,
  CreateLabelsMutationOptions,
  CreateKycPresetMutationVariables,
  CreateKycPresetMutation,
  CreateKycPresetMutationFn,
  CreateKycPresetMutationHookResult,
  CreateKycPresetMutationResult,
  CreateKycPresetMutationOptions,
  CreateInventoryMutationVariables,
  CreateInventoryMutation,
  CreateInventoryMutationFn,
  CreateInventoryMutationHookResult,
  CreateInventoryMutationResult,
  CreateInventoryMutationOptions,
  CreateInsurerMutationVariables,
  CreateInsurerMutation,
  CreateInsurerMutationFn,
  CreateInsurerMutationHookResult,
  CreateInsurerMutationResult,
  CreateInsurerMutationOptions,
  CreateInsuranceProductMutationVariables,
  CreateInsuranceProductMutation,
  CreateInsuranceProductMutationFn,
  CreateInsuranceProductMutationHookResult,
  CreateInsuranceProductMutationResult,
  CreateInsuranceProductMutationOptions,
  CreateInsuranceModuleMutationVariables,
  CreateInsuranceModuleMutation,
  CreateInsuranceModuleMutationFn,
  CreateInsuranceModuleMutationHookResult,
  CreateInsuranceModuleMutationResult,
  CreateInsuranceModuleMutationOptions,
  CreateGroupConsentsAndDeclarationsMutationVariables,
  CreateGroupConsentsAndDeclarationsMutation,
  CreateGroupConsentsAndDeclarationsMutationFn,
  CreateGroupConsentsAndDeclarationsMutationHookResult,
  CreateGroupConsentsAndDeclarationsMutationResult,
  CreateGroupConsentsAndDeclarationsMutationOptions,
  CreateGiftVoucherModuleMutationVariables,
  CreateGiftVoucherModuleMutation,
  CreateGiftVoucherModuleMutationFn,
  CreateGiftVoucherModuleMutationHookResult,
  CreateGiftVoucherModuleMutationResult,
  CreateGiftVoucherModuleMutationOptions,
  CreateFiservPaymentSettingsMutationVariables,
  CreateFiservPaymentSettingsMutation,
  CreateFiservPaymentSettingsMutationFn,
  CreateFiservPaymentSettingsMutationHookResult,
  CreateFiservPaymentSettingsMutationResult,
  CreateFiservPaymentSettingsMutationOptions,
  CreateFiservPaymentModuleMutationVariables,
  CreateFiservPaymentModuleMutation,
  CreateFiservPaymentModuleMutationFn,
  CreateFiservPaymentModuleMutationHookResult,
  CreateFiservPaymentModuleMutationResult,
  CreateFiservPaymentModuleMutationOptions,
  CreateFinderVehicleManagementModuleMutationVariables,
  CreateFinderVehicleManagementModuleMutation,
  CreateFinderVehicleManagementModuleMutationFn,
  CreateFinderVehicleManagementModuleMutationHookResult,
  CreateFinderVehicleManagementModuleMutationResult,
  CreateFinderVehicleManagementModuleMutationOptions,
  CreateFinderApplicationPublicAccessEntrypointMutationVariables,
  CreateFinderApplicationPublicAccessEntrypointMutation,
  CreateFinderApplicationPublicAccessEntrypointMutationFn,
  CreateFinderApplicationPublicAccessEntrypointMutationHookResult,
  CreateFinderApplicationPublicAccessEntrypointMutationResult,
  CreateFinderApplicationPublicAccessEntrypointMutationOptions,
  CreateFinderApplicationPrivateModuleMutationVariables,
  CreateFinderApplicationPrivateModuleMutation,
  CreateFinderApplicationPrivateModuleMutationFn,
  CreateFinderApplicationPrivateModuleMutationHookResult,
  CreateFinderApplicationPrivateModuleMutationResult,
  CreateFinderApplicationPrivateModuleMutationOptions,
  CreateFinderApplicationPublicModuleMutationVariables,
  CreateFinderApplicationPublicModuleMutation,
  CreateFinderApplicationPublicModuleMutationFn,
  CreateFinderApplicationPublicModuleMutationHookResult,
  CreateFinderApplicationPublicModuleMutationResult,
  CreateFinderApplicationPublicModuleMutationOptions,
  CreateFinderApplicationEntrypointMutationVariables,
  CreateFinderApplicationEntrypointMutation,
  CreateFinderApplicationEntrypointMutationFn,
  CreateFinderApplicationEntrypointMutationHookResult,
  CreateFinderApplicationEntrypointMutationResult,
  CreateFinderApplicationEntrypointMutationOptions,
  CreateFinanceProductMutationVariables,
  CreateFinanceProductMutation,
  CreateFinanceProductMutationFn,
  CreateFinanceProductMutationHookResult,
  CreateFinanceProductMutationResult,
  CreateFinanceProductMutationOptions,
  CreateEventApplicationModuleMutationVariables,
  CreateEventApplicationModuleMutation,
  CreateEventApplicationModuleMutationFn,
  CreateEventApplicationModuleMutationHookResult,
  CreateEventApplicationModuleMutationResult,
  CreateEventApplicationModuleMutationOptions,
  CreateEventApplicationEntrypointMutationVariables,
  CreateEventApplicationEntrypointMutation,
  CreateEventApplicationEntrypointMutationFn,
  CreateEventApplicationEntrypointMutationHookResult,
  CreateEventApplicationEntrypointMutationResult,
  CreateEventApplicationEntrypointMutationOptions,
  CreateEventMutationVariables,
  CreateEventMutation,
  CreateEventMutationFn,
  CreateEventMutationHookResult,
  CreateEventMutationResult,
  CreateEventMutationOptions,
  CreateEdmEmailSocialMediaMutationVariables,
  CreateEdmEmailSocialMediaMutation,
  CreateEdmEmailSocialMediaMutationFn,
  CreateEdmEmailSocialMediaMutationHookResult,
  CreateEdmEmailSocialMediaMutationResult,
  CreateEdmEmailSocialMediaMutationOptions,
  CreateDummyWelcomePageEndpointMutationVariables,
  CreateDummyWelcomePageEndpointMutation,
  CreateDummyWelcomePageEndpointMutationFn,
  CreateDummyWelcomePageEndpointMutationHookResult,
  CreateDummyWelcomePageEndpointMutationResult,
  CreateDummyWelcomePageEndpointMutationOptions,
  CreateDummyPrivatePageEndpointMutationVariables,
  CreateDummyPrivatePageEndpointMutation,
  CreateDummyPrivatePageEndpointMutationFn,
  CreateDummyPrivatePageEndpointMutationHookResult,
  CreateDummyPrivatePageEndpointMutationResult,
  CreateDummyPrivatePageEndpointMutationOptions,
  CreateDocusignSettingMutationVariables,
  CreateDocusignSettingMutation,
  CreateDocusignSettingMutationFn,
  CreateDocusignSettingMutationHookResult,
  CreateDocusignSettingMutationResult,
  CreateDocusignSettingMutationOptions,
  CreateDocusignModuleMutationVariables,
  CreateDocusignModuleMutation,
  CreateDocusignModuleMutationFn,
  CreateDocusignModuleMutationHookResult,
  CreateDocusignModuleMutationResult,
  CreateDocusignModuleMutationOptions,
  CreateDealerSocialMediaMutationVariables,
  CreateDealerSocialMediaMutation,
  CreateDealerSocialMediaMutationFn,
  CreateDealerSocialMediaMutationHookResult,
  CreateDealerSocialMediaMutationResult,
  CreateDealerSocialMediaMutationOptions,
  CreateDealerMutationVariables,
  CreateDealerMutation,
  CreateDealerMutationFn,
  CreateDealerMutationHookResult,
  CreateDealerMutationResult,
  CreateDealerMutationOptions,
  CreateCustomerListEndpointMutationVariables,
  CreateCustomerListEndpointMutation,
  CreateCustomerListEndpointMutationFn,
  CreateCustomerListEndpointMutationHookResult,
  CreateCustomerListEndpointMutationResult,
  CreateCustomerListEndpointMutationOptions,
  CreateCtsModuleSettingMutationVariables,
  CreateCtsModuleSettingMutation,
  CreateCtsModuleSettingMutationFn,
  CreateCtsModuleSettingMutationHookResult,
  CreateCtsModuleSettingMutationResult,
  CreateCtsModuleSettingMutationOptions,
  CreateCtsModuleMutationVariables,
  CreateCtsModuleMutation,
  CreateCtsModuleMutationFn,
  CreateCtsModuleMutationHookResult,
  CreateCtsModuleMutationResult,
  CreateCtsModuleMutationOptions,
  CreateConsentsAndDeclarationsModuleMutationVariables,
  CreateConsentsAndDeclarationsModuleMutation,
  CreateConsentsAndDeclarationsModuleMutationFn,
  CreateConsentsAndDeclarationsModuleMutationHookResult,
  CreateConsentsAndDeclarationsModuleMutationResult,
  CreateConsentsAndDeclarationsModuleMutationOptions,
  CreateConfiguratorModuleMutationVariables,
  CreateConfiguratorModuleMutation,
  CreateConfiguratorModuleMutationFn,
  CreateConfiguratorModuleMutationHookResult,
  CreateConfiguratorModuleMutationResult,
  CreateConfiguratorModuleMutationOptions,
  CreateConfiguratorApplicationEntrypointMutationVariables,
  CreateConfiguratorApplicationEntrypointMutation,
  CreateConfiguratorApplicationEntrypointMutationFn,
  CreateConfiguratorApplicationEntrypointMutationHookResult,
  CreateConfiguratorApplicationEntrypointMutationResult,
  CreateConfiguratorApplicationEntrypointMutationOptions,
  CreateCompanyMutationVariables,
  CreateCompanyMutation,
  CreateCompanyMutationFn,
  CreateCompanyMutationHookResult,
  CreateCompanyMutationResult,
  CreateCompanyMutationOptions,
  CreateColorBlockMutationVariables,
  CreateColorBlockMutation,
  CreateColorBlockMutationFn,
  CreateColorBlockMutationHookResult,
  CreateColorBlockMutationResult,
  CreateColorBlockMutationOptions,
  CreateCheckboxConsentsAndDeclarationsMutationVariables,
  CreateCheckboxConsentsAndDeclarationsMutation,
  CreateCheckboxConsentsAndDeclarationsMutationFn,
  CreateCheckboxConsentsAndDeclarationsMutationHookResult,
  CreateCheckboxConsentsAndDeclarationsMutationResult,
  CreateCheckboxConsentsAndDeclarationsMutationOptions,
  CreateCapModuleMutationVariables,
  CreateCapModuleMutation,
  CreateCapModuleMutationFn,
  CreateCapModuleMutationHookResult,
  CreateCapModuleMutationResult,
  CreateCapModuleMutationOptions,
  CreateCampaignMutationVariables,
  CreateCampaignMutation,
  CreateCampaignMutationFn,
  CreateCampaignMutationHookResult,
  CreateCampaignMutationResult,
  CreateCampaignMutationOptions,
  CreateBasicSigningModuleMutationVariables,
  CreateBasicSigningModuleMutation,
  CreateBasicSigningModuleMutationFn,
  CreateBasicSigningModuleMutationHookResult,
  CreateBasicSigningModuleMutationResult,
  CreateBasicSigningModuleMutationOptions,
  CreateBannerMutationVariables,
  CreateBannerMutation,
  CreateBannerMutationFn,
  CreateBannerMutationHookResult,
  CreateBannerMutationResult,
  CreateBannerMutationOptions,
  CreateBankModuleMutationVariables,
  CreateBankModuleMutation,
  CreateBankModuleMutationFn,
  CreateBankModuleMutationHookResult,
  CreateBankModuleMutationResult,
  CreateBankModuleMutationOptions,
  CreateBankMutationVariables,
  CreateBankMutation,
  CreateBankMutationFn,
  CreateBankMutationHookResult,
  CreateBankMutationResult,
  CreateBankMutationOptions,
  CreateAutoplayModuleSettingMutationVariables,
  CreateAutoplayModuleSettingMutation,
  CreateAutoplayModuleSettingMutationFn,
  CreateAutoplayModuleSettingMutationHookResult,
  CreateAutoplayModuleSettingMutationResult,
  CreateAutoplayModuleSettingMutationOptions,
  CreateAutoplayModuleMutationVariables,
  CreateAutoplayModuleMutation,
  CreateAutoplayModuleMutationFn,
  CreateAutoplayModuleMutationHookResult,
  CreateAutoplayModuleMutationResult,
  CreateAutoplayModuleMutationOptions,
  CreateAppointmentModuleMutationVariables,
  CreateAppointmentModuleMutation,
  CreateAppointmentModuleMutationFn,
  CreateAppointmentModuleMutationHookResult,
  CreateAppointmentModuleMutationResult,
  CreateAppointmentModuleMutationOptions,
  CreateAppointmentFromLeadMutationVariables,
  CreateAppointmentFromLeadMutation,
  CreateAppointmentFromLeadMutationFn,
  CreateAppointmentFromLeadMutationHookResult,
  CreateAppointmentFromLeadMutationResult,
  CreateAppointmentFromLeadMutationOptions,
  CreateApplicationListEndpointMutationVariables,
  CreateApplicationListEndpointMutation,
  CreateApplicationListEndpointMutationFn,
  CreateApplicationListEndpointMutationHookResult,
  CreateApplicationListEndpointMutationResult,
  CreateApplicationListEndpointMutationOptions,
  CreateAdyenPaymentSettingsMutationVariables,
  CreateAdyenPaymentSettingsMutation,
  CreateAdyenPaymentSettingsMutationFn,
  CreateAdyenPaymentSettingsMutationHookResult,
  CreateAdyenPaymentSettingsMutationResult,
  CreateAdyenPaymentSettingsMutationOptions,
  CreateAdyenPaymentModuleMutationVariables,
  CreateAdyenPaymentModuleMutation,
  CreateAdyenPaymentModuleMutationFn,
  CreateAdyenPaymentModuleMutationHookResult,
  CreateAdyenPaymentModuleMutationResult,
  CreateAdyenPaymentModuleMutationOptions,
  CreateAdditionalDetailMutationVariables,
  CreateAdditionalDetailMutation,
  CreateAdditionalDetailMutationFn,
  CreateAdditionalDetailMutationHookResult,
  CreateAdditionalDetailMutationResult,
  CreateAdditionalDetailMutationOptions,
  CopyVariantConfiguratorMutationVariables,
  CopyVariantConfiguratorMutation,
  CopyVariantConfiguratorMutationFn,
  CopyVariantConfiguratorMutationHookResult,
  CopyVariantConfiguratorMutationResult,
  CopyVariantConfiguratorMutationOptions,
  ContinueApplicationMutationVariables,
  ContinueApplicationMutation,
  ContinueApplicationMutationFn,
  ContinueApplicationMutationHookResult,
  ContinueApplicationMutationResult,
  ContinueApplicationMutationOptions,
  ContactApplicationMutationVariables,
  ContactApplicationMutation,
  ContactApplicationMutationFn,
  ContactApplicationMutationHookResult,
  ContactApplicationMutationResult,
  ContactApplicationMutationOptions,
  ConfirmLeadFollowUpMutationVariables,
  ConfirmLeadFollowUpMutation,
  ConfirmLeadFollowUpMutationFn,
  ConfirmLeadFollowUpMutationHookResult,
  ConfirmLeadFollowUpMutationResult,
  ConfirmLeadFollowUpMutationOptions,
  ConfirmBookingApplicationMutationVariables,
  ConfirmBookingApplicationMutation,
  ConfirmBookingApplicationMutationFn,
  ConfirmBookingApplicationMutationHookResult,
  ConfirmBookingApplicationMutationResult,
  ConfirmBookingApplicationMutationOptions,
  ConcludeAgreementApplicationMutationVariables,
  ConcludeAgreementApplicationMutation,
  ConcludeAgreementApplicationMutationFn,
  ConcludeAgreementApplicationMutationHookResult,
  ConcludeAgreementApplicationMutationResult,
  ConcludeAgreementApplicationMutationOptions,
  CompleteWebPublicKeyCredentialRegistrationMutationVariables,
  CompleteWebPublicKeyCredentialRegistrationMutation,
  CompleteWebPublicKeyCredentialRegistrationMutationFn,
  CompleteWebPublicKeyCredentialRegistrationMutationHookResult,
  CompleteWebPublicKeyCredentialRegistrationMutationResult,
  CompleteWebPublicKeyCredentialRegistrationMutationOptions,
  CompleteLeadMutationVariables,
  CompleteLeadMutation,
  CompleteLeadMutationFn,
  CompleteLeadMutationHookResult,
  CompleteLeadMutationResult,
  CompleteLeadMutationOptions,
  CompleteApplicationMutationVariables,
  CompleteApplicationMutation,
  CompleteApplicationMutationFn,
  CompleteApplicationMutationHookResult,
  CompleteApplicationMutationResult,
  CompleteApplicationMutationOptions,
  CheckInApplicationMutationVariables,
  CheckInApplicationMutation,
  CheckInApplicationMutationFn,
  CheckInApplicationMutationHookResult,
  CheckInApplicationMutationResult,
  CheckInApplicationMutationOptions,
  ChangePasswordFromTokenMutationVariables,
  ChangePasswordFromTokenMutation,
  ChangePasswordFromTokenMutationFn,
  ChangePasswordFromTokenMutationHookResult,
  ChangePasswordFromTokenMutationResult,
  ChangePasswordFromTokenMutationOptions,
  ChangePasswordFromAuthenticationMutationVariables,
  ChangePasswordFromAuthenticationMutation,
  ChangePasswordFromAuthenticationMutationFn,
  ChangePasswordFromAuthenticationMutationHookResult,
  ChangePasswordFromAuthenticationMutationResult,
  ChangePasswordFromAuthenticationMutationOptions,
  ChangePasswordMutationVariables,
  ChangePasswordMutation,
  ChangePasswordMutationFn,
  ChangePasswordMutationHookResult,
  ChangePasswordMutationResult,
  ChangePasswordMutationOptions,
  CancelLeadFollowUpMutationVariables,
  CancelLeadFollowUpMutation,
  CancelLeadFollowUpMutationFn,
  CancelLeadFollowUpMutationHookResult,
  CancelLeadFollowUpMutationResult,
  CancelLeadFollowUpMutationOptions,
  CancelApplicationMutationVariables,
  CancelApplicationMutation,
  CancelApplicationMutationFn,
  CancelApplicationMutationHookResult,
  CancelApplicationMutationResult,
  CancelApplicationMutationOptions,
  CalculateMutationVariables,
  CalculateMutation,
  CalculateMutationFn,
  CalculateMutationHookResult,
  CalculateMutationResult,
  CalculateMutationOptions,
  AuthenticateWithWebAuthnMutationVariables,
  AuthenticateWithWebAuthnMutation,
  AuthenticateWithWebAuthnMutationFn,
  AuthenticateWithWebAuthnMutationHookResult,
  AuthenticateWithWebAuthnMutationResult,
  AuthenticateWithWebAuthnMutationOptions,
  AuthenticateWithTotpMutationVariables,
  AuthenticateWithTotpMutation,
  AuthenticateWithTotpMutationFn,
  AuthenticateWithTotpMutationHookResult,
  AuthenticateWithTotpMutationResult,
  AuthenticateWithTotpMutationOptions,
  AuthenticateWithSmsOtpMutationVariables,
  AuthenticateWithSmsOtpMutation,
  AuthenticateWithSmsOtpMutationFn,
  AuthenticateWithSmsOtpMutationHookResult,
  AuthenticateWithSmsOtpMutationResult,
  AuthenticateWithSmsOtpMutationOptions,
  AuthenticateMutationVariables,
  AuthenticateMutation,
  AuthenticateMutationFn,
  AuthenticateMutationHookResult,
  AuthenticateMutationResult,
  AuthenticateMutationOptions,
  AttachUsersToUserGroupMutationVariables,
  AttachUsersToUserGroupMutation,
  AttachUsersToUserGroupMutationFn,
  AttachUsersToUserGroupMutationHookResult,
  AttachUsersToUserGroupMutationResult,
  AttachUsersToUserGroupMutationOptions,
  AttachUserOnRoleMutationVariables,
  AttachUserOnRoleMutation,
  AttachUserOnRoleMutationFn,
  AttachUserOnRoleMutationHookResult,
  AttachUserOnRoleMutationResult,
  AttachUserOnRoleMutationOptions,
  AttachSuperiorGroupsToUserGroupMutationVariables,
  AttachSuperiorGroupsToUserGroupMutation,
  AttachSuperiorGroupsToUserGroupMutationFn,
  AttachSuperiorGroupsToUserGroupMutationHookResult,
  AttachSuperiorGroupsToUserGroupMutationResult,
  AttachSuperiorGroupsToUserGroupMutationOptions,
  AttachPermissionOnRoleMutationVariables,
  AttachPermissionOnRoleMutation,
  AttachPermissionOnRoleMutationFn,
  AttachPermissionOnRoleMutationHookResult,
  AttachPermissionOnRoleMutationResult,
  AttachPermissionOnRoleMutationOptions,
  AttachDealersToUserGroupMutationVariables,
  AttachDealersToUserGroupMutation,
  AttachDealersToUserGroupMutationFn,
  AttachDealersToUserGroupMutationHookResult,
  AttachDealersToUserGroupMutationResult,
  AttachDealersToUserGroupMutationOptions,
  ApproveApplicationMutationVariables,
  ApproveApplicationMutation,
  ApproveApplicationMutationFn,
  ApproveApplicationMutationHookResult,
  ApproveApplicationMutationResult,
  ApproveApplicationMutationOptions,
  ApplyPorscheV3LayoutOnRouterMutationVariables,
  ApplyPorscheV3LayoutOnRouterMutation,
  ApplyPorscheV3LayoutOnRouterMutationFn,
  ApplyPorscheV3LayoutOnRouterMutationHookResult,
  ApplyPorscheV3LayoutOnRouterMutationResult,
  ApplyPorscheV3LayoutOnRouterMutationOptions,
  ApplyNewStandardApplicationMutationVariables,
  ApplyNewStandardApplicationMutation,
  ApplyNewStandardApplicationMutationFn,
  ApplyNewStandardApplicationMutationHookResult,
  ApplyNewStandardApplicationMutationResult,
  ApplyNewStandardApplicationMutationOptions,
  ApplyNewFinderApplicationMutationVariables,
  ApplyNewFinderApplicationMutation,
  ApplyNewFinderApplicationMutationFn,
  ApplyNewFinderApplicationMutationHookResult,
  ApplyNewFinderApplicationMutationResult,
  ApplyNewFinderApplicationMutationOptions,
  ApplyNewConfiguratorApplicationMutationVariables,
  ApplyNewConfiguratorApplicationMutation,
  ApplyNewConfiguratorApplicationMutationFn,
  ApplyNewConfiguratorApplicationMutationHookResult,
  ApplyNewConfiguratorApplicationMutationResult,
  ApplyNewConfiguratorApplicationMutationOptions,
  ApplyNewMutationVariables,
  ApplyNewMutation,
  ApplyNewMutationFn,
  ApplyNewMutationHookResult,
  ApplyNewMutationResult,
  ApplyNewMutationOptions,
  ApplyGiftVoucherMutationVariables,
  ApplyGiftVoucherMutation,
  ApplyGiftVoucherMutationFn,
  ApplyGiftVoucherMutationHookResult,
  ApplyGiftVoucherMutationResult,
  ApplyGiftVoucherMutationOptions,
  ApplyForPasswordChangeMutationVariables,
  ApplyForPasswordChangeMutation,
  ApplyForPasswordChangeMutationFn,
  ApplyForPasswordChangeMutationHookResult,
  ApplyForPasswordChangeMutationResult,
  ApplyForPasswordChangeMutationOptions,
  ApplyEmptyLayoutOnRouterMutationVariables,
  ApplyEmptyLayoutOnRouterMutation,
  ApplyEmptyLayoutOnRouterMutationFn,
  ApplyEmptyLayoutOnRouterMutationHookResult,
  ApplyEmptyLayoutOnRouterMutationResult,
  ApplyEmptyLayoutOnRouterMutationOptions,
  ApplyBasicProLayoutOnRouterMutationVariables,
  ApplyBasicProLayoutOnRouterMutation,
  ApplyBasicProLayoutOnRouterMutationFn,
  ApplyBasicProLayoutOnRouterMutationHookResult,
  ApplyBasicProLayoutOnRouterMutationResult,
  ApplyBasicProLayoutOnRouterMutationOptions,
  ApplyBasicLayoutOnRouterMutationVariables,
  ApplyBasicLayoutOnRouterMutation,
  ApplyBasicLayoutOnRouterMutationFn,
  ApplyBasicLayoutOnRouterMutationHookResult,
  ApplyBasicLayoutOnRouterMutationResult,
  ApplyBasicLayoutOnRouterMutationOptions,
  AmendMobilityApplicationMutationVariables,
  AmendMobilityApplicationMutation,
  AmendMobilityApplicationMutationFn,
  AmendMobilityApplicationMutationHookResult,
  AmendMobilityApplicationMutationResult,
  AmendMobilityApplicationMutationOptions,
  AddTradeInAuditTrailCommentMutationVariables,
  AddTradeInAuditTrailCommentMutation,
  AddTradeInAuditTrailCommentMutationFn,
  AddTradeInAuditTrailCommentMutationHookResult,
  AddTradeInAuditTrailCommentMutationResult,
  AddTradeInAuditTrailCommentMutationOptions,
  AddStockInventoryMutationVariables,
  AddStockInventoryMutation,
  AddStockInventoryMutationFn,
  AddStockInventoryMutationHookResult,
  AddStockInventoryMutationResult,
  AddStockInventoryMutationOptions,
  AddInventoryAuditTrailCommentMutationVariables,
  AddInventoryAuditTrailCommentMutation,
  AddInventoryAuditTrailCommentMutationFn,
  AddInventoryAuditTrailCommentMutationHookResult,
  AddInventoryAuditTrailCommentMutationResult,
  AddInventoryAuditTrailCommentMutationOptions,
  AddCustomerAuditTrailCommentMutationVariables,
  AddCustomerAuditTrailCommentMutation,
  AddCustomerAuditTrailCommentMutationFn,
  AddCustomerAuditTrailCommentMutationHookResult,
  AddCustomerAuditTrailCommentMutationResult,
  AddCustomerAuditTrailCommentMutationOptions,
  AddAuditTrailCommentMutationVariables,
  AddAuditTrailCommentMutation,
  AddAuditTrailCommentMutationFn,
  AddAuditTrailCommentMutationHookResult,
  AddAuditTrailCommentMutationResult,
  AddAuditTrailCommentMutationOptions,
  AuthorizeWithOidcMutationVariables,
  AuthorizeWithOidcMutation,
  AuthorizeWithOidcMutationFn,
  AuthorizeWithOidcMutationHookResult,
  AuthorizeWithOidcMutationResult,
  AuthorizeWithOidcMutationOptions,
} from "./mutations/index";

export {
  VerifyMobileOtpDocument,
  useVerifyMobileOtpMutation,
  ValidateSmsOtpForUpdateEmailDocument,
  useValidateSmsOtpForUpdateEmailMutation,
  ValidateSmsOtpForResetPasswordDocument,
  useValidateSmsOtpForResetPasswordMutation,
  ValidateSalesOfferRemoteJourneyPasscodeDocument,
  useValidateSalesOfferRemoteJourneyPasscodeMutation,
  ValidateRemoteJourneyPasscodeDocument,
  useValidateRemoteJourneyPasscodeMutation,
  UpsertKycPresetInEventDocument,
  useUpsertKycPresetInEventMutation,
  UpsertEventUserIdsDocument,
  useUpsertEventUserIdsMutation,
  UploadWebsiteSocialMediaAssetDocument,
  useUploadWebsiteSocialMediaAssetMutation,
  UploadWebPageImageDocument,
  useUploadWebPageImageMutation,
  UploadVisitAppointmentModuleAssetDocument,
  useUploadVisitAppointmentModuleAssetMutation,
  UploadVehicleSalesOfferSpecificationDocumentDocument,
  useUploadVehicleSalesOfferSpecificationDocumentMutation,
  UploadVariantConfiguratorTrimSettingAssetDocument,
  useUploadVariantConfiguratorTrimSettingAssetMutation,
  UploadVariantConfiguratorPackageSectionImageAssetDocument,
  useUploadVariantConfiguratorPackageSectionImageAssetMutation,
  UploadVariantConfiguratorPackageAdditionalDetailsAssetDocument,
  useUploadVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  UploadVariantConfiguratorOptionSettingAssetDocument,
  useUploadVariantConfiguratorOptionSettingAssetMutation,
  UploadVariantConfiguratorOptionAssetDocument,
  useUploadVariantConfiguratorOptionAssetMutation,
  UploadVariantConfiguratorMatrixAssetDocument,
  useUploadVariantConfiguratorMatrixAssetMutation,
  UploadVariantConfiguratorColorSettingAssetDocument,
  useUploadVariantConfiguratorColorSettingAssetMutation,
  UploadVariantAssetDocument,
  useUploadVariantAssetMutation,
  UploadUserAssetDocument,
  useUploadUserAssetMutation,
  UploadStockAssetDocument,
  useUploadStockAssetMutation,
  UploadStandardApplicationModuleAssetDocument,
  useUploadStandardApplicationModuleAssetMutation,
  UploadSalesOfferDocumentDocument,
  useUploadSalesOfferDocumentMutation,
  UploadModelConfiguratorAssetDocument,
  useUploadModelConfiguratorAssetMutation,
  UploadMobilityModuleEmailAssetDocument,
  useUploadMobilityModuleEmailAssetMutation,
  UploadMaintenanceModuleAssetDocument,
  useUploadMaintenanceModuleAssetMutation,
  UploadLeadDocumentsDocument,
  useUploadLeadDocumentsMutation,
  UploadLeadDocumentDocument,
  useUploadLeadDocumentMutation,
  UploadGiftVoucherModuleAssetDocument,
  useUploadGiftVoucherModuleAssetMutation,
  UploadGiftVoucherDocumentDocument,
  useUploadGiftVoucherDocumentMutation,
  UploadFinderApplicationModuleAssetDocument,
  useUploadFinderApplicationModuleAssetMutation,
  UploadEventLevelAssetDocument,
  useUploadEventLevelAssetMutation,
  UploadEventApplicationModuleAssetDocument,
  useUploadEventApplicationModuleAssetMutation,
  UploadEdmSocialMediaAssetDocument,
  useUploadEdmSocialMediaAssetMutation,
  UploadDealerSocialMediaAssetDocument,
  useUploadDealerSocialMediaAssetMutation,
  UploadConfiguratorModuleAssetDocument,
  useUploadConfiguratorModuleAssetMutation,
  UploadConfiguratorDescriptionImageDocument,
  useUploadConfiguratorDescriptionImageMutation,
  UploadCompanyAssetDocument,
  useUploadCompanyAssetMutation,
  UploadBannerImageDocument,
  useUploadBannerImageMutation,
  UploadBankAssetDocument,
  useUploadBankAssetMutation,
  UploadAppointmentModuleAssetDocument,
  useUploadAppointmentModuleAssetMutation,
  UploadApplicationDocumentsDocument,
  useUploadApplicationDocumentsMutation,
  UploadApplicationDocumentDocument,
  useUploadApplicationDocumentMutation,
  UpdateWhatsappLiveChatSettingsDocument,
  useUpdateWhatsappLiveChatSettingsMutation,
  UpdateWhatsappLiveChatModuleDocument,
  useUpdateWhatsappLiveChatModuleMutation,
  UpdateWebsiteSocialMediaDocument,
  useUpdateWebsiteSocialMediaMutation,
  UpdateWebsiteModuleDocument,
  useUpdateWebsiteModuleMutation,
  UpdateWebPageEndpointDocument,
  useUpdateWebPageEndpointMutation,
  UpdateWebPageDocument,
  useUpdateWebPageMutation,
  UpdateWebCalcSettingDocument,
  useUpdateWebCalcSettingMutation,
  UpdateVisitAppointmentModuleEmailContentDocument,
  useUpdateVisitAppointmentModuleEmailContentMutation,
  UpdateVisitAppointmentModuleDocument,
  useUpdateVisitAppointmentModuleMutation,
  UpdateVehicleSalesOfferDocument,
  useUpdateVehicleSalesOfferMutation,
  UpdateVehicleDataWithPorscheCodeIntegrationModuleDocument,
  useUpdateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  UpdateVariantLabelsDocument,
  useUpdateVariantLabelsMutation,
  UpdateVariantImageOrderingDocument,
  useUpdateVariantImageOrderingMutation,
  UpdateVariantConfiguratorDocument,
  useUpdateVariantConfiguratorMutation,
  UpdateVariantDocument,
  useUpdateVariantMutation,
  UpdateUserlikeChatbotSettingsDocument,
  useUpdateUserlikeChatbotSettingsMutation,
  UpdateUserlikeChatbotModuleDocument,
  useUpdateUserlikeChatbotModuleMutation,
  UpdateUserGroupDocument,
  useUpdateUserGroupMutation,
  UpdateUserDocument,
  useUpdateUserMutation,
  UpdateUobBankIntegrationSettingDocument,
  useUpdateUobBankIntegrationSettingMutation,
  UpdateTtbPaymentSettingsDocument,
  useUpdateTtbPaymentSettingsMutation,
  UpdateTtbPaymentModuleDocument,
  useUpdateTtbPaymentModuleMutation,
  UpdateTrimBlockDocument,
  useUpdateTrimBlockMutation,
  UpdateTradeInSalesOfferDocument,
  useUpdateTradeInSalesOfferMutation,
  UpdateTradeInModuleDocument,
  useUpdateTradeInModuleMutation,
  UpdateTextConsentsAndDeclarationsDocument,
  useUpdateTextConsentsAndDeclarationsMutation,
  UpdateTestDriveDataDocument,
  useUpdateTestDriveDataMutation,
  UpdateStockInventoryDocument,
  useUpdateStockInventoryMutation,
  UpdateStandardApplicationVehicleAssignmentsDocument,
  useUpdateStandardApplicationVehicleAssignmentsMutation,
  UpdateStandardApplicationPublicAccessEntrypointDocument,
  useUpdateStandardApplicationPublicAccessEntrypointMutation,
  UpdateStandardApplicationModuleMainDetailsDocument,
  useUpdateStandardApplicationModuleMainDetailsMutation,
  UpdateStandardApplicationModuleEmailContentDocument,
  useUpdateStandardApplicationModuleEmailContentMutation,
  UpdateStandardApplicationJourneyDocument,
  useUpdateStandardApplicationJourneyMutation,
  UpdateStandardApplicationFinanceProductAssignmentsDocument,
  useUpdateStandardApplicationFinanceProductAssignmentsMutation,
  UpdateStandardApplicationEntrypointDocument,
  useUpdateStandardApplicationEntrypointMutation,
  UpdateStandardApplicationDraftDocument,
  useUpdateStandardApplicationDraftMutation,
  UpdateStandardApplicationConfigurationDocument,
  useUpdateStandardApplicationConfigurationMutation,
  UpdateSimpleVehicleManagementModuleDocument,
  useUpdateSimpleVehicleManagementModuleMutation,
  UpdateSalesOfferModuleEmailContentDocument,
  useUpdateSalesOfferModuleEmailContentMutation,
  UpdateSalesOfferModuleDocument,
  useUpdateSalesOfferModuleMutation,
  UpdateSalesControlBoardModuleByDealerDocument,
  useUpdateSalesControlBoardModuleByDealerMutation,
  UpdateSalesControlBoardModuleDocument,
  useUpdateSalesControlBoardModuleMutation,
  UpdateRouterPathScriptsDocument,
  useUpdateRouterPathScriptsMutation,
  UpdateRouterMenuDocument,
  useUpdateRouterMenuMutation,
  UpdateRouterDocument,
  useUpdateRouterMutation,
  UpdateRoleDocument,
  useUpdateRoleMutation,
  UpdatePromoCodeModuleDocument,
  useUpdatePromoCodeModuleMutation,
  UpdatePromoCodeDocument,
  useUpdatePromoCodeMutation,
  UpdatePorscheRetainModuleDocument,
  useUpdatePorscheRetainModuleMutation,
  UpdatePorschePaymentSettingsDocument,
  useUpdatePorschePaymentSettingsMutation,
  UpdatePorschePaymentModuleDocument,
  useUpdatePorschePaymentModuleMutation,
  UpdatePorscheMasterDataModuleDocument,
  useUpdatePorscheMasterDataModuleMutation,
  UpdatePorscheIdModuleDocument,
  useUpdatePorscheIdModuleMutation,
  UpdatePayGatePaymentSettingsDocument,
  useUpdatePayGatePaymentSettingsMutation,
  UpdatePayGatePaymentModuleDocument,
  useUpdatePayGatePaymentModuleMutation,
  UpdatePackageSettingDocument,
  useUpdatePackageSettingMutation,
  UpdatePackageBlockDocument,
  useUpdatePackageBlockMutation,
  UpdateOptionsBlockDocument,
  useUpdateOptionsBlockMutation,
  UpdateOidcModuleDocument,
  useUpdateOidcModuleMutation,
  UpdateOidcClientDocument,
  useUpdateOidcClientMutation,
  UpdateNamirialSigningModuleDocument,
  useUpdateNamirialSigningModuleMutation,
  UpdateNamirialSettingsDocument,
  useUpdateNamirialSettingsMutation,
  UpdateMyInfoSettingDocument,
  useUpdateMyInfoSettingMutation,
  UpdateMyInfoModuleDocument,
  useUpdateMyInfoModuleMutation,
  UpdateModelConfiguratorDocument,
  useUpdateModelConfiguratorMutation,
  UpdateModelDocument,
  useUpdateModelMutation,
  UpdateMobilityModuleDocument,
  useUpdateMobilityModuleMutation,
  UpdateMobilityEmailContentDocument,
  useUpdateMobilityEmailContentMutation,
  UpdateMobilityDepositAmountDocument,
  useUpdateMobilityDepositAmountMutation,
  UpdateMobilityApplicationEntrypointDocument,
  useUpdateMobilityApplicationEntrypointMutation,
  UpdateMobilityApplicationDraftDocument,
  useUpdateMobilityApplicationDraftMutation,
  UpdateMobilityApplicationDocument,
  useUpdateMobilityApplicationMutation,
  UpdateMobilityAddonDocument,
  useUpdateMobilityAddonMutation,
  UpdateMobilityAdditionalInfoDocument,
  useUpdateMobilityAdditionalInfoMutation,
  UpdateMaybankIntegrationSettingDocument,
  useUpdateMaybankIntegrationSettingMutation,
  UpdateMarketingModuleDocument,
  useUpdateMarketingModuleMutation,
  UpdateMarketingConsentsAndDeclarationsDocument,
  useUpdateMarketingConsentsAndDeclarationsMutation,
  UpdateMakeDocument,
  useUpdateMakeMutation,
  UpdateMaintenanceModuleDetailsDocument,
  useUpdateMaintenanceModuleDetailsMutation,
  UpdateMaintenanceModuleDocument,
  useUpdateMaintenanceModuleMutation,
  UpdateMainDetailsSalesOfferDocument,
  useUpdateMainDetailsSalesOfferMutation,
  UpdateLocalCustomerManagementKycFieldsDocument,
  useUpdateLocalCustomerManagementKycFieldsMutation,
  UpdateLocalCustomerManagementDocument,
  useUpdateLocalCustomerManagementMutation,
  UpdateLeadListEndpointDocument,
  useUpdateLeadListEndpointMutation,
  UpdateLeadFollowUpDocument,
  useUpdateLeadFollowUpMutation,
  UpdateLeadDocument,
  useUpdateLeadMutation,
  UpdateLaunchpadApplicationTradeInDocument,
  useUpdateLaunchpadApplicationTradeInMutation,
  UpdateLaunchPadModuleVehicleAssignmentsDocument,
  useUpdateLaunchPadModuleVehicleAssignmentsMutation,
  UpdateLaunchPadModuleDocument,
  useUpdateLaunchPadModuleMutation,
  UpdateLaunchPadApplicationEntrypointDocument,
  useUpdateLaunchPadApplicationEntrypointMutation,
  UpdateLanguagePackDocument,
  useUpdateLanguagePackMutation,
  UpdateLabelsModuleDocument,
  useUpdateLabelsModuleMutation,
  UpdateLabelsDocument,
  useUpdateLabelsMutation,
  UpdateKycPresetDocument,
  useUpdateKycPresetMutation,
  UpdateInventoryDocument,
  useUpdateInventoryMutation,
  UpdateInsurerDocument,
  useUpdateInsurerMutation,
  UpdateInsuranceSalesOfferDocument,
  useUpdateInsuranceSalesOfferMutation,
  UpdateInsuranceProductDocument,
  useUpdateInsuranceProductMutation,
  UpdateInsuranceModuleDocument,
  useUpdateInsuranceModuleMutation,
  UpdateHlfBankV2IntegrationSettingDocument,
  useUpdateHlfBankV2IntegrationSettingMutation,
  UpdateHlfBankIntegrationSettingDocument,
  useUpdateHlfBankIntegrationSettingMutation,
  UpdateGroupConsentsAndDeclarationsDocument,
  useUpdateGroupConsentsAndDeclarationsMutation,
  UpdateGiftVoucherModuleEmailContentDocument,
  useUpdateGiftVoucherModuleEmailContentMutation,
  UpdateGiftVoucherModuleDocument,
  useUpdateGiftVoucherModuleMutation,
  UpdateGiftVoucherDocument,
  useUpdateGiftVoucherMutation,
  UpdateFiservPaymentSettingsDocument,
  useUpdateFiservPaymentSettingsMutation,
  UpdateFiservPaymentModuleDocument,
  useUpdateFiservPaymentModuleMutation,
  UpdateFinderVehicleManagementModuleDocument,
  useUpdateFinderVehicleManagementModuleMutation,
  UpdateFinderVehicleDocument,
  useUpdateFinderVehicleMutation,
  UpdateFinderApplicationPublicModuleEmailContentsDocument,
  useUpdateFinderApplicationPublicModuleEmailContentsMutation,
  UpdateFinderApplicationPublicAccessEntrypointDocument,
  useUpdateFinderApplicationPublicAccessEntrypointMutation,
  UpdateFinderApplicationPrivateModuleEmailContentsDocument,
  useUpdateFinderApplicationPrivateModuleEmailContentsMutation,
  UpdateFinderApplicationPrivateModuleDocument,
  useUpdateFinderApplicationPrivateModuleMutation,
  UpdateFinderApplicationPublicModuleDocument,
  useUpdateFinderApplicationPublicModuleMutation,
  UpdateFinderApplicationJourneyDocument,
  useUpdateFinderApplicationJourneyMutation,
  UpdateFinderApplicationEntrypointDocument,
  useUpdateFinderApplicationEntrypointMutation,
  UpdateFinderApplicationDraftDocument,
  useUpdateFinderApplicationDraftMutation,
  UpdateFinderApplicationDocument,
  useUpdateFinderApplicationMutation,
  UpdateFinanceSalesOfferDocument,
  useUpdateFinanceSalesOfferMutation,
  UpdateFinanceProductDocument,
  useUpdateFinanceProductMutation,
  UpdateEventApplicationVehicleAssignmentsDocument,
  useUpdateEventApplicationVehicleAssignmentsMutation,
  UpdateEventApplicationModuleMainDetailsDocument,
  useUpdateEventApplicationModuleMainDetailsMutation,
  UpdateEventApplicationModuleEmailContentsDocument,
  useUpdateEventApplicationModuleEmailContentsMutation,
  UpdateEventApplicationFinanceProductAssignmentsDocument,
  useUpdateEventApplicationFinanceProductAssignmentsMutation,
  UpdateEventApplicationEntrypointDocument,
  useUpdateEventApplicationEntrypointMutation,
  UpdateEventApplicationDocument,
  useUpdateEventApplicationMutation,
  UpdateEventDocument,
  useUpdateEventMutation,
  UpdateEnbdBankIntegrationSettingDocument,
  useUpdateEnbdBankIntegrationSettingMutation,
  UpdateEmailInsurerIntegrationSettingDocument,
  useUpdateEmailInsurerIntegrationSettingMutation,
  UpdateEmailFromTokenDocument,
  useUpdateEmailFromTokenMutation,
  UpdateEmailBankIntegrationSettingDocument,
  useUpdateEmailBankIntegrationSettingMutation,
  UpdateEdmEmailSocialMediaDocument,
  useUpdateEdmEmailSocialMediaMutation,
  UpdateEazyInsurerIntegrationSettingDocument,
  useUpdateEazyInsurerIntegrationSettingMutation,
  UpdateDummyWelcomePageEndpointDocument,
  useUpdateDummyWelcomePageEndpointMutation,
  UpdateDummyPrivatePageEndpointDocument,
  useUpdateDummyPrivatePageEndpointMutation,
  UpdateDocusignSettingDocument,
  useUpdateDocusignSettingMutation,
  UpdateDocusignModuleDocument,
  useUpdateDocusignModuleMutation,
  UpdateDepositSalesOfferDocument,
  useUpdateDepositSalesOfferMutation,
  UpdateDealerSocialMediaDocument,
  useUpdateDealerSocialMediaMutation,
  UpdateDealerDocument,
  useUpdateDealerMutation,
  UpdateDbsBankIntegrationSettingDocument,
  useUpdateDbsBankIntegrationSettingMutation,
  UpdateCustomerListEndpointDocument,
  useUpdateCustomerListEndpointMutation,
  UpdateCustomerDocument,
  useUpdateCustomerMutation,
  UpdateCtsModuleSettingDocument,
  useUpdateCtsModuleSettingMutation,
  UpdateCtsModuleDocument,
  useUpdateCtsModuleMutation,
  UpdateCtsInsuranceProductsDocument,
  useUpdateCtsInsuranceProductsMutation,
  UpdateCtsFinanceProductsDocument,
  useUpdateCtsFinanceProductsMutation,
  UpdateConsentsAndDeclarationsModuleDocument,
  useUpdateConsentsAndDeclarationsModuleMutation,
  UpdateConsentOrderListDocument,
  useUpdateConsentOrderListMutation,
  UpdateConfiguratorModuleEmailContentsDocument,
  useUpdateConfiguratorModuleEmailContentsMutation,
  UpdateConfiguratorModuleDocument,
  useUpdateConfiguratorModuleMutation,
  UpdateConfiguratorApplicationJourneyDocument,
  useUpdateConfiguratorApplicationJourneyMutation,
  UpdateConfiguratorApplicationEntrypointDocument,
  useUpdateConfiguratorApplicationEntrypointMutation,
  UpdateConfiguratorApplicationConfigurationDocument,
  useUpdateConfiguratorApplicationConfigurationMutation,
  UpdateConfiguratorApplicationDocument,
  useUpdateConfiguratorApplicationMutation,
  UpdateCompanyDocument,
  useUpdateCompanyMutation,
  UpdateColorBlockDocument,
  useUpdateColorBlockMutation,
  UpdateCheckboxConsentsAndDeclarationsDocument,
  useUpdateCheckboxConsentsAndDeclarationsMutation,
  UpdateCapModuleDocument,
  useUpdateCapModuleMutation,
  UpdateCampaignDocument,
  useUpdateCampaignMutation,
  UpdateBasicSigningModuleDocument,
  useUpdateBasicSigningModuleMutation,
  UpdateBannerDocument,
  useUpdateBannerMutation,
  UpdateBankModuleDocument,
  useUpdateBankModuleMutation,
  UpdateBankAvailableFinanceProductTypesDocument,
  useUpdateBankAvailableFinanceProductTypesMutation,
  UpdateBankDocument,
  useUpdateBankMutation,
  UpdateAutoplayModuleSettingDocument,
  useUpdateAutoplayModuleSettingMutation,
  UpdateAutoplayModuleDocument,
  useUpdateAutoplayModuleMutation,
  UpdateAssigneeOnLeadDocument,
  useUpdateAssigneeOnLeadMutation,
  UpdateAssigneeOnApplicationDocument,
  useUpdateAssigneeOnApplicationMutation,
  UpdateAppointmentModuleEmailContentDocument,
  useUpdateAppointmentModuleEmailContentMutation,
  UpdateAppointmentModuleDocument,
  useUpdateAppointmentModuleMutation,
  UpdateAppointmentDataDocument,
  useUpdateAppointmentDataMutation,
  UpdateApplicationListEndpointDocument,
  useUpdateApplicationListEndpointMutation,
  UpdateApplicationFieldsDocument,
  useUpdateApplicationFieldsMutation,
  UpdateApplicationDealershipAssignmentsByDealerDocument,
  useUpdateApplicationDealershipAssignmentsByDealerMutation,
  UpdateApplicationDocument,
  useUpdateApplicationMutation,
  UpdateApplicantVisitAppointmentDocument,
  useUpdateApplicantVisitAppointmentMutation,
  UpdateApplicantAppointmentDocument,
  useUpdateApplicantAppointmentMutation,
  UpdateAdyenPaymentSettingsDocument,
  useUpdateAdyenPaymentSettingsMutation,
  UpdateAdyenPaymentModuleDocument,
  useUpdateAdyenPaymentModuleMutation,
  UpdateAdditionalDetailDocument,
  useUpdateAdditionalDetailMutation,
  UpdateAccountDocument,
  useUpdateAccountMutation,
  UnqualifyLeadDocument,
  useUnqualifyLeadMutation,
  TakeOutStockInventoryDocument,
  useTakeOutStockInventoryMutation,
  SynchronizePorscheMasterDataDocument,
  useSynchronizePorscheMasterDataMutation,
  SynchronizeFinderVehicleDocument,
  useSynchronizeFinderVehicleMutation,
  SubmitTtbPaymentDocument,
  useSubmitTtbPaymentMutation,
  SubmitTestDriveKycDocument,
  useSubmitTestDriveKycMutation,
  SubmitTestDriveAgreementsDocument,
  useSubmitTestDriveAgreementsMutation,
  SubmitSigningOtpDocument,
  useSubmitSigningOtpMutation,
  SubmitSalesOfferPorschePaymentDocument,
  useSubmitSalesOfferPorschePaymentMutation,
  SubmitPorschePaymentDocument,
  useSubmitPorschePaymentMutation,
  SubmitPayGatePaymentDocument,
  useSubmitPayGatePaymentMutation,
  SubmitIntentAndAssignDocument,
  useSubmitIntentAndAssignMutation,
  SubmitGuarantorKycDocument,
  useSubmitGuarantorKycMutation,
  SubmitGuarantorAgreementsDocument,
  useSubmitGuarantorAgreementsMutation,
  SubmitGiftVoucherTtbPaymentDocument,
  useSubmitGiftVoucherTtbPaymentMutation,
  SubmitGiftVoucherPorschePaymentDocument,
  useSubmitGiftVoucherPorschePaymentMutation,
  SubmitGiftVoucherPayGatePaymentDocument,
  useSubmitGiftVoucherPayGatePaymentMutation,
  SubmitGiftVoucherFiservPaymentDocument,
  useSubmitGiftVoucherFiservPaymentMutation,
  SubmitGiftVoucherApplicantKycDocument,
  useSubmitGiftVoucherApplicantKycMutation,
  SubmitGiftVoucherApplicantAgreementsDocument,
  useSubmitGiftVoucherApplicantAgreementsMutation,
  SubmitGiftVoucherAdyenPaymentDocument,
  useSubmitGiftVoucherAdyenPaymentMutation,
  SubmitFiservPaymentDocument,
  useSubmitFiservPaymentMutation,
  SubmitChangesDocument,
  useSubmitChangesMutation,
  SubmitApplicationQuotationDocument,
  useSubmitApplicationQuotationMutation,
  SubmitApplicantVisitAppointmentDocument,
  useSubmitApplicantVisitAppointmentMutation,
  SubmitApplicantKycDocument,
  useSubmitApplicantKycMutation,
  SubmitApplicantAppointmentDocument,
  useSubmitApplicantAppointmentMutation,
  SubmitApplicantAgreementsDocument,
  useSubmitApplicantAgreementsMutation,
  SubmitAdyenPaymentDocument,
  useSubmitAdyenPaymentMutation,
  StartTestDriveDocument,
  useStartTestDriveMutation,
  ShareStandardApplicationDocument,
  useShareStandardApplicationMutation,
  ShareSalesOfferDocumentDocument,
  useShareSalesOfferDocumentMutation,
  SendSalesOfferDocument,
  useSendSalesOfferMutation,
  SendMobileVerificationOtpDocument,
  useSendMobileVerificationOtpMutation,
  RevokeWebPublicKeyCredentialDocument,
  useRevokeWebPublicKeyCredentialMutation,
  RevokeSessionDocument,
  useRevokeSessionMutation,
  ResubmitLeadToCapDocument,
  useResubmitLeadToCapMutation,
  ResetFinderVehicleDocument,
  useResetFinderVehicleMutation,
  ResendSmsOtpForUpdateEmailDocument,
  useResendSmsOtpForUpdateEmailMutation,
  ResendSmsOtpForResetPasswordDocument,
  useResendSmsOtpForResetPasswordMutation,
  ResendActivationLinkDocument,
  useResendActivationLinkMutation,
  RequestWebPublicKeyCredentialRegistrationDocument,
  useRequestWebPublicKeyCredentialRegistrationMutation,
  RequestReleaseLetterDocument,
  useRequestReleaseLetterMutation,
  RequestDisbursementDocument,
  useRequestDisbursementMutation,
  RemoveUserOnUserGroupDocument,
  useRemoveUserOnUserGroupMutation,
  RemoveUserOnRoleDocument,
  useRemoveUserOnRoleMutation,
  RemovePermissionOnRoleDocument,
  useRemovePermissionOnRoleMutation,
  RemoveDealerOnUserGroupDocument,
  useRemoveDealerOnUserGroupMutation,
  ReleaseReservedMobilityStockDocument,
  useReleaseReservedMobilityStockMutation,
  ReleaseReservedConfiguratorStockDocument,
  useReleaseReservedConfiguratorStockMutation,
  ReleaseFinderVehicleExpiryDocument,
  useReleaseFinderVehicleExpiryMutation,
  RefreshCredentialsDocument,
  useRefreshCredentialsMutation,
  RefreshApplicationStatusDocument,
  useRefreshApplicationStatusMutation,
  QualifyLeadWithCapValuesDocument,
  useQualifyLeadWithCapValuesMutation,
  QualifyLeadDocument,
  useQualifyLeadMutation,
  ProceedWithCustomerDeviceDocument,
  useProceedWithCustomerDeviceMutation,
  MarkLeadAsLostDocument,
  useMarkLeadAsLostMutation,
  MarkAsContactedDocument,
  useMarkAsContactedMutation,
  InitialSalesOfferSigningDocument,
  useInitialSalesOfferSigningMutation,
  InitialSalesOfferPaymentDocument,
  useInitialSalesOfferPaymentMutation,
  ImportVariantImagesDocument,
  useImportVariantImagesMutation,
  ImportVariantDocument,
  useImportVariantMutation,
  ImportSubModelDocument,
  useImportSubModelMutation,
  ImportSalesControlBoardDataDocument,
  useImportSalesControlBoardDataMutation,
  ImportModelDocument,
  useImportModelMutation,
  ImportMakeDocument,
  useImportMakeMutation,
  ImportLanguagePackDocument,
  useImportLanguagePackMutation,
  ImportInventoriesDocument,
  useImportInventoriesMutation,
  ImportFinderVehiclesLtaDocument,
  useImportFinderVehiclesLtaMutation,
  ImportDealersDocument,
  useImportDealersMutation,
  ImportConfiguratorVariantPricesDocument,
  useImportConfiguratorVariantPricesMutation,
  ImportCompanyConfigurationDocument,
  useImportCompanyConfigurationMutation,
  GetAgreementsAndKycFieldsFromUpdatedConfigurationDocument,
  useGetAgreementsAndKycFieldsFromUpdatedConfigurationMutation,
  GenerateSigningOtpDocument,
  useGenerateSigningOtpMutation,
  GenerateSalesOfferJourneyTokenDocument,
  useGenerateSalesOfferJourneyTokenMutation,
  GenerateRemoteJourneyPasscodeDocument,
  useGenerateRemoteJourneyPasscodeMutation,
  GenerateCancelMobilityApplicationAccessDocument,
  useGenerateCancelMobilityApplicationAccessMutation,
  GenerateAmendMobilityApplicationAccessDocument,
  useGenerateAmendMobilityApplicationAccessMutation,
  ExtendMobilityStockExpiryDocument,
  useExtendMobilityStockExpiryMutation,
  ExtendGiftVoucherJourneyExpiryDocument,
  useExtendGiftVoucherJourneyExpiryMutation,
  ExtendFinderVehicleExpiryDocument,
  useExtendFinderVehicleExpiryMutation,
  ExtendEventJourneyExpiryDocument,
  useExtendEventJourneyExpiryMutation,
  ExtendConfiguratorStockExpiryDocument,
  useExtendConfiguratorStockExpiryMutation,
  ExportCompanyConfigurationDocument,
  useExportCompanyConfigurationMutation,
  EndTestDriveDocument,
  useEndTestDriveMutation,
  EnableAuthenticatorDocument,
  useEnableAuthenticatorMutation,
  DraftStandardApplicationFromLeadDocument,
  useDraftStandardApplicationFromLeadMutation,
  DraftMobilityApplicationDocument,
  useDraftMobilityApplicationMutation,
  DraftLaunchpadApplicationDocument,
  useDraftLaunchpadApplicationMutation,
  DraftGiftVoucherDocument,
  useDraftGiftVoucherMutation,
  DraftFinderApplicationFromLeadDocument,
  useDraftFinderApplicationFromLeadMutation,
  DraftFinderApplicationDocument,
  useDraftFinderApplicationMutation,
  DraftEventApplicationDocument,
  useDraftEventApplicationMutation,
  DraftConfiguratorApplicationDocument,
  useDraftConfiguratorApplicationMutation,
  DraftApplicationDocument,
  useDraftApplicationMutation,
  DownloadSpecificationDocumentDocument,
  useDownloadSpecificationDocumentMutation,
  DownloadSalesOfferDocumentDocument,
  useDownloadSalesOfferDocumentMutation,
  DisableAuthenticatorDocument,
  useDisableAuthenticatorMutation,
  DeleteWhatsappLiveChatSettingsDocument,
  useDeleteWhatsappLiveChatSettingsMutation,
  DeleteWebsiteSocialMediaAssetDocument,
  useDeleteWebsiteSocialMediaAssetMutation,
  DeleteWebsiteSocialMediaDocument,
  useDeleteWebsiteSocialMediaMutation,
  DeleteWebPageImageDocument,
  useDeleteWebPageImageMutation,
  DeleteWebPageBlockDocument,
  useDeleteWebPageBlockMutation,
  DeleteWebPageDocument,
  useDeleteWebPageMutation,
  DeleteVisitAppointmentModuleAssetDocument,
  useDeleteVisitAppointmentModuleAssetMutation,
  DeleteVehicleDocument,
  useDeleteVehicleMutation,
  DeleteVariantConfiguratorTrimSettingAssetDocument,
  useDeleteVariantConfiguratorTrimSettingAssetMutation,
  DeleteVariantConfiguratorPackageSectionImageAssetDocument,
  useDeleteVariantConfiguratorPackageSectionImageAssetMutation,
  DeleteVariantConfiguratorPackageAdditionalDetailsAssetDocument,
  useDeleteVariantConfiguratorPackageAdditionalDetailsAssetMutation,
  DeleteVariantConfiguratorOptionSettingAssetDocument,
  useDeleteVariantConfiguratorOptionSettingAssetMutation,
  DeleteVariantConfiguratorOptionAssetDocument,
  useDeleteVariantConfiguratorOptionAssetMutation,
  DeleteVariantConfiguratorMatrixAssetDocument,
  useDeleteVariantConfiguratorMatrixAssetMutation,
  DeleteVariantConfiguratorColorSettingAssetDocument,
  useDeleteVariantConfiguratorColorSettingAssetMutation,
  DeleteVariantConfiguratorDocument,
  useDeleteVariantConfiguratorMutation,
  DeleteVariantAssetDocument,
  useDeleteVariantAssetMutation,
  DeleteUserlikeChatbotSettingsDocument,
  useDeleteUserlikeChatbotSettingsMutation,
  DeleteUserGroupDocument,
  useDeleteUserGroupMutation,
  DeleteUserAssetDocument,
  useDeleteUserAssetMutation,
  DeleteUserDocument,
  useDeleteUserMutation,
  DeleteTtbPaymentSettingsDocument,
  useDeleteTtbPaymentSettingsMutation,
  DeleteStockInventoryDocument,
  useDeleteStockInventoryMutation,
  DeleteStockAssetDocument,
  useDeleteStockAssetMutation,
  DeleteStandardApplicationModuleAssetDocument,
  useDeleteStandardApplicationModuleAssetMutation,
  DeleteSalesOfferDocumentDocument,
  useDeleteSalesOfferDocumentMutation,
  DeleteRouterDocument,
  useDeleteRouterMutation,
  DeleteRoleDocument,
  useDeleteRoleMutation,
  DeletePromoCodeDocument,
  useDeletePromoCodeMutation,
  DeletePorschePaymentSettingsDocument,
  useDeletePorschePaymentSettingsMutation,
  DeletePayGatePaymentSettingsDocument,
  useDeletePayGatePaymentSettingsMutation,
  DeletePackageSettingDocument,
  useDeletePackageSettingMutation,
  DeleteOptionsBlockDocument,
  useDeleteOptionsBlockMutation,
  DeleteMyInfoSettingDocument,
  useDeleteMyInfoSettingMutation,
  DeleteModuleDocument,
  useDeleteModuleMutation,
  DeleteModelConfiguratorAssetDocument,
  useDeleteModelConfiguratorAssetMutation,
  DeleteModelConfiguratorDocument,
  useDeleteModelConfiguratorMutation,
  DeleteMobilityModuleEmailAssetDocument,
  useDeleteMobilityModuleEmailAssetMutation,
  DeleteMobilityDocument,
  useDeleteMobilityMutation,
  DeleteMaintenanceModuleAssetDocument,
  useDeleteMaintenanceModuleAssetMutation,
  DeleteLeadDocumentDocument,
  useDeleteLeadDocumentMutation,
  DeleteLanguagePackDocument,
  useDeleteLanguagePackMutation,
  DeleteLabelsDocument,
  useDeleteLabelsMutation,
  DeleteKycPresetsDocument,
  useDeleteKycPresetsMutation,
  DeleteInventoriesDocument,
  useDeleteInventoriesMutation,
  DeleteInsurerDocument,
  useDeleteInsurerMutation,
  DeleteInsuranceProductDocument,
  useDeleteInsuranceProductMutation,
  DeleteGiftVoucherModuleAssetDocument,
  useDeleteGiftVoucherModuleAssetMutation,
  DeleteGiftVoucherDocumentDocument,
  useDeleteGiftVoucherDocumentMutation,
  DeleteFiservPaymentSettingsDocument,
  useDeleteFiservPaymentSettingsMutation,
  DeleteFinderApplicationModuleAssetDocument,
  useDeleteFinderApplicationModuleAssetMutation,
  DeleteFinanceProductDocument,
  useDeleteFinanceProductMutation,
  DeleteEventLevelAssetDocument,
  useDeleteEventLevelAssetMutation,
  DeleteEventApplicationModuleAssetDocument,
  useDeleteEventApplicationModuleAssetMutation,
  DeleteEventDocument,
  useDeleteEventMutation,
  DeleteEndpointDocument,
  useDeleteEndpointMutation,
  DeleteEdmSocialMediaAssetDocument,
  useDeleteEdmSocialMediaAssetMutation,
  DeleteEdmEmailSocialMediaDocument,
  useDeleteEdmEmailSocialMediaMutation,
  DeleteDocusignSettingDocument,
  useDeleteDocusignSettingMutation,
  DeleteDealerSocialMediaAssetDocument,
  useDeleteDealerSocialMediaAssetMutation,
  DeleteDealerSocialMediaDocument,
  useDeleteDealerSocialMediaMutation,
  DeleteDealerDocument,
  useDeleteDealerMutation,
  DeleteCtsModuleSettingDocument,
  useDeleteCtsModuleSettingMutation,
  DeleteConsentsAndDeclarationsDocument,
  useDeleteConsentsAndDeclarationsMutation,
  DeleteConfiguratorModuleAssetDocument,
  useDeleteConfiguratorModuleAssetMutation,
  DeleteConfiguratorDescriptionImageDocument,
  useDeleteConfiguratorDescriptionImageMutation,
  DeleteCompanyAssetDocument,
  useDeleteCompanyAssetMutation,
  DeleteCompanyDocument,
  useDeleteCompanyMutation,
  DeleteCampaignDocument,
  useDeleteCampaignMutation,
  DeleteBannerImageDocument,
  useDeleteBannerImageMutation,
  DeleteBannerDocument,
  useDeleteBannerMutation,
  DeleteBankAssetDocument,
  useDeleteBankAssetMutation,
  DeleteBankDocument,
  useDeleteBankMutation,
  DeleteAutoplayModuleSettingDocument,
  useDeleteAutoplayModuleSettingMutation,
  DeleteAppointmentModuleAssetDocument,
  useDeleteAppointmentModuleAssetMutation,
  DeleteApplicationDocumentDocument,
  useDeleteApplicationDocumentMutation,
  DeleteAdyenPaymentSettingsDocument,
  useDeleteAdyenPaymentSettingsMutation,
  DeleteAdditionalDetailDocument,
  useDeleteAdditionalDetailMutation,
  DeclineApplicationDocument,
  useDeclineApplicationMutation,
  CreateWhatsappLiveChatSettingsDocument,
  useCreateWhatsappLiveChatSettingsMutation,
  CreateWhatsappLiveChatModuleDocument,
  useCreateWhatsappLiveChatModuleMutation,
  CreateWebsiteSocialMediaDocument,
  useCreateWebsiteSocialMediaMutation,
  CreateWebsiteModuleDocument,
  useCreateWebsiteModuleMutation,
  CreateWebPageEndpointDocument,
  useCreateWebPageEndpointMutation,
  CreateWebPageDocument,
  useCreateWebPageMutation,
  CreateWebCalcSettingDocument,
  useCreateWebCalcSettingMutation,
  CreateVisitAppointmentModuleDocument,
  useCreateVisitAppointmentModuleMutation,
  CreateVehicleDataWithPorscheCodeIntegrationModuleDocument,
  useCreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
  CreateVariantConfiguratorDocument,
  useCreateVariantConfiguratorMutation,
  CreateVariantDocument,
  useCreateVariantMutation,
  CreateUserlikeChatbotSettingsDocument,
  useCreateUserlikeChatbotSettingsMutation,
  CreateUserlikeChatbotModuleDocument,
  useCreateUserlikeChatbotModuleMutation,
  CreateUserGroupDocument,
  useCreateUserGroupMutation,
  CreateUserDocument,
  useCreateUserMutation,
  CreateTtbPaymentSettingsDocument,
  useCreateTtbPaymentSettingsMutation,
  CreateTtbPaymentModuleDocument,
  useCreateTtbPaymentModuleMutation,
  CreateTrimBlockDocument,
  useCreateTrimBlockMutation,
  CreateTradeInModuleDocument,
  useCreateTradeInModuleMutation,
  CreateTextConsentsAndDeclarationsDocument,
  useCreateTextConsentsAndDeclarationsMutation,
  CreateSubmodelDocument,
  useCreateSubmodelMutation,
  CreateStandardApplicationPublicAccessEntrypointDocument,
  useCreateStandardApplicationPublicAccessEntrypointMutation,
  CreateStandardApplicationModuleDocument,
  useCreateStandardApplicationModuleMutation,
  CreateStandardApplicationEntrypointDocument,
  useCreateStandardApplicationEntrypointMutation,
  CreateSimpleVehicleManagementModuleDocument,
  useCreateSimpleVehicleManagementModuleMutation,
  CreateShowroomVisitAppointmentFromLeadDocument,
  useCreateShowroomVisitAppointmentFromLeadMutation,
  CreateSalesOfferModuleDocument,
  useCreateSalesOfferModuleMutation,
  CreateSalesOfferDocument,
  useCreateSalesOfferMutation,
  CreateSalesControlBoardModuleDocument,
  useCreateSalesControlBoardModuleMutation,
  CreateRouterMenuDocument,
  useCreateRouterMenuMutation,
  CreateRouterDocument,
  useCreateRouterMutation,
  CreateRoleDocument,
  useCreateRoleMutation,
  CreatePromoCodeModuleDocument,
  useCreatePromoCodeModuleMutation,
  CreatePromoCodeDocument,
  useCreatePromoCodeMutation,
  CreatePorscheRetainModuleDocument,
  useCreatePorscheRetainModuleMutation,
  CreatePorschePaymentSettingsDocument,
  useCreatePorschePaymentSettingsMutation,
  CreatePorschePaymentModuleDocument,
  useCreatePorschePaymentModuleMutation,
  CreatePorscheMasterDataModuleDocument,
  useCreatePorscheMasterDataModuleMutation,
  CreatePorscheIdModuleDocument,
  useCreatePorscheIdModuleMutation,
  CreatePayGatePaymentSettingsDocument,
  useCreatePayGatePaymentSettingsMutation,
  CreatePayGatePaymentModuleDocument,
  useCreatePayGatePaymentModuleMutation,
  CreatePackageSettingDocument,
  useCreatePackageSettingMutation,
  CreatePackageBlockDocument,
  useCreatePackageBlockMutation,
  CreateOptionsBlockDocument,
  useCreateOptionsBlockMutation,
  CreateOidcModuleDocument,
  useCreateOidcModuleMutation,
  CreateNewContactFromBpDocument,
  useCreateNewContactFromBpMutation,
  CreateNamirialSigningModuleDocument,
  useCreateNamirialSigningModuleMutation,
  CreateMyInfoSettingDocument,
  useCreateMyInfoSettingMutation,
  CreateMyInfoModuleDocument,
  useCreateMyInfoModuleMutation,
  CreateModelConfiguratorDocument,
  useCreateModelConfiguratorMutation,
  CreateModelDocument,
  useCreateModelMutation,
  CreateMobilityModuleDocument,
  useCreateMobilityModuleMutation,
  CreateMobilityApplicationEntrypointDocument,
  useCreateMobilityApplicationEntrypointMutation,
  CreateMobilityAddonDocument,
  useCreateMobilityAddonMutation,
  CreateMobilityAdditionalInfoDocument,
  useCreateMobilityAdditionalInfoMutation,
  CreateMarketingModuleDocument,
  useCreateMarketingModuleMutation,
  CreateMarketingConsentsAndDeclarationsDocument,
  useCreateMarketingConsentsAndDeclarationsMutation,
  CreateMakeDocument,
  useCreateMakeMutation,
  CreateMaintenanceModuleDocument,
  useCreateMaintenanceModuleMutation,
  CreateLocalCustomerManagementDocument,
  useCreateLocalCustomerManagementMutation,
  CreateLeadListEndpointDocument,
  useCreateLeadListEndpointMutation,
  CreateLeadFollowUpDocument,
  useCreateLeadFollowUpMutation,
  CreateLaunchPadModuleDocument,
  useCreateLaunchPadModuleMutation,
  CreateLaunchPadApplicationEntrypointDocument,
  useCreateLaunchPadApplicationEntrypointMutation,
  CreateLanguagePackDocument,
  useCreateLanguagePackMutation,
  CreateLabelsModuleDocument,
  useCreateLabelsModuleMutation,
  CreateLabelsDocument,
  useCreateLabelsMutation,
  CreateKycPresetDocument,
  useCreateKycPresetMutation,
  CreateInventoryDocument,
  useCreateInventoryMutation,
  CreateInsurerDocument,
  useCreateInsurerMutation,
  CreateInsuranceProductDocument,
  useCreateInsuranceProductMutation,
  CreateInsuranceModuleDocument,
  useCreateInsuranceModuleMutation,
  CreateGroupConsentsAndDeclarationsDocument,
  useCreateGroupConsentsAndDeclarationsMutation,
  CreateGiftVoucherModuleDocument,
  useCreateGiftVoucherModuleMutation,
  CreateFiservPaymentSettingsDocument,
  useCreateFiservPaymentSettingsMutation,
  CreateFiservPaymentModuleDocument,
  useCreateFiservPaymentModuleMutation,
  CreateFinderVehicleManagementModuleDocument,
  useCreateFinderVehicleManagementModuleMutation,
  CreateFinderApplicationPublicAccessEntrypointDocument,
  useCreateFinderApplicationPublicAccessEntrypointMutation,
  CreateFinderApplicationPrivateModuleDocument,
  useCreateFinderApplicationPrivateModuleMutation,
  CreateFinderApplicationPublicModuleDocument,
  useCreateFinderApplicationPublicModuleMutation,
  CreateFinderApplicationEntrypointDocument,
  useCreateFinderApplicationEntrypointMutation,
  CreateFinanceProductDocument,
  useCreateFinanceProductMutation,
  CreateEventApplicationModuleDocument,
  useCreateEventApplicationModuleMutation,
  CreateEventApplicationEntrypointDocument,
  useCreateEventApplicationEntrypointMutation,
  CreateEventDocument,
  useCreateEventMutation,
  CreateEdmEmailSocialMediaDocument,
  useCreateEdmEmailSocialMediaMutation,
  CreateDummyWelcomePageEndpointDocument,
  useCreateDummyWelcomePageEndpointMutation,
  CreateDummyPrivatePageEndpointDocument,
  useCreateDummyPrivatePageEndpointMutation,
  CreateDocusignSettingDocument,
  useCreateDocusignSettingMutation,
  CreateDocusignModuleDocument,
  useCreateDocusignModuleMutation,
  CreateDealerSocialMediaDocument,
  useCreateDealerSocialMediaMutation,
  CreateDealerDocument,
  useCreateDealerMutation,
  CreateCustomerListEndpointDocument,
  useCreateCustomerListEndpointMutation,
  CreateCtsModuleSettingDocument,
  useCreateCtsModuleSettingMutation,
  CreateCtsModuleDocument,
  useCreateCtsModuleMutation,
  CreateConsentsAndDeclarationsModuleDocument,
  useCreateConsentsAndDeclarationsModuleMutation,
  CreateConfiguratorModuleDocument,
  useCreateConfiguratorModuleMutation,
  CreateConfiguratorApplicationEntrypointDocument,
  useCreateConfiguratorApplicationEntrypointMutation,
  CreateCompanyDocument,
  useCreateCompanyMutation,
  CreateColorBlockDocument,
  useCreateColorBlockMutation,
  CreateCheckboxConsentsAndDeclarationsDocument,
  useCreateCheckboxConsentsAndDeclarationsMutation,
  CreateCapModuleDocument,
  useCreateCapModuleMutation,
  CreateCampaignDocument,
  useCreateCampaignMutation,
  CreateBasicSigningModuleDocument,
  useCreateBasicSigningModuleMutation,
  CreateBannerDocument,
  useCreateBannerMutation,
  CreateBankModuleDocument,
  useCreateBankModuleMutation,
  CreateBankDocument,
  useCreateBankMutation,
  CreateAutoplayModuleSettingDocument,
  useCreateAutoplayModuleSettingMutation,
  CreateAutoplayModuleDocument,
  useCreateAutoplayModuleMutation,
  CreateAppointmentModuleDocument,
  useCreateAppointmentModuleMutation,
  CreateAppointmentFromLeadDocument,
  useCreateAppointmentFromLeadMutation,
  CreateApplicationListEndpointDocument,
  useCreateApplicationListEndpointMutation,
  CreateAdyenPaymentSettingsDocument,
  useCreateAdyenPaymentSettingsMutation,
  CreateAdyenPaymentModuleDocument,
  useCreateAdyenPaymentModuleMutation,
  CreateAdditionalDetailDocument,
  useCreateAdditionalDetailMutation,
  CopyVariantConfiguratorDocument,
  useCopyVariantConfiguratorMutation,
  ContinueApplicationDocument,
  useContinueApplicationMutation,
  ContactApplicationDocument,
  useContactApplicationMutation,
  ConfirmLeadFollowUpDocument,
  useConfirmLeadFollowUpMutation,
  ConfirmBookingApplicationDocument,
  useConfirmBookingApplicationMutation,
  ConcludeAgreementApplicationDocument,
  useConcludeAgreementApplicationMutation,
  CompleteWebPublicKeyCredentialRegistrationDocument,
  useCompleteWebPublicKeyCredentialRegistrationMutation,
  CompleteLeadDocument,
  useCompleteLeadMutation,
  CompleteApplicationDocument,
  useCompleteApplicationMutation,
  CheckInApplicationDocument,
  useCheckInApplicationMutation,
  ChangePasswordFromTokenDocument,
  useChangePasswordFromTokenMutation,
  ChangePasswordFromAuthenticationDocument,
  useChangePasswordFromAuthenticationMutation,
  ChangePasswordDocument,
  useChangePasswordMutation,
  CancelLeadFollowUpDocument,
  useCancelLeadFollowUpMutation,
  CancelApplicationDocument,
  useCancelApplicationMutation,
  CalculateDocument,
  useCalculateMutation,
  AuthenticateWithWebAuthnDocument,
  useAuthenticateWithWebAuthnMutation,
  AuthenticateWithTotpDocument,
  useAuthenticateWithTotpMutation,
  AuthenticateWithSmsOtpDocument,
  useAuthenticateWithSmsOtpMutation,
  AuthenticateDocument,
  useAuthenticateMutation,
  AttachUsersToUserGroupDocument,
  useAttachUsersToUserGroupMutation,
  AttachUserOnRoleDocument,
  useAttachUserOnRoleMutation,
  AttachSuperiorGroupsToUserGroupDocument,
  useAttachSuperiorGroupsToUserGroupMutation,
  AttachPermissionOnRoleDocument,
  useAttachPermissionOnRoleMutation,
  AttachDealersToUserGroupDocument,
  useAttachDealersToUserGroupMutation,
  ApproveApplicationDocument,
  useApproveApplicationMutation,
  ApplyPorscheV3LayoutOnRouterDocument,
  useApplyPorscheV3LayoutOnRouterMutation,
  ApplyNewStandardApplicationDocument,
  useApplyNewStandardApplicationMutation,
  ApplyNewFinderApplicationDocument,
  useApplyNewFinderApplicationMutation,
  ApplyNewConfiguratorApplicationDocument,
  useApplyNewConfiguratorApplicationMutation,
  ApplyNewDocument,
  useApplyNewMutation,
  ApplyGiftVoucherDocument,
  useApplyGiftVoucherMutation,
  ApplyForPasswordChangeDocument,
  useApplyForPasswordChangeMutation,
  ApplyEmptyLayoutOnRouterDocument,
  useApplyEmptyLayoutOnRouterMutation,
  ApplyBasicProLayoutOnRouterDocument,
  useApplyBasicProLayoutOnRouterMutation,
  ApplyBasicLayoutOnRouterDocument,
  useApplyBasicLayoutOnRouterMutation,
  AmendMobilityApplicationDocument,
  useAmendMobilityApplicationMutation,
  AddTradeInAuditTrailCommentDocument,
  useAddTradeInAuditTrailCommentMutation,
  AddStockInventoryDocument,
  useAddStockInventoryMutation,
  AddInventoryAuditTrailCommentDocument,
  useAddInventoryAuditTrailCommentMutation,
  AddCustomerAuditTrailCommentDocument,
  useAddCustomerAuditTrailCommentMutation,
  AddAuditTrailCommentDocument,
  useAddAuditTrailCommentMutation,
  AuthorizeWithOidcDocument,
  useAuthorizeWithOidcMutation,
} from "./mutations/index";

export type {
  ListenOnSystemSubscriptionVariables,
  ListenOnSystemSubscription,
  ListenOnSystemSubscriptionHookResult,
  ListenOnSystemSubscriptionResult,
  ListenOnApplicationSubscriptionVariables,
  ListenOnApplicationSubscription,
  ListenOnApplicationSubscriptionHookResult,
  ListenOnApplicationSubscriptionResult,
} from "./subscriptions/index";

export {
  ListenOnSystemDocument,
  useListenOnSystemSubscription,
  ListenOnApplicationDocument,
  useListenOnApplicationSubscription,
} from "./subscriptions/index";
