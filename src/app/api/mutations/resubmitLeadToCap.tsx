import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ResubmitLeadToCapMutationVariables = SchemaTypes.Exact<{
  leadId: SchemaTypes.Scalars['ObjectID']['input'];
  capValues: SchemaTypes.CapValuesInput;
  resubmitQualifyValues?: SchemaTypes.InputMaybe<SchemaTypes.QualifyLeadInput>;
  isTestDrive: SchemaTypes.Scalars['Boolean']['input'];
  updatedKyc?: SchemaTypes.InputMaybe<Array<SchemaTypes.LocalCustomerFieldSettings> | SchemaTypes.LocalCustomerFieldSettings>;
  updatedTradeInVehicle?: SchemaTypes.InputMaybe<Array<SchemaTypes.TradeInVehiclePayload> | SchemaTypes.TradeInVehiclePayload>;
}>;


export type ResubmitLeadToCapMutation = (
  { __typename: 'Mutation' }
  & { result: SchemaTypes.Mutation['resubmitLeadToCap'] }
);


export const ResubmitLeadToCapDocument = /*#__PURE__*/ gql`
    mutation resubmitLeadToCap($leadId: ObjectID!, $capValues: CapValuesInput!, $resubmitQualifyValues: QualifyLeadInput, $isTestDrive: Boolean!, $updatedKyc: [LocalCustomerFieldSettings!], $updatedTradeInVehicle: [TradeInVehiclePayload!]) {
  result: resubmitLeadToCap(
    leadId: $leadId
    capValues: $capValues
    resubmitQualifyValues: $resubmitQualifyValues
    isTestDrive: $isTestDrive
    updatedKyc: $updatedKyc
    updatedTradeInVehicle: $updatedTradeInVehicle
  )
}
    `;
export type ResubmitLeadToCapMutationFn = Apollo.MutationFunction<ResubmitLeadToCapMutation, ResubmitLeadToCapMutationVariables>;

/**
 * __useResubmitLeadToCapMutation__
 *
 * To run a mutation, you first call `useResubmitLeadToCapMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useResubmitLeadToCapMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [resubmitLeadToCapMutation, { data, loading, error }] = useResubmitLeadToCapMutation({
 *   variables: {
 *      leadId: // value for 'leadId'
 *      capValues: // value for 'capValues'
 *      resubmitQualifyValues: // value for 'resubmitQualifyValues'
 *      isTestDrive: // value for 'isTestDrive'
 *      updatedKyc: // value for 'updatedKyc'
 *      updatedTradeInVehicle: // value for 'updatedTradeInVehicle'
 *   },
 * });
 */
export function useResubmitLeadToCapMutation(baseOptions?: Apollo.MutationHookOptions<ResubmitLeadToCapMutation, ResubmitLeadToCapMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ResubmitLeadToCapMutation, ResubmitLeadToCapMutationVariables>(ResubmitLeadToCapDocument, options);
      }
export type ResubmitLeadToCapMutationHookResult = ReturnType<typeof useResubmitLeadToCapMutation>;
export type ResubmitLeadToCapMutationResult = Apollo.MutationResult<ResubmitLeadToCapMutation>;
export type ResubmitLeadToCapMutationOptions = Apollo.BaseMutationOptions<ResubmitLeadToCapMutation, ResubmitLeadToCapMutationVariables>;