mutation resubmitLeadToCap(
    $leadId: ObjectID!
    $capValues: CapValuesInput!
    $resubmitQualifyValues: QualifyLeadInput
    $isTestDrive: Boolean!
    $updatedKyc: [LocalCustomerFieldSettings!]
    $updatedTradeInVehicle: [TradeInVehiclePayload!]
) {
    result: resubmitLeadToCap(
        leadId: $leadId
        capValues: $capValues
        resubmitQualifyValues: $resubmitQualifyValues
        isTestDrive: $isTestDrive
        updatedKyc: $updatedKyc
        updatedTradeInVehicle: $updatedTradeInVehicle
    )
}
