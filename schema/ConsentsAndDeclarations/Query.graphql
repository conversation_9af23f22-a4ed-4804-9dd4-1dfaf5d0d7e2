extend type Query {
    """
    Get a consent and declaration by its ID
    """
    getConsentsAndDeclarations(id: ObjectID!): ConsentsAndDeclarations

    """
    List Consents and Declarations
    """
    listConsentsAndDeclarations(
        pagination: Pagination
        sort: ConsentsAndDeclarationsSortingRule
        filter: ConsentsAndDeclarationsFilteringRule
    ): PaginatedConsentsAndDeclarations!

    """
    Prefetch agreements for event
    """
    prefetchAgreementsForEvent(
        eventId: ObjectID
        urlSlug: String
        configuration: EventApplicationConfigurationPayload!
        customerKind: String
        dealerId: ObjectID
        eventModuleId: ObjectID
    ): [ApplicationAgreement!]!

    """
    Prefetch agreements for launch pad
    """
    prefetchAgreementsForLaunchPad(
        applicationModuleId: ObjectID!
        configuration: LaunchPadApplicationConfiguration!
        customerKind: String
    ): [ApplicationAgreement!]!

    """
    Prefetch agreements for standard application
    """
    prefetchAgreementsForStandardApplication(
        applicationModuleId: ObjectID!
        configuration: StandardApplicationConfiguration!
        customerKind: String
    ): [ApplicationAgreement!]!

    """
    Prefetch agreements for Launch Pad Appointment
    """
    prefetchAgreementsForLaunchPadAppointment(applicationModuleId: ObjectID!): [ApplicationAgreement!]!

    """
    Prefetch agreements for Launch Pad Showroom Visit
    """
    prefetchAgreementsForLaunchPadShowroomVisit(applicationModuleId: ObjectID!): [ApplicationAgreement!]!

    """
    Get a condition entity by its ID and type
    """
    getConditionDetail(id: ObjectID!, kind: ConditionType!): ConditionDetail!
}
