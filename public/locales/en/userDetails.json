{"addTitle": "User", "title": "User - {{name}}", "cardTitles": {"configuration": "Configuration", "translations": "Translations", "permissionList": "User Permissions"}, "content": {"displayName": "Display Name", "username": "Username", "email": "Email", "mobile": "Mobile", "alias": "C@P ID"}, "permissionOrigin": {"fromRole": "Permission comes from this role(s):", "fromUser": "Permission attached to this user"}, "mainActionButtons": {"actions": "Actions", "updateSpecs": "Update information", "delete": "Delete"}, "fields": {"displayName": {"label": "Display name"}, "username": {"label": "Username"}, "email": {"label": "Email"}, "roles": {"label": "Roles"}, "userGroups": {"label": "User Groups"}, "mobile": {"label": "Mobile"}, "alias": {"label": "C@P ID"}, "isActive": {"label": "Active"}, "additionalCalendarEmails": {"label": "Additional Email Calendar Recipients", "tooltip": "Add email addresses to send calendar invites to additional recipients."}}, "actions": {"newConsentsAndDeclarations": "Add User", "create": "Create", "manage": "manage", "update": "Update", "delete": "Delete", "resendActivation": "Resend Activation"}, "messages": {"creationSubmitting": "Creating new user..", "creationSuccessful": "New user created", "updateSubmitting": "Updating user..", "updateSuccessful": "User updated", "deleteSubmitting": "Requesting user deletion..", "deleteSuccessful": "User deleted", "resendActivationSubmitting": "Resending activation email..", "resendActivationSuccessful": "Activation email has been sent."}, "permissions": {"noData": {"action": "Update user", "message": "This user has no permissions currently. Do you want to update this user?"}}, "deleteUser": {"message": "Do you confirm the deletion for this user? This action cannot be reverted.", "okText": "Confirm & Delete", "cancelText": "Cancel", "messages": {"submitting": "Requesting user deletion..", "success": "User deleted", "failed": "Unable to delete this user"}, "assigned": {"applications": "Applications Assigned:", "modules": "<PERSON><PERSON>les Assigned:", "events": "Lead C@Pture Forms Assigned:"}, "assignTo": "Assign to", "assignToPlaceholder": "Select a user"}}