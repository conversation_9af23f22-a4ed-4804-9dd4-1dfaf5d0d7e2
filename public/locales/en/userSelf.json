{"title": "Profile", "subTitle": "", "myProfile": {"title": "My Profile"}, "editMyProfile": {"title": "Edit My Profile", "changeButton": "Change"}, "general": {"title": "General", "subTitle": "Main Details", "fields": {"displayName": {"label": "Name"}, "email": {"label": "Email"}, "mobile": {"label": "Mobile No."}, "profileImage": {"label": "Profile Image"}, "alias": {"label": "C@P ID"}, "additionalCalendarEmails": {"label": "Additional Email Calendar Recipients", "tooltip": "Add email addresses to send calendar invites to additional recipients."}}, "messages": {"updateSubmitting": "Updating profile..", "updateSuccessful": "Profile updated", "updateEmailSuccessful": "An email has been sent to your new email address. Please click on the verification link to activate the changes"}, "submitButton": "Save", "cancelButton": "Cancel"}, "authenticatorSettings": {"title": "Authenticator (2FA)", "badge": {"enabled": "enabled", "disabled": "not yet enabled"}, "introduction": {"text": "Two-factor authentication (2FA) is the best way to protect yourself online. It adds an additional layer of protection beyond passwords.", "submitButton": "Configure"}, "setup": {"service": "Appvantage Starter-Kit", "disableConfirm": {"title": "Disable Authenticator (2FA)", "description": "Do you confirm removing the current authenticator from your account ? Once removed, your account may be at risk."}, "enabledSuccessMessage": {"title": "Authenticator (2FA)", "description": "Authenticator is now enabled on your account"}, "disabledSuccessMessage": {"title": "Authenticator (2FA)", "description": "Authenti<PERSON><PERSON> is now disabled, be aware your account is at risk"}, "qrcodeStep": {"title": "<PERSON><PERSON>", "introduction": "Start by scanning the following QRCode with your authenticator.", "cancelButton": "Previous", "submitButton": "Next"}, "codeStep": {"title": "Verification", "label": "Code", "introduction": "Input the code generated by your authenticator in it's 30s validity below.", "cancelButton": "Previous", "submitButton": "Verify"}}, "summary": {"introduction": "Authenticator (2FA) is already enabled on this account.", "disableButton": "Disable"}, "fields": {"code": {"placeholder": "Enter code here...", "label": "Input Code"}}}, "passwordSettings": {"title": "Password", "badge": {"expired": "expired", "nearExpiration": "near expiration", "valid": "valid for {{days}} days"}, "introductionText": "Your password will expire on the {{date}}, you may change it ahead of time. Once your password is expired, you will be asked to change it on your next sign in.", "changeButton": "Set new password", "submitButton": "Update", "cancelButton": "cancel", "fields": {"previousPassword": {"label": "Current Password", "placeholder": "Enter here..."}, "newPassword": {"label": "New Password", "placeholder": "Enter here..."}, "newPasswordRepeat": {"label": "Retype New Password", "placeholder": "Enter here..."}}, "successMessage": {"title": "Password updated", "description": "Your password has been updated and is valid for 90 days"}, "requirements": {"title": "Password Requirements", "error": {"atLeastUpperCase": "At least {{min}} upper case letter {{range}}", "atLeastLowerCase": "At least {{min}} lower case letter {{range}}", "atLeastNumber": "At least {{min}} numeral {{range}}", "atLeastSpecialCharacter": "At least {{min}} special character {{range}}", "atLeastCharacter": "At least {{size}} characters", "atLeastIdenticalCharInRow": "No more than {{max}} identical characters in a row"}, "strongPassword": "Strong password", "strongPasswordTooltip": "Use a mix of uppercase and lowercase letters, numbers, and special characters. Avoid common words or personal info. Ensure your password is at least 14 characters long."}}, "webAuthnSettings": {"title": "Security Keys (WebAuthn)", "createButton": "Setup a new key", "revokeButton": "Revoke", "introductionText": "You may use security keys with <webauthn>WebAuthn</webauthn> as an alternative way to authenticate from your devices without using your password.", "table": {"activeTooltip": "Active key on this device", "titles": {"expiresAt": "Expires at", "userAgent": "User-Agent", "lastUsed": "Latest activity", "actions": "Actions"}}, "successMessage": {"title": "Security Key created", "description": "Your account is now configured with a new security key for this device"}, "deleteMessage": {"title": "Security Key revoked", "description": "Your security key has been revoked and cannot be used anymore"}, "revokeModal": {"title": "Revoking security key", "content": "Do you confirm your action to revoke the security key ?", "okText": "Continue", "cancelText": "Cancel"}}, "sessions": {"title": "Active Sessions", "introductionText": "The following table lists all active sessions for this account. You may revoke suspicious active sessions remotely.", "revokeButton": "Revoke", "table": {"titles": {"userAgent": "User-Agent", "ip": "IP", "country": "Country", "lastActivityAt": "Latest activity", "actions": "Actions"}}, "deleteMessage": {"title": "Session revoked", "description": "The session has been revoked and will not be allowed accesses anymore"}, "revokeModal": {"title": "Revoke session", "content": "Do you confirm your action to revoke the session ?", "okText": "Continue", "cancelText": "Cancel"}}}