{"invalidCredentials": "Invalid credentials", "cantSameAsEmail": "Cannot be the same as email", "cantSameAsEmailAddress": "Cannot be the same as email address (before @)", "weakPassword": "Choose a password that's harder for people to guess", "duplicateEmail": "Email already taken", "existingEmail": "A user with the same email already exists.", "reusedPassword": "Enter a password that you haven't used before", "invalidTOTP": "Invalid OTP", "invalidValue": "Unexpected value", "duplicatedIdentifier": "Identifier already in used", "duplicatedOrder": "Order already in used", "notDeleteable": "Unable to delete vehicle type", "invalidModel": "Invalid model", "invalidMake": "Invalid make", "financeProduct": {"bankId": {"notFound": "Bank not found", "canNotUpdate": "Bank should not update"}, "interestOnly": {"required": "Interest only is required"}, "downPayment": {"required": "DownPayment is required", "incompatibleWithLoan": "DownPayment is incompatible with Loan"}, "loan": {"required": "Loan is required"}, "paymentMode": {"required": "Payment Mode is required"}, "interestRate": {"required": "Interest Rate is required"}, "deposit": {"required": "Deposit is required"}, "basedOn": {"required": "Based On is required"}, "lease": {"required": "Lease is required"}, "licensePlateFee": {"required": "License Plate Fee is required"}, "commission": {"required": "Commission is required"}, "monthlyPaymentFixedInterestRate": {"required": "Monthly Payment Fixed Interest Rate is required"}, "displacement": {"required": "Displacement is required"}, "licenseAndFuelTax": {"required": "License And Fuel Tax is required"}, "insuranceFee": {"required": "Insurance Fee is required"}, "taxLoss": {"required": "Tax Loss is required"}, "balloonGfv": {"required": "Balloon GFV is required"}, "reducedMonthlyPayment": {"required": "Reduced Monthly Payment is required"}, "settlementInstalmentOn": {"required": "Settlement Instalment On is required"}, "invalidType": "Invalid Finance Product Type", "general": {"invalidStep": "Invalid step value", "invalidMin": "Invalid min value", "invalidMax": "Invalid max value", "invalidDefault": "Invalid default value", "invalidUnit": "Invalid unit value"}, "reducedMonthlyPaymentRate": {"required": "Reduced Monthly Payment Rate required"}, "assuredResaleValue": {"required": "Assured Resale Value required"}, "estimatedSurplusBalloon": {"required": "Estimated Surplus After Assured Resale GFV required"}}, "eventModuleNotFound": "Event Module not found", "paymentModuleNotSelected": "Payment Module is not selected", "noResponsibleSalesPerson": "There must be a responsible sales person for Public Event", "CompanyNotFound": "Company not Found", "invalidUserGroups": "Invalid User Group ID", "invalidParentUserGroups": "Invalid Parent User Group ID", "invalidOTP": "Invalid OTP code", "expiredOTP": "OTP code has expired", "invalidOrExpiredOTP": "Invalid or expired OTP code", "otpCooldown": "Please wait before requesting a new code", "tooManyAttempts": "Too many verification attempts, please try again later", "tooManyRequests": "Too many requests, please try again later", "failedToSendSMS": "Failed to send SMS, please try again later", "invalidPhone": "Invalid phone number", "otpVerificationPending": "OTP Verification Pending", "invalidSession": "Invalid Session", "userNotFound": "User Not Found", "userAssigned": "User is currently assigned to existing resources.", "duplicateIdentifier": "Duplicate Identifier", "promoCode": {"invalid": {"title": "The Promo Code is not valid", "description": "Invalid"}, "notAvailable": {"title": "The Promo Code is no longer available", "description": "No longer available"}}, "giftVoucher": {"invalid": {"title": "The Gift Voucher is not valid", "description": "Invalid"}, "notAvailable": {"title": "The Promo Code is no longer available", "description": "No longer available"}}, "giftVoucherAndPromoCode": {"invalid": {"title": "The code is not valid", "description": "Invalid"}}, "moduleNotSupported": "<PERSON><PERSON><PERSON> not supported", "invalidModule": "Invalid module", "invalidInventory": "Invalid inventory ID", "invalidCustomer": "Invalid customer ID", "invalidStockInventory": "Invalid stock inventory ID", "unableToDeleteStock": "Unable to Delete as some stocks are in use", "reservationStatusNotSupported": "Reservation status not supported", "inventoryStockIsNotZero": "Inventory stock for this variant configurator id is not zero", "reservedStockFound": "This variant cannot be deleted as there are reserved stock.", "invalidMenuItem": "Invalid Menu Item inputs", "emptyIdentity": "Empty Identity Number", "invalidIdentity": "Invalid Identity Number", "invalidPromoCodeModule": "Invalid Promo Code Module ID", "cannotDeleteModule": "Unable to delete as this module is currently in use by {{ moduleData }}.", "cannotDeleteSetting": "Unable to delete as this setting is currently in use by {{ settingData }}.", "invalidRouter": "Invalid Router ID", "invalidEndpoint": "Invalid Endpoint ID", "cannotDeleteRouter": "Unable to delete as there is no replacement endpoints for this router. Endpoints without replacement: {{ endpointDetails }}.", "cannotDeleteEndpoint": "Unable to delete as there is no replacement endpoint for this endpoint.", "cannotDeleteLanguagePack": "Unable to delete as this language pack is currently in use by {{ languagePackData }}.", "mobilityApplication": {"mobilityNotFound": "Mobility not found", "mobilityAddonRequired": "Mobility addon information required", "inventoryNotFound": "Inventory not found", "periodNotMutable": "Period cannot be updated", "periodNotAvailable": "The specified period has already been booked, please check for other available ones.", "periodInvalid": "End Time should not same or lesser/lower than Start Time", "timeInvalid": "{{time}} is fall in between non-operating hours", "dayInvalid": "{{day}} in non-operating day", "bookingCodeRequired": "Booking Code is required", "bookingCodeInvalid": "Booking Code is invalid"}, "mobilityInventory": {"bookingsOutOfInventoryPeriod": "Some existing bookings are not within specified period."}, "invalidEvent": "Invalid event", "role": {"duplicateDisplayName": "Display name already exists", "invalidRoles": "Invalid Role ID"}, "company": {"marketField": "Market field is required"}, "moduleInUse": {"label": "Unable to delete, module has CTS Settings"}, "settingInUse": {"label": "Unable to delete, CTS Setting attached in some applications"}, "invalidTradeIn": "Invalid Trade-In", "insurancing": {"dateOfBirth": "Date of Birth is required", "dateOfRegistration": "Date of Registration is required", "totalPrice": "Total Price is required", "ncd": "No Claim Discount is required", "yearsOfDriving": "Year of Driving is required", "insuranceProductId": "Insurance Product is required"}, "addressAutocomplete": {"invalidQuery": "Search query is required and must be at least 2 characters", "invalidLimit": "Limit must be at least 1", "invalidProximity": "Proximity coordinates must be valid [longitude, latitude] values"}, "salesControlBoard": {"missingFile": {"label": "Missing file for upload"}, "importDataAlreadyExists": {"label": "There are existing data for the month of {{month}}"}}, "consentsAndDeclarations": {"invalidModuleId": "Invalid Module ID", "invalidConsentId": "Invalid Consent ID", "invalidConditionSettings": "Invalid Condition Settings", "groupChildrenMustBeCheckbox": "All children of a group must be of checkbox type", "checkboxSettingsRequired": "Checkbox settings are required for all group children", "notGroupConsent": "Not a group consent"}, "campaign": {"notFound": "Campaign not found", "campaignInUsedByModule": "{{ campaignId }} is currently in use by {{ moduleName }} module.", "campaignInUsedByEvent": "{{ campaignId }} is currently in use by {{ eventName }} event."}}